# 生产环境配置示例
# 复制此文件为 .env 并修改相应的值

# ===========================================
# 应用基本配置
# ===========================================
SPRING_PROFILES_ACTIVE=prod
SERVER_PORT=8081

# ===========================================
# Redis配置
# ===========================================
# Redis服务器地址
REDIS_HOST=localhost
# Redis端口
REDIS_PORT=6379
# Redis密码（强烈建议在生产环境设置）
REDIS_PASSWORD=your_strong_redis_password_here
# Redis数据库编号
REDIS_DATABASE=0

# Redis集群配置（如果使用集群，取消注释）
# REDIS_CLUSTER_NODES=*************:7000,*************:7001,*************:7002

# Redis哨兵配置（如果使用哨兵，取消注释）
# REDIS_SENTINEL_MASTER=mymaster
# REDIS_SENTINEL_NODES=*************:26379,*************:26379,*************:26379

# ===========================================
# 数据库配置（如果使用MySQL）
# ===========================================
DB_HOST=localhost
DB_PORT=3306
DB_NAME=rtpos_prod
DB_USERNAME=rtpos_user
DB_PASSWORD=your_strong_db_password_here

# ===========================================
# JVM配置
# ===========================================
# JVM内存和垃圾回收配置
JAVA_OPTS=-Xms512m -Xmx2g -XX:+UseG1GC -XX:+UseStringDeduplication

# ===========================================
# 外部API配置
# ===========================================
# POS订单API配置
POS_ORDER_API_BASE_URL=http://middle-order-biz.idc1.fn
POS_ORDER_API_PATH=/api/queryOrder/querySelfPosNodeOrders

# POS收银日志API配置
POS_OPERATION_LOG_API_BASE_URL=http://rt-pos-api.idc1.fn
POS_OPERATION_LOG_API_PATH=/api/pos/getOperationLogInfo

# ===========================================
# 同步配置
# ===========================================
# 门店配置
POS_STORE_IDS=1001,1002,1003,1004,1005
POS_STORE_NOS=1001,1002,1003,1004,1005
POS_NOS=895,896,897,898,899

# 同步批次大小
POS_SYNC_BATCH_SIZE=200
POS_OPERATION_LOG_SYNC_BATCH_SIZE=20

# ===========================================
# 安全配置
# ===========================================
# 应用安全密钥（用于JWT等）
APP_SECRET_KEY=your_app_secret_key_here

# 管理员账户
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_admin_password_here

# ===========================================
# 监控配置
# ===========================================
# 是否启用监控端点
MANAGEMENT_ENDPOINTS_ENABLED=true

# 监控端点访问端口（可选，默认与应用端口相同）
MANAGEMENT_SERVER_PORT=8082

# ===========================================
# 日志配置
# ===========================================
# 日志级别
LOGGING_LEVEL_ROOT=INFO
LOGGING_LEVEL_APP=INFO

# 日志文件路径
LOGGING_FILE_NAME=logs/rtpos-server-prod.log

# ===========================================
# 使用说明
# ===========================================
# 1. 复制此文件为 .env
# 2. 修改上述配置值
# 3. 使用以下命令启动应用：
#    source .env && ./start-prod.sh
#
# 或者直接设置环境变量：
#    export REDIS_HOST=*************
#    export REDIS_PASSWORD=mypassword
#    ./start-prod.sh
