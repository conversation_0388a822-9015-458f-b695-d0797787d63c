#!/bin/bash

# 本地环境检查脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}    本地开发环境配置检查${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# 检查Java
echo -e "${YELLOW}🔍 检查Java...${NC}"
if command -v java &> /dev/null; then
    java_version=$(java -version 2>&1 | head -n 1)
    echo -e "${GREEN}✅ Java已安装${NC}"
    echo "    $java_version"
else
    echo -e "${RED}❌ Java未安装${NC}"
    exit 1
fi

echo ""

# 检查Maven
echo -e "${YELLOW}🔍 检查Maven...${NC}"
if [ -f "./mvnw" ]; then
    echo -e "${GREEN}✅ Maven包装器存在${NC}"
    ./mvnw --version | head -n 1
else
    echo -e "${RED}❌ Maven包装器不存在${NC}"
    exit 1
fi

echo ""

# 检查MySQL
echo -e "${YELLOW}🔍 检查MySQL...${NC}"
if command -v mysql &> /dev/null; then
    echo -e "${GREEN}✅ MySQL客户端已安装${NC}"
    mysql --version
    
    # 检查MySQL服务
    if mysqladmin ping -h localhost --silent 2>/dev/null; then
        echo -e "${GREEN}✅ MySQL服务正在运行${NC}"
        
        # 检查端口
        if lsof -i :3306 > /dev/null 2>&1; then
            echo -e "${GREEN}✅ MySQL端口3306正在监听${NC}"
        else
            echo -e "${YELLOW}⚠️  MySQL端口3306未监听${NC}"
        fi
    else
        echo -e "${RED}❌ MySQL服务未运行${NC}"
        echo -e "${YELLOW}启动命令：${NC}"
        echo -e "  macOS: brew services start mysql"
        echo -e "  Linux: sudo systemctl start mysql"
    fi
else
    echo -e "${RED}❌ MySQL未安装${NC}"
    echo -e "${YELLOW}安装命令：${NC}"
    echo -e "  macOS: brew install mysql"
    echo -e "  Linux: sudo apt-get install mysql-server"
fi

echo ""

# 检查Redis（可选）
echo -e "${YELLOW}🔍 检查Redis（可选）...${NC}"
if command -v redis-cli &> /dev/null; then
    echo -e "${GREEN}✅ Redis客户端已安装${NC}"
    redis-cli --version
    
    if redis-cli ping > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Redis服务正在运行${NC}"
    else
        echo -e "${YELLOW}⚠️  Redis服务未运行（可选）${NC}"
        echo -e "${BLUE}启动命令：${NC}"
        echo -e "  macOS: brew services start redis"
        echo -e "  Linux: sudo systemctl start redis"
    fi
else
    echo -e "${YELLOW}⚠️  Redis未安装（可选）${NC}"
    echo -e "${BLUE}安装命令：${NC}"
    echo -e "  macOS: brew install redis"
    echo -e "  Linux: sudo apt-get install redis-server"
fi

echo ""

# 检查配置文件
echo -e "${YELLOW}🔍 检查配置文件...${NC}"

files=(
    "src/main/resources/application-dev.yml"
    "start-dev-local.sh"
    "db-manage-local.sh"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo -e "${GREEN}✅ $file${NC}"
    else
        echo -e "${RED}❌ $file 不存在${NC}"
    fi
done

echo ""

# 检查端口占用
echo -e "${YELLOW}🔍 检查端口占用...${NC}"

ports=(3306 6379 8081)
port_names=("MySQL" "Redis" "应用")

for i in "${!ports[@]}"; do
    port=${ports[$i]}
    name=${port_names[$i]}
    
    if lsof -i :$port > /dev/null 2>&1; then
        if [ "$port" = "3306" ] || [ "$port" = "6379" ]; then
            echo -e "${GREEN}✅ 端口 $port ($name) 正在使用${NC}"
        else
            echo -e "${YELLOW}⚠️  端口 $port ($name) 已被占用${NC}"
            lsof -i :$port | head -n 2
        fi
    else
        if [ "$port" = "3306" ]; then
            echo -e "${RED}❌ 端口 $port ($name) 未监听 - MySQL可能未启动${NC}"
        else
            echo -e "${GREEN}✅ 端口 $port ($name) 可用${NC}"
        fi
    fi
done

echo ""

# 检查数据库连接（如果MySQL运行）
if mysqladmin ping -h localhost --silent 2>/dev/null; then
    echo -e "${YELLOW}🔍 检查数据库连接...${NC}"
    echo -e "${BLUE}请输入MySQL root密码（测试连接）：${NC}"
    read -s mysql_password
    echo ""
    
    if mysql -uroot -p"$mysql_password" -e "SELECT 1;" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 数据库连接成功${NC}"
        
        # 检查开发数据库是否存在
        if mysql -uroot -p"$mysql_password" -e "USE rtpos_dev; SELECT 1;" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ 开发数据库 rtpos_dev 已存在${NC}"
        else
            echo -e "${YELLOW}⚠️  开发数据库 rtpos_dev 不存在${NC}"
            echo -e "${BLUE}运行 ./db-manage-local.sh 创建数据库${NC}"
        fi
    else
        echo -e "${RED}❌ 数据库连接失败，请检查密码${NC}"
    fi
fi

echo ""
echo -e "${BLUE}========================================${NC}"
echo -e "${GREEN}🎉 环境检查完成！${NC}"
echo ""
echo -e "${BLUE}下一步操作：${NC}"
echo -e "  1. 确保MySQL服务正在运行"
echo -e "  2. 运行 ${YELLOW}./start-dev-local.sh${NC} 启动开发环境"
echo -e "  3. 访问 ${YELLOW}http://localhost:8081/api/v1/swagger-ui.html${NC} 查看API文档"
echo -e "  4. 使用 ${YELLOW}./db-manage-local.sh${NC} 管理数据库"
echo ""
