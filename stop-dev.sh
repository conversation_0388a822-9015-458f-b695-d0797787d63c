#!/bin/bash

# 开发环境停止脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}    停止RTPosServer开发环境${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker未运行${NC}"
    exit 1
fi

# 检查是否存在docker-compose.dev.yml
if [ ! -f "docker-compose.dev.yml" ]; then
    echo -e "${RED}❌ 找不到docker-compose.dev.yml文件${NC}"
    exit 1
fi

echo -e "${YELLOW}🛑 停止开发环境服务...${NC}"

# 停止所有开发环境服务
docker-compose -f docker-compose.dev.yml down

echo -e "${GREEN}✅ 开发环境服务已停止${NC}"
echo ""
echo -e "${YELLOW}💡 提示:${NC}"
echo -e "  - 数据已保存在Docker卷中"
echo -e "  - 下次启动时数据会自动恢复"
echo -e "  - 如需完全清理数据，请运行: docker-compose -f docker-compose.dev.yml down -v"
echo ""

# 显示当前运行的容器
echo -e "${BLUE}当前运行的Docker容器:${NC}"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
