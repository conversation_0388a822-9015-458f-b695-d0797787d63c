#!/bin/bash

# 开发环境启动脚本
# 使用本地MySQL数据库

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}    RTPosServer 开发环境启动脚本${NC}"
echo -e "${BLUE}    使用本地MySQL数据库${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker未运行，请先启动Docker${NC}"
    exit 1
fi

# 检查是否存在docker-compose.dev.yml
if [ ! -f "docker-compose.dev.yml" ]; then
    echo -e "${RED}❌ 找不到docker-compose.dev.yml文件${NC}"
    exit 1
fi

echo -e "${YELLOW}🔧 启动开发环境数据库...${NC}"

# 启动MySQL和Redis
docker-compose -f docker-compose.dev.yml up -d mysql-dev redis-dev

# 等待MySQL启动完成
echo -e "${YELLOW}⏳ 等待MySQL数据库启动完成...${NC}"
timeout=60
counter=0
while ! docker exec rtpos-mysql-dev mysqladmin ping -h localhost -uroot -ppassword --silent; do
    sleep 2
    counter=$((counter + 2))
    if [ $counter -ge $timeout ]; then
        echo -e "${RED}❌ MySQL启动超时${NC}"
        exit 1
    fi
    echo -n "."
done
echo ""
echo -e "${GREEN}✅ MySQL数据库启动成功${NC}"

# 等待Redis启动完成
echo -e "${YELLOW}⏳ 等待Redis启动完成...${NC}"
timeout=30
counter=0
while ! docker exec rtpos-redis-dev redis-cli ping > /dev/null 2>&1; do
    sleep 1
    counter=$((counter + 1))
    if [ $counter -ge $timeout ]; then
        echo -e "${RED}❌ Redis启动超时${NC}"
        exit 1
    fi
    echo -n "."
done
echo ""
echo -e "${GREEN}✅ Redis启动成功${NC}"

echo ""
echo -e "${GREEN}🎉 开发环境数据库启动完成！${NC}"
echo ""
echo -e "${BLUE}数据库连接信息:${NC}"
echo -e "  📊 MySQL:"
echo -e "    - 主机: localhost:3306"
echo -e "    - 数据库: rtpos_dev"
echo -e "    - 用户名: root"
echo -e "    - 密码: password"
echo -e "    - 开发用户: rtpos_dev / rtpos_dev123"
echo ""
echo -e "  🗄️ Redis:"
echo -e "    - 主机: localhost:6379"
echo -e "    - 数据库: 1"
echo ""
echo -e "  🌐 phpMyAdmin (数据库管理):"
echo -e "    - 访问地址: http://localhost:8083"
echo -e "    - 用户名: root"
echo -e "    - 密码: password"
echo ""

# 检查Maven包装器
if [ ! -f "./mvnw" ]; then
    echo -e "${RED}❌ 找不到Maven包装器，请确保在项目根目录运行此脚本${NC}"
    exit 1
fi

echo -e "${YELLOW}🔨 编译项目...${NC}"
./mvnw clean compile

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 编译成功${NC}"
    echo ""
    echo -e "${YELLOW}🚀 启动应用...${NC}"
    echo ""
    echo -e "${BLUE}应用访问地址:${NC}"
    echo -e "  - API文档: http://localhost:8081/api/v1/swagger-ui.html"
    echo -e "  - 健康检查: http://localhost:8081/api/v1/actuator/health"
    echo -e "  - API基础路径: http://localhost:8081/api/v1"
    echo ""
    echo -e "${BLUE}默认认证信息:${NC}"
    echo -e "  - 用户名: admin"
    echo -e "  - 密码: admin123"
    echo ""
    echo -e "${YELLOW}💡 提示:${NC}"
    echo -e "  - 数据会持久化保存在MySQL中"
    echo -e "  - 可以通过phpMyAdmin管理数据库"
    echo -e "  - 按 Ctrl+C 停止应用（数据库会继续运行）"
    echo -e "  - 运行 './stop-dev.sh' 停止所有服务"
    echo ""
    echo -e "${BLUE}========================================${NC}"

    # 启动应用
    ./mvnw spring-boot:run -Dspring-boot.run.profiles=dev
else
    echo -e "${RED}❌ 编译失败，请检查错误信息${NC}"
    exit 1
fi
