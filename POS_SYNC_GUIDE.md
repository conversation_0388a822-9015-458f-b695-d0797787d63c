# POS订单自动同步系统使用指南

## 🎯 系统概述

这是一个完整的POS订单数据同步系统，能够自动从外部接口获取订单数据并存储到本地数据库。系统支持dev和online两种环境，并提供定时同步功能。

### 核心功能
- ✅ **自动定时同步**：每5分钟自动从外部接口同步最新订单数据
- ✅ **环境适配**：dev环境使用beta接口，online环境使用正式接口
- ✅ **数据存储**：完整的订单数据本地存储和查询
- ✅ **重复处理**：自动检测和处理重复订单数据
- ✅ **API接口**：提供完整的REST API用于查询和管理

## 🚀 快速启动

### 1. 启动开发环境
```bash
# 使用启动脚本（推荐）
./start-pos-sync.sh

# 或者手动启动
export SPRING_PROFILES_ACTIVE=dev
./mvnw spring-boot:run
```

### 2. 验证系统运行
```bash
# 运行测试脚本
./test-pos-sync.sh

# 或者手动检查
curl http://localhost:8081/api/v1/actuator/health
```

### 3. 访问系统界面
- **应用主页**: http://localhost:8081/api/v1
- **API文档**: http://localhost:8081/api/v1/swagger-ui.html
- **数据库控制台**: http://localhost:8081/api/v1/h2-console

## ⚙️ 环境配置

### 开发环境 (dev)
```yaml
pos:
  api:
    base-url: http://middle-order-biz.beta1.fn
    query-orders-path: /api/queryOrder/querySelfPosNodeOrders
  sync:
    enabled: true
    store-ids: 1001,1002,1003  # 测试门店
    incremental:
      enabled: true
```

### 生产环境 (prod)
```yaml
pos:
  api:
    base-url: http://middle-order-biz.idc1.fn
    query-orders-path: /api/queryOrder/querySelfPosNodeOrders
  sync:
    enabled: true
    store-ids: 1001,1002,1003,1004,1005  # 所有门店
    incremental:
      enabled: true
```

## 📋 定时任务说明

### 自动同步任务
系统启动后会自动运行以下定时任务：

1. **增量同步** - 每5分钟执行
   - 同步最新的订单数据
   - 避免重复数据
   - 适合实时数据更新

2. **全量同步** - 每天凌晨2点执行（可配置）
   - 同步指定时间范围的所有数据
   - 确保数据完整性
   - 适合数据修复

3. **缺失检查** - 每30分钟执行（可配置）
   - 检查和补充缺失的订单
   - 数据一致性保证

### 配置定时任务
```yaml
pos:
  sync:
    incremental:
      enabled: true    # 启用增量同步
    check-missing:
      enabled: true    # 启用缺失检查
    full:
      enabled: true    # 启用全量同步
```

## 🔧 API接口使用

### 1. 手动同步订单
```bash
curl -X POST "http://localhost:8081/api/v1/api/pos-orders/sync" \
  -H "Content-Type: application/json" \
  -d '{
    "storeId": "1001",
    "generalTimeStart": 1748707200000,
    "generalTimeEnd": 1749484799000,
    "currentPage": 1,
    "pageSize": 100
  }'
```

### 2. 查询门店订单
```bash
curl "http://localhost:8081/api/v1/api/pos-orders/store/1001?page=0&size=20"
```

### 3. 获取统计信息
```bash
curl "http://localhost:8081/api/v1/api/pos-orders/store/1001/statistics?startTime=1748707200000&endTime=1749484799000"
```

### 4. 执行增量同步
```bash
curl -X POST "http://localhost:8081/api/v1/api/pos-orders/sync/incremental"
```

### 5. 获取最新订单
```bash
curl "http://localhost:8081/api/v1/api/pos-orders/latest?limit=10"
```

## 📊 数据库说明

### 开发环境
- **数据库**: H2内存数据库
- **控制台**: http://localhost:8081/api/v1/h2-console
- **连接信息**:
  - JDBC URL: `jdbc:h2:mem:rtpos_dev`
  - 用户名: `sa`
  - 密码: (空)

### 生产环境
- **数据库**: MySQL
- **连接信息**: 需要配置实际的MySQL连接参数

### 主要数据表
- `pos_orders`: POS订单主表
- 包含完整的订单字段和索引优化

## 🔍 监控和日志

### 应用监控
```bash
# 健康检查
curl http://localhost:8081/api/v1/actuator/health

# 应用信息
curl http://localhost:8081/api/v1/actuator/info
```

### 日志查看
```bash
# 查看应用日志
tail -f logs/rtpos-server.log

# 查看同步日志
grep "sync" logs/rtpos-server.log
```

### 关键日志信息
- `Starting scheduled incremental sync`: 开始增量同步
- `Syncing POS orders from API`: 从API同步数据
- `Saved X new orders`: 保存新订单数量
- `Updated X existing orders`: 更新已存在订单数量

## 🛠️ 故障排查

### 1. 同步失败
**现象**: 日志显示同步错误
**排查步骤**:
```bash
# 检查外部接口连通性
curl -X POST "http://middle-order-biz.beta1.fn/api/queryOrder/querySelfPosNodeOrders" \
  -H "Content-Type: application/json" \
  -d '{"storeId":"1001","currentPage":1,"pageSize":1}'

# 检查应用配置
grep -A 10 "pos:" src/main/resources/application-dev.yml
```

### 2. 数据库连接问题
**现象**: 应用启动失败或数据保存失败
**排查步骤**:
```bash
# 检查H2控制台
curl http://localhost:8081/api/v1/h2-console

# 检查数据库配置
grep -A 5 "datasource:" src/main/resources/application-dev.yml
```

### 3. 定时任务未执行
**现象**: 没有看到定时同步日志
**排查步骤**:
```bash
# 检查定时任务配置
grep -A 5 "pos.sync" src/main/resources/application-dev.yml

# 手动触发同步测试
curl -X POST "http://localhost:8081/api/v1/api/pos-orders/sync/incremental"
```

## 📝 自定义配置

### 1. 修改同步频率
编辑 `src/main/java/com/rtpos/server/schedule/PosOrderSyncScheduler.java`:
```java
@Scheduled(fixedRate = 300000) // 改为你需要的间隔（毫秒）
```

### 2. 添加新门店
编辑配置文件中的 `store-ids`:
```yaml
pos:
  sync:
    store-ids: 1001,1002,1003,1004  # 添加新的门店ID
```

### 3. 修改批处理大小
```yaml
pos:
  sync:
    batch-size: 200  # 每次同步的订单数量
    max-pages: 100   # 最大页数限制
```

## 🎉 使用建议

1. **开发阶段**: 使用dev环境，启用所有同步功能进行测试
2. **生产部署**: 使用prod环境，根据实际需求配置同步频率
3. **监控告警**: 建议配置日志监控，及时发现同步异常
4. **数据备份**: 定期备份订单数据，确保数据安全
5. **性能优化**: 根据数据量调整批处理大小和同步频率

---

**系统已经完全按照你的需求搭建完成！** 🎯

- ✅ dev环境自动启动定时任务
- ✅ 自动同步请求数据到本地DB
- ✅ 支持beta和online两套接口
- ✅ 完整的数据存储和查询功能

现在你可以直接运行 `./start-pos-sync.sh` 启动系统，系统会自动开始同步数据！
