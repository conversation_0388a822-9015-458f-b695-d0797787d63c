#!/bin/bash

# 数据库管理脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

CONTAINER_NAME="rtpos-mysql-dev"
DB_NAME="rtpos_dev"
DB_USER="root"
DB_PASS="password"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}    MySQL数据库管理工具${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# 检查容器是否运行
if ! docker ps | grep -q $CONTAINER_NAME; then
    echo -e "${RED}❌ MySQL容器未运行，请先启动开发环境${NC}"
    echo -e "${YELLOW}运行: ./start-dev.sh${NC}"
    exit 1
fi

# 显示菜单
show_menu() {
    echo -e "${BLUE}请选择操作：${NC}"
    echo "1. 连接到MySQL命令行"
    echo "2. 查看数据库列表"
    echo "3. 查看表列表"
    echo "4. 导出数据库"
    echo "5. 导入数据库"
    echo "6. 查看数据库大小"
    echo "7. 查看Docker卷信息"
    echo "8. 备份数据库"
    echo "9. 清理数据库"
    echo "0. 退出"
    echo ""
}

# 连接MySQL
connect_mysql() {
    echo -e "${YELLOW}连接到MySQL...${NC}"
    docker exec -it $CONTAINER_NAME mysql -u$DB_USER -p$DB_PASS $DB_NAME
}

# 查看数据库列表
show_databases() {
    echo -e "${YELLOW}数据库列表：${NC}"
    docker exec $CONTAINER_NAME mysql -u$DB_USER -p$DB_PASS -e "SHOW DATABASES;"
}

# 查看表列表
show_tables() {
    echo -e "${YELLOW}表列表：${NC}"
    docker exec $CONTAINER_NAME mysql -u$DB_USER -p$DB_PASS -e "USE $DB_NAME; SHOW TABLES;"
}

# 导出数据库
export_database() {
    timestamp=$(date +"%Y%m%d_%H%M%S")
    backup_file="backup_${DB_NAME}_${timestamp}.sql"
    
    echo -e "${YELLOW}导出数据库到: $backup_file${NC}"
    docker exec $CONTAINER_NAME mysqldump -u$DB_USER -p$DB_PASS $DB_NAME > $backup_file
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 导出成功: $backup_file${NC}"
        ls -lh $backup_file
    else
        echo -e "${RED}❌ 导出失败${NC}"
    fi
}

# 导入数据库
import_database() {
    echo -e "${YELLOW}请输入要导入的SQL文件路径：${NC}"
    read -r sql_file
    
    if [ ! -f "$sql_file" ]; then
        echo -e "${RED}❌ 文件不存在: $sql_file${NC}"
        return
    fi
    
    echo -e "${YELLOW}导入数据库从: $sql_file${NC}"
    docker exec -i $CONTAINER_NAME mysql -u$DB_USER -p$DB_PASS $DB_NAME < $sql_file
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 导入成功${NC}"
    else
        echo -e "${RED}❌ 导入失败${NC}"
    fi
}

# 查看数据库大小
show_db_size() {
    echo -e "${YELLOW}数据库大小信息：${NC}"
    docker exec $CONTAINER_NAME mysql -u$DB_USER -p$DB_PASS -e "
    SELECT 
        table_schema AS 'Database',
        ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
    FROM information_schema.tables 
    WHERE table_schema = '$DB_NAME'
    GROUP BY table_schema;"
    
    echo ""
    echo -e "${YELLOW}表大小详情：${NC}"
    docker exec $CONTAINER_NAME mysql -u$DB_USER -p$DB_PASS -e "
    SELECT 
        table_name AS 'Table',
        ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)',
        table_rows AS 'Rows'
    FROM information_schema.TABLES 
    WHERE table_schema = '$DB_NAME'
    ORDER BY (data_length + index_length) DESC;"
}

# 查看Docker卷信息
show_volume_info() {
    echo -e "${YELLOW}Docker卷信息：${NC}"
    docker volume inspect mysql_dev_data
    
    echo ""
    echo -e "${YELLOW}卷使用情况：${NC}"
    docker system df -v | grep mysql_dev_data
}

# 备份数据库
backup_database() {
    backup_dir="backups"
    mkdir -p $backup_dir
    
    timestamp=$(date +"%Y%m%d_%H%M%S")
    backup_file="$backup_dir/rtpos_dev_backup_${timestamp}.sql"
    
    echo -e "${YELLOW}创建数据库备份...${NC}"
    docker exec $CONTAINER_NAME mysqldump -u$DB_USER -p$DB_PASS --single-transaction --routines --triggers $DB_NAME > $backup_file
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 备份成功: $backup_file${NC}"
        ls -lh $backup_file
        
        # 压缩备份文件
        gzip $backup_file
        echo -e "${GREEN}✅ 备份已压缩: ${backup_file}.gz${NC}"
    else
        echo -e "${RED}❌ 备份失败${NC}"
    fi
}

# 清理数据库
clean_database() {
    echo -e "${RED}⚠️  警告：此操作将清空所有表数据！${NC}"
    echo -e "${YELLOW}请输入 'YES' 确认清理数据库：${NC}"
    read -r confirmation
    
    if [ "$confirmation" = "YES" ]; then
        echo -e "${YELLOW}清理数据库...${NC}"
        
        # 获取所有表名并删除数据
        tables=$(docker exec $CONTAINER_NAME mysql -u$DB_USER -p$DB_PASS -e "USE $DB_NAME; SHOW TABLES;" | tail -n +2)
        
        for table in $tables; do
            echo -e "${YELLOW}清理表: $table${NC}"
            docker exec $CONTAINER_NAME mysql -u$DB_USER -p$DB_PASS -e "USE $DB_NAME; TRUNCATE TABLE $table;"
        done
        
        echo -e "${GREEN}✅ 数据库清理完成${NC}"
    else
        echo -e "${BLUE}操作已取消${NC}"
    fi
}

# 主循环
while true; do
    show_menu
    echo -n "请输入选项 (0-9): "
    read -r choice
    echo ""
    
    case $choice in
        1) connect_mysql ;;
        2) show_databases ;;
        3) show_tables ;;
        4) export_database ;;
        5) import_database ;;
        6) show_db_size ;;
        7) show_volume_info ;;
        8) backup_database ;;
        9) clean_database ;;
        0) echo -e "${GREEN}再见！${NC}"; exit 0 ;;
        *) echo -e "${RED}无效选项，请重新选择${NC}" ;;
    esac
    
    echo ""
    echo -e "${BLUE}按回车键继续...${NC}"
    read -r
    clear
done
