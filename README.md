# RTPosServer - 实时POS看盘API服务

## 项目简介

RTPosServer是一个基于Spring Boot的实时POS看盘API服务，用于对接商品订单组的订单数据，提供数据缓存和实时监控功能。

## 技术栈

- **Spring Boot 3.2.0** - 主流Java微服务开发框架
- **Spring MVC** - 提供控制器路由、请求处理等核心Web功能
- **Spring WebFlux** - 响应式编程模型，用于高并发或流式处理场景
- **Spring Security** - 用于鉴权、登录认证、权限控制
- **Spring Data JPA** - 操作数据库的ORM工具
- **MyBatis** - SQL映射框架
- **Redis** - 缓存服务
- **MySQL/H2** - 数据库（生产环境使用MySQL，开发环境使用H2）
- **Swagger/OpenAPI 3** - API文档生成

## 项目结构

```
src/main/java/com/rtpos/server/
├── RTPosServerApplication.java     # 主应用程序入口
├── config/                         # 配置类
│   ├── DatabaseConfig.java        # 数据库配置
│   ├── RedisConfig.java           # Redis配置
│   ├── SecurityConfig.java        # 安全配置
│   └── WebFluxConfig.java         # WebFlux配置
├── controller/                     # 控制器层
│   └── OrderController.java       # 订单控制器
├── service/                        # 服务层
│   ├── OrderService.java          # 订单服务接口
│   └── impl/
│       └── OrderServiceImpl.java  # 订单服务实现
├── repository/                     # 数据访问层
│   ├── OrderRepository.java       # 订单仓库
│   └── ProductRepository.java     # 商品仓库
├── entity/                         # 实体类
│   ├── BaseEntity.java           # 基础实体
│   ├── Order.java                # 订单实体
│   ├── OrderItem.java            # 订单项实体
│   └── Product.java              # 商品实体
├── dto/                           # 数据传输对象
│   ├── ApiResponse.java          # 统一响应格式
│   ├── OrderDTO.java             # 订单DTO
│   └── OrderItemDTO.java         # 订单项DTO
├── cache/                         # 缓存服务
│   └── CacheService.java         # Redis缓存服务
├── exception/                     # 异常处理
│   ├── BusinessException.java    # 业务异常
│   └── GlobalExceptionHandler.java # 全局异常处理器
└── utils/                         # 工具类
```

## 快速开始

### 环境要求

- Java 17+
- Maven 3.6+
- Redis 6.0+
- MySQL 8.0+ (生产环境)

### 开发环境启动

1. 克隆项目
```bash
git clone <repository-url>
cd RTPosServer
```

2. 启动Redis服务
```bash
redis-server
```

3. 运行应用（开发环境使用H2内存数据库）
```bash
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

4. 访问应用
- API文档: http://localhost:8080/api/v1/swagger-ui.html
- H2控制台: http://localhost:8080/api/v1/h2-console
- 健康检查: http://localhost:8080/api/v1/actuator/health

### 生产环境配置

1. 配置MySQL数据库
```yaml
spring:
  datasource:
    url: ************************************
    username: your_username
    password: your_password
```

2. 配置Redis
```yaml
spring:
  data:
    redis:
      host: your_redis_host
      port: 6379
      password: your_redis_password
```

3. 启动应用
```bash
mvn spring-boot:run -Dspring-boot.run.profiles=prod
```

## API接口

### 认证

默认使用HTTP Basic认证：
- 用户名: admin
- 密码: admin123

### 主要接口

#### 订单管理
- `POST /api/v1/orders` - 创建订单
- `GET /api/v1/orders/{id}` - 查询订单详情
- `PUT /api/v1/orders/{id}` - 更新订单
- `DELETE /api/v1/orders/{id}` - 删除订单
- `GET /api/v1/orders` - 分页查询订单
- `POST /api/v1/orders/{id}/pay` - 支付订单
- `POST /api/v1/orders/{id}/complete` - 完成订单
- `POST /api/v1/orders/{id}/cancel` - 取消订单

#### 统计接口
- `GET /api/v1/orders/stats/today/{storeId}` - 门店今日统计

#### 数据同步
- `POST /api/v1/orders/sync` - 手动触发订单同步

## 功能特性

### 数据缓存
- 使用Redis缓存热点数据
- 支持缓存过期时间配置
- 提供缓存清理机制

### 数据同步
- 定时同步订单数据
- 支持增量同步
- 可配置同步间隔和批次大小

### 安全认证
- HTTP Basic认证
- 基于角色的权限控制
- 方法级别的安全注解

### 监控运维
- Spring Boot Actuator健康检查
- 详细的日志记录
- 性能指标监控

## 配置说明

### 应用配置
主要配置项在 `application.yml` 中：

```yaml
rtpos:
  order:
    sync:
      enabled: true          # 是否启用订单同步
      interval: 30000        # 同步间隔（毫秒）
      batch-size: 100        # 批次大小
    cache:
      ttl: 300000           # 缓存TTL（毫秒）
```

### 数据库配置
支持MySQL和H2数据库，通过profile切换。

### Redis配置
支持单机和集群模式，可配置连接池参数。

## 开发指南

### 添加新的实体
1. 在 `entity` 包下创建实体类，继承 `BaseEntity`
2. 在 `repository` 包下创建对应的Repository接口
3. 在 `service` 包下创建服务接口和实现类
4. 在 `controller` 包下创建控制器类

### 缓存使用
使用 `CacheService` 进行缓存操作：
```java
@Autowired
private CacheService cacheService;

// 设置缓存
cacheService.set("key", value, Duration.ofMinutes(30));

// 获取缓存
String value = cacheService.get("key", String.class);
```

### 异常处理
继承 `BusinessException` 创建业务异常，全局异常处理器会自动处理。

## 测试

运行测试：
```bash
mvn test
```

## 部署

### Docker部署
```bash
# 构建镜像
docker build -t rtpos-server .

# 运行容器
docker run -p 8080:8080 rtpos-server
```

### 传统部署
```bash
# 打包
mvn clean package

# 运行
java -jar target/rtpos-server-1.0.0-SNAPSHOT.jar
```

## 贡献指南

1. Fork项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

本项目采用MIT许可证。
