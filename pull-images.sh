#!/bin/bash

# Docker镜像预拉取脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}    Docker镜像预拉取脚本${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker未运行，请先启动Docker Desktop${NC}"
    exit 1
fi

# 需要的镜像列表
images=(
    "mysql:8.0"
    "redis:7-alpine"
    "phpmyadmin/phpmyadmin"
)

echo -e "${YELLOW}🔄 开始拉取Docker镜像...${NC}"
echo ""

# 拉取镜像
for image in "${images[@]}"; do
    echo -e "${BLUE}📥 拉取镜像: $image${NC}"
    
    # 尝试拉取镜像，设置超时
    if timeout 300 docker pull "$image"; then
        echo -e "${GREEN}✅ 成功拉取: $image${NC}"
    else
        echo -e "${RED}❌ 拉取失败: $image${NC}"
        echo -e "${YELLOW}💡 建议：${NC}"
        echo -e "  1. 检查网络连接"
        echo -e "  2. 配置Docker镜像加速器"
        echo -e "  3. 稍后重试"
        exit 1
    fi
    echo ""
done

echo -e "${GREEN}🎉 所有镜像拉取完成！${NC}"
echo ""

# 显示已拉取的镜像
echo -e "${BLUE}📋 已拉取的镜像：${NC}"
for image in "${images[@]}"; do
    docker images | grep "$(echo $image | cut -d: -f1)" | head -1
done

echo ""
echo -e "${GREEN}✅ 现在可以运行 ./start-dev.sh 启动开发环境${NC}"
