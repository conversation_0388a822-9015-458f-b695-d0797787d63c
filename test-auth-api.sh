#!/bin/bash

# 用户认证API测试脚本

echo "=== 用户认证API测试脚本 ==="

# 设置基础URL
BASE_URL="http://localhost:8081/api/v1"
AUTH_URL="$BASE_URL/api/auth"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查应用是否运行
check_app_status() {
    echo -e "${BLUE}检查应用状态...${NC}"
    
    if curl -s "$BASE_URL/actuator/health" > /dev/null; then
        echo -e "${GREEN}✅ 应用正在运行${NC}"
        return 0
    else
        echo -e "${RED}❌ 应用未运行，请先启动应用${NC}"
        echo "启动命令: ./mvnw spring-boot:run -Dspring-boot.run.profiles=dev"
        return 1
    fi
}

# 测试发送验证码接口
test_send_code() {
    echo -e "\n${BLUE}测试发送验证码接口...${NC}"
    
    response=$(curl -s -w "\n%{http_code}" -X POST "$AUTH_URL/send-code" \
        -H "Content-Type: application/json" \
        -d '{
            "mobile": "15721096991"
        }')
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | sed '$d')
    
    echo "HTTP状态码: $http_code"
    echo "响应内容: $body"
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ 发送验证码接口测试通过${NC}"
    else
        echo -e "${RED}❌ 发送验证码接口测试失败${NC}"
    fi
}

# 测试验证码登录接口
test_login() {
    echo -e "\n${BLUE}测试验证码登录接口...${NC}"
    
    response=$(curl -s -w "\n%{http_code}" -X POST "$AUTH_URL/login" \
        -H "Content-Type: application/json" \
        -d '{
            "mobile": "15721096991",
            "code": "123456",
            "type": 1
        }')
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | sed '$d')
    
    echo "HTTP状态码: $http_code"
    echo "响应内容: $body"
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ 验证码登录接口测试通过${NC}"
    else
        echo -e "${RED}❌ 验证码登录接口测试失败${NC}"
    fi
}

# 测试查询员工信息接口
test_query_emp() {
    echo -e "\n${BLUE}测试查询员工信息接口...${NC}"
    
    response=$(curl -s -w "\n%{http_code}" -X POST "$AUTH_URL/query-emp-dept-role" \
        -H "Content-Type: application/json" \
        -d '{
            "empId": "20190232044"
        }')
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | sed '$d')
    
    echo "HTTP状态码: $http_code"
    echo "响应内容: $body"
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ 查询员工信息接口测试通过${NC}"
    else
        echo -e "${RED}❌ 查询员工信息接口测试失败${NC}"
    fi
}

# 测试参数校验
test_validation() {
    echo -e "\n${BLUE}测试参数校验...${NC}"
    
    # 测试手机号格式错误
    response=$(curl -s -w "\n%{http_code}" -X POST "$AUTH_URL/send-code" \
        -H "Content-Type: application/json" \
        -d '{
            "mobile": "123"
        }')
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | sed '$d')
    
    echo "测试无效手机号:"
    echo "HTTP状态码: $http_code"
    echo "响应内容: $body"
    
    if [ "$http_code" = "400" ]; then
        echo -e "${GREEN}✅ 参数校验测试通过${NC}"
    else
        echo -e "${YELLOW}⚠️ 参数校验可能需要调整${NC}"
    fi
}

# 显示API文档地址
show_api_docs() {
    echo -e "\n${BLUE}API文档地址:${NC}"
    echo "Swagger UI: $BASE_URL/swagger-ui.html"
    echo "API Docs: $BASE_URL/api-docs"
}

# 主函数
main() {
    if ! check_app_status; then
        exit 1
    fi
    
    test_send_code
    test_login
    test_query_emp
    test_validation
    show_api_docs
    
    echo -e "\n${GREEN}=== 测试完成 ===${NC}"
}

# 运行主函数
main
