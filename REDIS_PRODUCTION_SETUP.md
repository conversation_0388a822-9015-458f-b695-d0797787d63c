# 生产环境Redis配置指南

## 🏭 生产环境Redis部署方案

### 方案1: 单机Redis部署（推荐用于中小型应用）

#### 1.1 安装Redis服务器

**CentOS/RHEL:**
```bash
# 安装EPEL仓库
sudo yum install epel-release

# 安装Redis
sudo yum install redis

# 启动Redis服务
sudo systemctl start redis
sudo systemctl enable redis
```

**Ubuntu/Debian:**
```bash
# 更新包列表
sudo apt update

# 安装Redis
sudo apt install redis-server

# 启动Redis服务
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

**Docker部署（推荐）:**
```bash
# 创建Redis数据目录
mkdir -p /data/redis

# 启动Redis容器
docker run -d \
  --name redis-server \
  --restart unless-stopped \
  -p 6379:6379 \
  -v /data/redis:/data \
  redis:7-alpine redis-server --appendonly yes
```

#### 1.2 Redis安全配置

**编辑Redis配置文件** (`/etc/redis/redis.conf`):
```bash
# 绑定IP地址（仅允许特定IP访问）
bind 127.0.0.1 *************  # 替换为您的应用服务器IP

# 设置密码
requirepass your_strong_password_here

# 禁用危险命令
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG ""

# 设置最大内存
maxmemory 2gb
maxmemory-policy allkeys-lru

# 启用持久化
save 900 1
save 300 10
save 60 10000
appendonly yes
```

#### 1.3 Spring Boot应用配置

**方式1: 使用环境变量（推荐）**
```bash
# 设置环境变量
export REDIS_HOST=*************
export REDIS_PORT=6379
export REDIS_PASSWORD=your_strong_password_here
export REDIS_DATABASE=0

# 启动应用
java -jar rtpos-server.jar --spring.profiles.active=prod
```

**方式2: 使用配置文件**
```yaml
# application-prod.yml
spring:
  data:
    redis:
      host: *************
      port: 6379
      password: your_strong_password_here
      database: 0
```

### 方案2: Redis集群部署（推荐用于大型应用）

#### 2.1 Redis集群配置

**创建集群配置文件** (`redis-cluster.conf`):
```bash
port 7000
cluster-enabled yes
cluster-config-file nodes.conf
cluster-node-timeout 5000
appendonly yes
```

**启动集群节点**:
```bash
# 启动6个Redis实例（3主3从）
for port in 7000 7001 7002 7003 7004 7005; do
  redis-server redis-${port}.conf
done

# 创建集群
redis-cli --cluster create \
  *************:7000 *************:7001 *************:7002 \
  *************:7000 *************:7001 *************:7002 \
  --cluster-replicas 1
```

#### 2.2 Spring Boot集群配置

```yaml
# application-prod.yml
spring:
  data:
    redis:
      cluster:
        nodes:
          - *************:7000
          - *************:7001
          - *************:7002
          - *************:7000
          - *************:7001
          - *************:7002
        max-redirects: 3
      password: your_cluster_password
```

### 方案3: Redis哨兵部署（推荐用于高可用）

#### 3.1 哨兵配置

**创建哨兵配置文件** (`sentinel.conf`):
```bash
port 26379
sentinel monitor mymaster ************* 6379 2
sentinel auth-pass mymaster your_password
sentinel down-after-milliseconds mymaster 5000
sentinel failover-timeout mymaster 10000
sentinel parallel-syncs mymaster 1
```

#### 3.2 Spring Boot哨兵配置

```yaml
# application-prod.yml
spring:
  data:
    redis:
      sentinel:
        master: mymaster
        nodes:
          - *************:26379
          - *************:26379
          - *************:26379
      password: your_password
```

## 🔧 环境变量配置方式

### 推荐的环境变量配置

创建 `.env` 文件或在系统中设置：
```bash
# Redis基本配置
REDIS_HOST=*************
REDIS_PORT=6379
REDIS_PASSWORD=your_strong_password_here
REDIS_DATABASE=0

# Redis集群配置（如果使用集群）
REDIS_CLUSTER_NODES=*************:7000,*************:7001,*************:7002

# Redis哨兵配置（如果使用哨兵）
REDIS_SENTINEL_MASTER=mymaster
REDIS_SENTINEL_NODES=*************:26379,*************:26379,*************:26379
```

### Docker Compose部署

```yaml
# docker-compose.yml
version: '3.8'
services:
  redis:
    image: redis:7-alpine
    container_name: redis-server
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=your_strong_password_here

  rtpos-server:
    image: rtpos-server:latest
    container_name: rtpos-server
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=your_strong_password_here
    depends_on:
      - redis

volumes:
  redis_data:
```

## 🔍 连接验证

### 1. 测试Redis连接

```bash
# 测试Redis连接
redis-cli -h ************* -p 6379 -a your_password ping

# 应该返回: PONG
```

### 2. 验证Spring Boot连接

```bash
# 检查应用健康状态
curl http://your-app-server:8081/api/v1/actuator/health

# 应该看到Redis状态为UP
```

### 3. 测试Redis功能

```bash
# 在Redis中设置测试数据
redis-cli -h ************* -p 6379 -a your_password
> SET test_key "Hello Redis"
> GET test_key

# 在应用中验证缓存功能
curl http://your-app-server:8081/api/v1/api/pos-orders/query
```

## 🛡️ 安全最佳实践

### 1. 网络安全
- 使用防火墙限制Redis端口访问
- 仅允许应用服务器IP访问Redis
- 使用VPN或内网连接

### 2. 认证安全
- 设置强密码
- 定期更换密码
- 使用TLS加密连接

### 3. 配置安全
- 禁用危险命令
- 设置内存限制
- 启用持久化备份

## 📊 监控和维护

### 1. Redis监控

```bash
# 查看Redis信息
redis-cli -h ************* -p 6379 -a your_password info

# 监控Redis性能
redis-cli -h ************* -p 6379 -a your_password --latency
```

### 2. 日志监控

```bash
# 查看Redis日志
tail -f /var/log/redis/redis-server.log

# 查看应用日志中的Redis相关信息
grep -i redis /path/to/your/app/logs/rtpos-server-prod.log
```

## 🚀 部署检查清单

- [ ] Redis服务器已安装并启动
- [ ] Redis配置文件已正确配置
- [ ] 防火墙规则已设置
- [ ] 密码已设置并记录
- [ ] 应用环境变量已配置
- [ ] 连接测试已通过
- [ ] 健康检查显示Redis状态为UP
- [ ] 监控和日志已配置

## 🔧 故障排查

### 常见问题

1. **连接被拒绝**
   - 检查Redis服务是否启动
   - 检查防火墙设置
   - 检查bind配置

2. **认证失败**
   - 检查密码配置
   - 检查环境变量设置

3. **性能问题**
   - 检查内存使用情况
   - 检查连接池配置
   - 检查网络延迟

通过以上配置，您的Spring Boot应用就可以成功连接到生产环境的Redis服务了！
