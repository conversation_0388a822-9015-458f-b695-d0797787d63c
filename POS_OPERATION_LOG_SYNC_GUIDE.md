# POS收银操作日志同步功能指南

## 📋 功能概述

本功能实现了从外部API同步POS收银操作日志数据到本地数据库的完整解决方案，沿用了现有订单同步的架构模式，支持增量同步、全量同步和定时任务。

## 🏗️ 架构设计

### 核心组件
- **实体层**: `PosOperationLog` - 收银操作日志实体
- **数据访问层**: `PosOperationLogRepository` - 数据库操作
- **服务层**: `PosOperationLogSyncService` - 业务逻辑
- **控制器层**: `PosOperationLogController` - API接口
- **定时任务**: `PosOperationLogSyncScheduler` - 自动同步

### 数据流程
```
外部API → RestTemplate → DTO转换 → 实体保存 → 本地数据库
```

## 🔧 配置说明

### 环境配置

#### 开发环境 (application-dev.yml)
```yaml
pos:
  api:
    operation-log-base-url: http://rt-pos-api.beta1.fn
    operation-log-path: /api/pos/getOperationLogInfo
  sync:
    operation-log:
      enabled: true
      batch-size: 20
      store-nos: 1001,1002,1003
      pos-nos: 895,896,897
      incremental:
        enabled: true
```

#### 生产环境 (application-prod.yml)
```yaml
pos:
  api:
    operation-log-base-url: http://rt-pos-api.idc1.fn
    operation-log-path: /api/pos/getOperationLogInfo
  sync:
    operation-log:
      enabled: true
      batch-size: 20
      store-nos: 1001,1002,1003,1004,1005
      pos-nos: 895,896,897,898,899
      incremental:
        enabled: true
```

### 配置参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `operation-log-base-url` | 收银日志API基础URL | - |
| `operation-log-path` | 收银日志API路径 | `/api/pos/getOperationLogInfo` |
| `batch-size` | 每次同步的数据量 | 20 |
| `max-pages` | 最大页数限制 | 50 |
| `store-nos` | 门店编号列表 | - |
| `pos-nos` | POS机编号列表 | - |

## 📊 数据库表结构

### pos_operation_logs 表
```sql
CREATE TABLE pos_operation_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    store_no INT NOT NULL COMMENT '门店编号',
    pos_no INT NOT NULL COMMENT 'POS机编号',
    device_no VARCHAR(100) COMMENT '设备编号',
    pos_type INT COMMENT 'POS机类型',
    operation_type INT NOT NULL COMMENT '操作类型',
    flow_id VARCHAR(100) COMMENT '流水ID',
    shift_no VARCHAR(50) COMMENT '班次号',
    order_id VARCHAR(50) COMMENT '订单ID',
    membership_card_id VARCHAR(50) COMMENT '会员卡ID',
    occurrence_time BIGINT NOT NULL COMMENT '发生时间（时间戳）',
    occurrence_time_dt DATETIME COMMENT '发生时间（LocalDateTime）',
    work_time VARCHAR(200) COMMENT '工作时间描述',
    sync_status INT DEFAULT 0 COMMENT '同步状态',
    sync_time DATETIME COMMENT '同步时间',
    -- 基础字段
    created_at DATETIME NOT NULL,
    updated_at DATETIME,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),
    version BIGINT,
    deleted BOOLEAN DEFAULT FALSE
);
```

### 索引设计
- `idx_operation_store_no`: 门店编号索引
- `idx_operation_pos_no`: POS机编号索引
- `idx_operation_type`: 操作类型索引
- `idx_operation_occurrence_time`: 发生时间索引
- 等等...

## 🔄 同步机制详解

### 同步状态管理架构

为了确保数据同步的可靠性和一致性，系统采用了基于数据库的同步状态管理机制。

#### 为什么需要SyncStatus数据库支持？

**核心问题**：
1. **状态持久化**：应用重启后能够从上次同步位置继续
2. **精确断点续传**：避免重复同步或数据遗漏
3. **部分失败处理**：单个门店失败不影响其他门店
4. **监控统计**：提供详细的同步成功率和性能指标

#### 同步状态管理流程

```mermaid
sequenceDiagram
    participant S as 调度器
    participant SS as SyncStatusService
    participant DB as SyncStatus表
    participant API as 外部API
    participant POS as PosOperationLog表

    Note over S,POS: 增量同步开始
    S->>SS: 获取上次同步时间
    SS->>DB: SELECT last successful sync
    DB-->>SS: 返回时间戳
    SS-->>S: lastSyncTime

    loop 每个门店/POS机
        S->>SS: 记录同步开始
        SS->>DB: INSERT (RUNNING状态)
        S->>API: 调用同步接口
        alt 同步成功
            API-->>S: 返回操作日志数据
            S->>POS: 保存操作日志数据
            S->>SS: 记录同步成功
            SS->>DB: UPDATE (SUCCESS状态)
        else 同步失败
            API-->>S: 返回错误
            S->>SS: 记录同步失败
            SS->>DB: UPDATE (FAILURE状态)
        end
    end

    Note over S,POS: 下次同步时
    S->>SS: 获取上次同步时间
    SS->>DB: 只查询SUCCESS状态的记录
    Note over SS: 失败的门店/POS机会重新同步
```

#### 完整同步状态管理流程

```mermaid
graph TD
    A[应用启动] --> B{检查上次同步时间}
    B -->|有记录| C[从上次位置继续]
    B -->|无记录| D[从1小时前开始]
    C --> E[执行增量同步]
    D --> E
    E --> F[记录同步开始状态]
    F --> G[调用外部API]
    G -->|成功| H[记录成功状态]
    G -->|失败| I[记录失败状态]
    H --> J[更新最后同步时间]
    I --> K[下次重试时使用]

    style F fill:#e1f5fe
    style H fill:#c8e6c9
    style I fill:#ffcdd2
```

### SyncStatus表结构

```sql
CREATE TABLE sync_status (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    sync_type VARCHAR(50) NOT NULL COMMENT '同步类型：incremental, full, progressive',
    store_id VARCHAR(50) NOT NULL COMMENT '门店ID',
    start_time BIGINT COMMENT '同步开始时间戳',
    end_time BIGINT COMMENT '同步结束时间戳',
    sync_time DATETIME NOT NULL COMMENT '同步执行时间',
    status VARCHAR(20) NOT NULL COMMENT '状态：RUNNING, SUCCESS, FAILURE',
    record_count INT COMMENT '同步记录数量',
    error_message VARCHAR(1000) COMMENT '错误信息',
    duration_ms BIGINT COMMENT '耗时（毫秒）',
    -- 基础字段
    created_at DATETIME NOT NULL,
    updated_at DATETIME,
    version BIGINT,
    deleted BOOLEAN DEFAULT FALSE,

    INDEX idx_sync_type_store (sync_type, store_id),
    INDEX idx_sync_time (sync_time),
    INDEX idx_created_time (created_at)
);
```

### 同步类型说明

### 1. 增量同步 (INCREMENTAL)
- **频率**: 每5分钟执行一次
- **逻辑**: 从上次成功同步时间开始同步
- **配置**: `pos.sync.operation-log.incremental.enabled=true`
- **状态跟踪**: 每个门店/POS机独立记录同步状态

### 2. 全量同步 (FULL)
- **频率**: 每天凌晨3点执行
- **逻辑**: 同步指定时间段的所有操作日志
- **配置**: `pos.sync.operation-log.full.enabled=true`
- **状态跟踪**: 按门店/POS机分别记录同步进度

### 3. 缺失检查 (CHECK_MISSING)
- **频率**: 每30分钟执行一次
- **逻辑**: 检查并补充缺失的操作日志
- **配置**: `pos.sync.operation-log.check-missing.enabled=true`
- **状态跟踪**: 记录检查和修复的详细信息

### 同步状态查询和统计

#### 关键优势对比

| 场景 | 没有SyncStatus | 有SyncStatus |
|------|---------------|-------------|
| 应用重启 | 丢失同步状态，重新开始 | 从上次成功位置继续 |
| 部分失败 | 全部重新同步 | 只重新同步失败的门店/POS机 |
| 监控统计 | 无法统计 | 详细的成功率、耗时统计 |
| 故障排查 | 只有日志 | 结构化的错误记录 |
| 数据一致性 | 可能重复或遗漏 | 精确的断点续传 |

#### 实际应用场景示例

**场景1：部分门店同步失败**
```
第一次同步：
- 门店1001/POS895: 成功 (记录end_time: 1703088000000)
- 门店1002/POS896: 失败 (状态: FAILURE)
- 门店1003/POS897: 成功 (记录end_time: 1703088000000)

第二次同步：
- 门店1001/POS895: 从1703088000000开始同步
- 门店1002/POS896: 从原始时间重新同步 (因为上次失败)
- 门店1003/POS897: 从1703088000000开始同步
```

**场景2：应用重启**
```
重启前：已同步到2024-01-01 12:00:00
重启后：自动从数据库读取上次成功时间，继续同步
```

**场景3：监控和统计**
```java
// 获取同步统计
Map<String, Object> stats = syncStatusService.getSyncStatistics("INCREMENTAL");
// 结果示例：
{
  "totalSyncs": 1000,
  "successSyncs": 950,
  "failureSyncs": 50,
  "successRate": 95.0,
  "averageDurationMs": 3500,
  "lastSyncTime": "2024-01-01T12:00:00",
  "lastSyncStatus": "SUCCESS"
}
```

#### 数据库中的状态记录示例

```sql
-- sync_status表的数据示例
INSERT INTO sync_status VALUES
(1, 'INCREMENTAL', '1001_895', 1703001600000, 1703088000000, '2024-01-01 10:00:00', 'SUCCESS', 150, NULL, 5000),
(2, 'INCREMENTAL', '1002_896', 1703001600000, 1703088000000, '2024-01-01 10:01:00', 'FAILURE', 0, 'API timeout', 30000),
(3, 'INCREMENTAL', '1003_897', 1703001600000, 1703088000000, '2024-01-01 10:02:00', 'SUCCESS', 89, NULL, 3000);

-- 下次同步时查询上次成功时间
SELECT MAX(end_time) FROM sync_status
WHERE sync_type = 'INCREMENTAL'
  AND store_id = '1001_895'
  AND status = 'SUCCESS';  -- 只查询成功的记录
```

## 🚀 API接口

### 手动同步接口

#### 1. 同步指定门店和POS机的操作日志
```http
POST /api/pos-operation-logs/sync/store/{storeNo}/pos/{posNo}
?startTime=1749517200000&endTime=1749520800000
```

#### 2. 增量同步
```http
POST /api/pos-operation-logs/sync/incremental
```

#### 3. 全量同步
```http
POST /api/pos-operation-logs/sync/full
?storeNo=1001&posNo=895&startTime=1749517200000&endTime=1749520800000
```

#### 4. 批量同步
```http
POST /api/pos-operation-logs/sync/batch
?storeNos=1001,1002&posNos=895,896&startTime=1749517200000&endTime=1749520800000
```

### 查询接口

#### 1. 查询操作日志
```http
GET /api/pos-operation-logs/query
?storeNo=1001&posNo=895&page=0&size=20
```

#### 2. 根据订单ID查询
```http
GET /api/pos-operation-logs/order/{orderId}
```

#### 3. 根据班次号查询
```http
GET /api/pos-operation-logs/shift/{shiftNo}
```

#### 4. 根据流水ID查询
```http
GET /api/pos-operation-logs/flow/{flowId}
```

## 📝 操作类型说明

| 操作类型 | 说明 |
|----------|------|
| 10001 | 上班 |
| 10002 | 下班 |
| 10004 | 会员操作 |
| 10005 | 开始交易 |
| 10031 | 完成交易 |

## 🧪 测试

### 运行单元测试
```bash
mvn test -Dtest=PosOperationLogSyncServiceTest
```

### 手动测试同步功能
```bash
# 启动应用
mvn spring-boot:run

# 测试增量同步
curl -X POST "http://localhost:8081/api/v1/api/pos-operation-logs/sync/incremental"

# 测试指定门店同步
curl -X POST "http://localhost:8081/api/v1/api/pos-operation-logs/sync/store/1001/pos/895?startTime=1749517200000&endTime=1749520800000"
```

## 🔍 监控和日志

### 同步状态监控

#### 1. 同步状态查询接口
```http
GET /api/sync-status/statistics/{syncType}
# 示例：GET /api/sync-status/statistics/INCREMENTAL
```

#### 2. 获取最后同步时间
```http
GET /api/sync-status/last-sync-time/{syncType}/{storeId}
# 示例：GET /api/sync-status/last-sync-time/INCREMENTAL/1001_895
```

#### 3. 检查是否需要同步
```http
GET /api/sync-status/needs-sync/{storeId}?lastModifiedTime=1703088000000
```

### 日志级别
- **INFO**: 同步开始/完成信息
- **DEBUG**: 详细的同步过程和状态变更
- **ERROR**: 同步失败错误信息和状态记录

### 关键日志示例
```
2024-01-01 10:00:00 INFO  - Starting scheduled incremental sync for operation logs
2024-01-01 10:00:00 DEBUG - Recording sync start: type=INCREMENTAL, store=1001_895, timeRange=1703001600000-1703088000000
2024-01-01 10:00:01 INFO  - Successfully fetched 16 operation logs from API
2024-01-01 10:00:02 INFO  - Operation logs saved: 10 new, 6 updated, 0 skipped
2024-01-01 10:00:02 DEBUG - Recording sync success: type=INCREMENTAL, store=1001_895, records=16
2024-01-01 10:00:03 INFO  - Completed scheduled incremental sync for operation logs
```

### 同步状态日志示例
```
2024-01-01 10:00:00 DEBUG - SyncStatusService: Recording sync start for INCREMENTAL sync of store 1001_895
2024-01-01 10:00:03 DEBUG - SyncStatusService: Recording sync success for INCREMENTAL sync of store 1001_895, duration: 3000ms
2024-01-01 10:01:00 ERROR - SyncStatusService: Recording sync failure for INCREMENTAL sync of store 1002_896, error: Connection timeout
```

## 🛠️ 故障排查

### 1. API连接问题
**现象**: 同步失败，出现连接超时
**排查步骤**:
```bash
# 检查API配置
grep -A 5 "operation-log-base-url" src/main/resources/application-dev.yml

# 测试API连接
curl -X POST "http://rt-pos-api.beta1.fn/api/pos/getOperationLogInfo" \
  -H "Content-Type: application/json" \
  -d '{"storeNo":1001,"posNo":895,"beginTime":1749517200000,"endTime":1749520800000,"limit":20,"page":1}'
```

### 2. 数据重复问题
**现象**: 同一条操作日志被重复保存
**解决方案**: 检查唯一键约束逻辑

### 3. 定时任务未执行
**现象**: 没有看到定时同步日志
**排查步骤**:
```bash
# 检查定时任务配置
grep -A 5 "operation-log.enabled" src/main/resources/application-dev.yml

# 手动触发同步测试
curl -X POST "http://localhost:8081/api/v1/api/pos-operation-logs/sync/incremental"
```

## 📈 性能优化

### 1. 批量处理
- 使用批量插入减少数据库操作
- 合理设置批次大小（默认20条）

### 2. 索引优化
- 为常用查询字段添加索引
- 定期分析查询性能

### 3. 数据清理
- 定期清理历史数据
- 配置数据保留策略

## 🔄 扩展功能

### 1. 数据清理任务
```java
@Scheduled(cron = "0 0 4 * * SUN")
public void cleanupOldOperationLogs() {
    // 清理30天前的数据
}
```

### 2. 健康检查
```java
@Scheduled(fixedRate = 3600000)
public void healthCheck() {
    // 检查API连接状态
}
```

## 🎯 最佳实践

### 1. 同步状态管理
- 始终通过SyncStatusService记录同步状态
- 定期清理过期的同步记录（建议保留30天）
- 监控长时间处于RUNNING状态的任务

### 2. 错误处理
- 单个门店/POS机同步失败不应影响其他设备
- 记录详细的错误信息便于故障排查
- 实现合理的重试机制

### 3. 性能优化
- 根据系统负载调整批次大小和并发数
- 在业务低峰期执行全量同步
- 监控外部API的调用频率限制

### 4. 数据一致性
- 使用事务确保同步状态和业务数据的一致性
- 定期进行数据完整性检查
- 实现数据修复机制

## 📚 相关文档

- [POS订单同步优化策略](SYNC_OPTIMIZATION_STRATEGY.md)
- [POS订单同步指南](POS_ORDER_STORAGE_README.md)
- [项目总体架构](README.md)
- [数据库设计文档](DATABASE_DESIGN.md)

## 🔧 故障排查清单

### 同步状态相关问题

1. **同步状态不更新**
   - 检查SyncStatusService是否正确注入
   - 确认数据库事务配置
   - 查看是否有异常被捕获但未处理

2. **重复同步数据**
   - 检查同步状态查询逻辑
   - 确认是否正确使用SUCCESS状态过滤
   - 验证时间戳比较逻辑

3. **同步进度丢失**
   - 检查数据库连接配置
   - 确认sync_status表结构正确
   - 验证索引是否创建

4. **性能问题**
   - 检查sync_status表的查询性能
   - 考虑添加适当的索引
   - 定期清理历史数据

通过这套完整的同步状态管理机制，系统能够可靠地处理大规模POS操作日志同步，确保数据的完整性和一致性。
