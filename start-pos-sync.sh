#!/bin/bash

# POS订单同步系统启动脚本

echo "=== POS订单同步系统启动脚本 ==="

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请先安装Java 17或更高版本"
    exit 1
fi

# 检查Maven环境
if [ ! -f "./mvnw" ]; then
    echo "错误: 未找到Maven Wrapper，请确保在项目根目录下运行此脚本"
    exit 1
fi

# 设置环境变量
export SPRING_PROFILES_ACTIVE=dev

echo "当前环境: $SPRING_PROFILES_ACTIVE"
echo "启动POS订单同步系统..."

# 编译项目
echo "正在编译项目..."
./mvnw clean compile -DskipTests

if [ $? -ne 0 ]; then
    echo "错误: 项目编译失败"
    exit 1
fi

echo "编译成功！"

# 启动应用
echo "正在启动应用..."
echo "应用将在以下端口启动:"
echo "- 开发环境: http://localhost:8081/api/v1"
echo "- Swagger文档: http://localhost:8081/api/v1/swagger-ui.html"
echo "- H2数据库控制台: http://localhost:8081/api/v1/h2-console"
echo ""
echo "定时任务配置:"
echo "- 增量同步: 每5分钟执行一次"
echo "- 全量同步: 每天凌晨2点执行"
echo "- 同步接口: http://middle-order-biz.beta1.fn/api/queryOrder/querySelfPosNodeOrders"
echo ""
echo "按 Ctrl+C 停止应用"
echo "========================"

./mvnw spring-boot:run
