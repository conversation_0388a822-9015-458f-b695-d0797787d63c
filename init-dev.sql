-- 开发环境数据库初始化脚本
-- 创建开发环境数据库
CREATE DATABASE IF NOT EXISTS rtpos_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用开发环境数据库
USE rtpos_dev;

-- 创建开发用户（如果不存在）
CREATE USER IF NOT EXISTS 'rtpos_dev'@'%' IDENTIFIED BY 'rtpos_dev123';

-- 授权开发用户对开发数据库的所有权限
GRANT ALL PRIVILEGES ON rtpos_dev.* TO 'rtpos_dev'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 设置时区
SET time_zone = '+08:00';

-- 开发环境优化设置
SET GLOBAL innodb_buffer_pool_size = 128M;
SET GLOBAL max_connections = 200;

COMMIT;
