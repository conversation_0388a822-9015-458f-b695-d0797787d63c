# 环境配置管理指南

## 📁 环境配置文件

项目支持多环境配置，通过不同的环境变量文件来管理：

```
├── .env.development    # 开发环境配置
├── .env.test          # 测试环境配置
├── .env.production    # 生产环境配置
└── src/config/api.js  # API配置文件
```

## 🔧 环境变量说明

### 开发环境 (`.env.development`)
```bash
NODE_ENV=development
VUE_APP_API_BASE_URL=http://10.211.241.215:8080
VUE_APP_API_PREFIX=/api
VUE_APP_USE_MOCK=false
VUE_APP_ENV=development
VUE_APP_TITLE=POS看盘中台-开发环境
```

### 测试环境 (`.env.test`)
```bash
NODE_ENV=production
VUE_APP_API_BASE_URL=http://test-api.your-company.com:8080
VUE_APP_API_PREFIX=/api
VUE_APP_USE_MOCK=false
VUE_APP_ENV=test
VUE_APP_TITLE=POS看盘中台-测试环境
```

### 生产环境 (`.env.production`)
```bash
NODE_ENV=production
VUE_APP_API_BASE_URL=https://your-production-api.com
VUE_APP_API_PREFIX=/api
VUE_APP_USE_MOCK=false
VUE_APP_ENV=production
VUE_APP_TITLE=POS看盘中台
```

## 🚀 启动命令

### 开发环境
```bash
npm run serve          # 默认开发环境
npm run serve:dev       # 明确指定开发环境
```

### 测试环境
```bash
npm run serve:test      # 测试环境
```

### 构建命令
```bash
npm run build           # 默认生产环境构建
npm run build:dev       # 开发环境构建
npm run build:test      # 测试环境构建
npm run build:prod      # 生产环境构建
```

## 🔗 API配置

### 统一配置文件 (`src/config/api.js`)
```javascript
// 基础配置
const config = {
  baseURL: process.env.VUE_APP_API_BASE_URL,
  apiPrefix: process.env.VUE_APP_API_PREFIX,
  timeout: 10000,
  useMock: process.env.VUE_APP_USE_MOCK === 'true',
  env: process.env.VUE_APP_ENV
}

// API端点配置
export const API_ENDPOINTS = {
  POS_STORE: '/pos/getPosStore',
  DASHBOARD_DATA: '/dashboard/data',
  // ... 其他端点
}
```

### 请求实例 (`src/utils/request.js`)
- 统一的axios配置
- 请求/响应拦截器
- 错误处理
- 日志记录

## 🌐 代理配置

开发环境通过 `vue.config.js` 配置代理解决跨域问题：

```javascript
proxy: {
  '/api': {
    target: process.env.VUE_APP_API_BASE_URL,
    changeOrigin: true,
    secure: false
  }
}
```

## 📝 使用示例

### 在组件中使用
```javascript
import { getPosStore } from '@/api'

// 自动使用当前环境的API配置
const storeList = await getPosStore()
```

### 环境判断
```javascript
if (process.env.VUE_APP_ENV === 'development') {
  console.log('开发环境特有逻辑')
}
```

## 🔄 环境切换

1. **本地开发**: 默认使用 `.env.development`
2. **测试部署**: 使用 `npm run build:test`
3. **生产部署**: 使用 `npm run build:prod`

## ⚠️ 注意事项

1. **环境变量必须以 `VUE_APP_` 开头** 才能在前端代码中访问
2. **不要在环境文件中存储敏感信息** (如密码、密钥等)
3. **生产环境的API地址需要根据实际情况修改**
4. **代理配置只在开发环境生效**

## 🛠️ 故障排查

### API请求失败
1. 检查环境变量配置是否正确
2. 确认API服务器是否可访问
3. 查看浏览器控制台的网络请求
4. 检查代理配置是否正确

### 跨域问题
1. 开发环境：检查 `vue.config.js` 代理配置
2. 生产环境：需要后端配置CORS或使用nginx代理
