# POS订单数据存储系统

## 概述

本系统实现了从外部接口 `http://middle-order-biz.beta1.fn/api/queryOrder/querySelfPosNodeOrders` 同步POS订单数据到本地数据库的完整功能，包括数据存储、查询、同步等核心功能。

## 功能特性

### 1. 数据存储
- **POS订单实体**：完整映射外部接口返回的订单数据结构
- **数据库表**：自动创建 `pos_orders` 表，包含所有必要字段和索引
- **数据转换**：自动处理时间戳与LocalDateTime的转换，金额分与元的转换
- **扩展字段**：支持JSON格式的扩展数据存储

### 2. 数据同步
- **API同步**：从外部接口获取订单数据并存储到本地
- **增量同步**：定时同步最新的订单数据
- **全量同步**：支持指定时间范围的全量数据同步
- **重复处理**：自动检测和处理重复订单数据

### 3. 数据查询
- **分页查询**：支持按门店、时间范围等条件分页查询
- **统计查询**：提供订单数量、销售额等统计功能
- **多种查询方式**：支持按业务订单ID、外部订单ID等多种方式查询

### 4. 定时任务
- **增量同步**：每5分钟执行一次增量同步
- **全量同步**：每天凌晨2点执行全量同步（可配置）
- **缺失检查**：定期检查和补充缺失的订单数据

## 核心组件

### 1. 实体类
- `PosOrder`：POS订单实体，映射数据库表
- `PosOrderDTO`：数据传输对象，用于API交互

### 2. 数据访问层
- `PosOrderRepository`：提供丰富的查询方法

### 3. 服务层
- `PosOrderService`：订单业务逻辑接口
- `PosOrderServiceImpl`：订单业务逻辑实现
- `PosOrderSyncService`：数据同步服务接口
- `PosOrderSyncServiceImpl`：数据同步服务实现

### 4. 控制器
- `PosOrderController`：提供REST API接口

### 5. 定时任务
- `PosOrderSyncScheduler`：定时同步任务

## API接口

### 查询接口

#### 1. 分页查询POS订单
```http
POST /api/pos-orders/query
Content-Type: application/json

{
    "storeId": "1001",
    "generalTimeStart": 1748707200000,
    "generalTimeEnd": 1749484799000,
    "currentPage": 1,
    "pageSize": 20
}
```

#### 2. 根据门店ID查询订单
```http
GET /api/pos-orders/store/1001?page=0&size=20
```

#### 3. 根据时间范围查询订单
```http
GET /api/pos-orders/store/1001/time-range?startTime=1748707200000&endTime=1749484799000&page=0&size=20
```

#### 4. 根据业务订单ID查询
```http
GET /api/pos-orders/biz-order/910010211748915822
```

#### 5. 获取门店统计信息
```http
GET /api/pos-orders/store/1001/statistics?startTime=1748707200000&endTime=1749484799000
```

### 同步接口

#### 1. 从外部API同步订单
```http
POST /api/pos-orders/sync
Content-Type: application/json

{
    "storeId": "1001",
    "generalTimeStart": 1748707200000,
    "generalTimeEnd": 1749484799000,
    "currentPage": 1,
    "pageSize": 20
}
```

#### 2. 同步指定门店订单
```http
POST /api/pos-orders/sync/store/1001?startTime=1748707200000&endTime=1749484799000
```

#### 3. 执行增量同步
```http
POST /api/pos-orders/sync/incremental
```

## 配置说明

### application.yml 配置
```yaml
# POS订单同步配置
pos:
  api:
    base-url: http://middle-order-biz.beta1.fn
    query-orders-path: /api/queryOrder/querySelfPosNodeOrders
  sync:
    enabled: true
    batch-size: 100
    max-pages: 50
    incremental:
      enabled: true
    check-missing:
      enabled: false
    full:
      enabled: false
```

### 数据库配置
系统使用JPA自动创建数据库表，也可以手动执行 `data.sql` 中的建表语句。

## 使用示例

### 1. 启动应用
```bash
mvn spring-boot:run
```

### 2. 手动同步数据
```bash
curl -X POST "http://localhost:8081/api/v1/api/pos-orders/sync" \
  -H "Content-Type: application/json" \
  -d '{
    "storeId": "1001",
    "generalTimeStart": 1748707200000,
    "generalTimeEnd": 1749484799000,
    "currentPage": 1,
    "pageSize": 100
  }'
```

### 3. 查询订单数据
```bash
curl -X GET "http://localhost:8081/api/v1/api/pos-orders/store/1001?page=0&size=10"
```

### 4. 获取统计信息
```bash
curl -X GET "http://localhost:8081/api/v1/api/pos-orders/store/1001/statistics?startTime=1748707200000&endTime=1749484799000"
```

## 数据库表结构

### pos_orders 表
主要字段包括：
- `id`：主键
- `store_id`：门店ID
- `biz_order_id`：业务订单ID（唯一）
- `main_order_id`：主订单ID
- `out_order_id`：外部订单ID
- `order_time`：下单时间（时间戳）
- `order_time_dt`：下单时间（LocalDateTime）
- `pay_time`：支付时间（时间戳）
- `pay_time_dt`：支付时间（LocalDateTime）
- `order_status`：订单状态
- `operator_id`：操作员ID
- `operator_name`：操作员姓名
- `original_amt`：原始金额（分）
- `original_amount`：原始金额（元）
- `discount_amt`：折扣金额（分）
- `discount_amount`：折扣金额（元）
- 其他扩展字段...

## 监控和日志

### 1. 应用监控
访问 `http://localhost:8081/api/v1/actuator/health` 查看应用健康状态

### 2. API文档
访问 `http://localhost:8081/api/v1/swagger-ui.html` 查看完整的API文档

### 3. 日志查看
日志文件位置：`logs/rtpos-server.log`

## 扩展说明

### 1. 添加新的门店
在 `PosOrderSyncServiceImpl.getStoreIdsForSync()` 方法中添加新的门店ID

### 2. 自定义同步频率
修改 `PosOrderSyncScheduler` 中的 `@Scheduled` 注解参数

### 3. 添加新的查询条件
在 `PosOrderRepository` 中添加新的查询方法

### 4. 扩展数据字段
修改 `PosOrder` 实体类和对应的DTO类

## 注意事项

1. **数据一致性**：系统会自动处理重复数据，避免重复插入
2. **性能优化**：使用了适当的数据库索引，支持大数据量查询
3. **错误处理**：包含完整的异常处理和日志记录
4. **配置灵活**：支持通过配置文件控制同步行为
5. **测试覆盖**：提供了完整的单元测试

## 故障排查

### 1. 同步失败
- 检查外部API是否可访问
- 检查网络连接和防火墙设置
- 查看应用日志获取详细错误信息

### 2. 数据库连接问题
- 检查数据库配置
- 确认数据库服务是否正常运行
- 检查数据库用户权限

### 3. 性能问题
- 调整批处理大小 `pos.sync.batch-size`
- 优化查询条件和索引
- 监控数据库性能指标
