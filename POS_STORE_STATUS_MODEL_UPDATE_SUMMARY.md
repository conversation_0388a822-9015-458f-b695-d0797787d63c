# POS门店状态模型更新总结

## 问题描述

外部API的数据结构发生了变化，原来的 `online` 字段现在被拆分成了三个独立的字段：
- `onlManualPos` - 在线人工POS列表
- `onlSelfPos` - 在线自助POS列表  
- `onlMobilePos` - 在线移动POS列表

这导致 `PosStoreStatusBody.getOnline()` 方法一直返回 null，影响了 `PosOperationLogSyncScheduler` 中的在线设备优先同步逻辑。

## 解决方案

### 1. 更新 PosStoreStatusBody.java 模型

#### 新增字段
```java
@Schema(description = "在线人工POS列表")
@JsonProperty("onlManualPos")
private List<Integer> onlManualPos;

@Schema(description = "在线自助POS列表")
@JsonProperty("onlSelfPos")
private List<Integer> onlSelfPos;

@Schema(description = "在线移动POS列表")
@JsonProperty("onlMobilePos")
private List<Integer> onlMobilePos;
```

#### 废弃旧字段
```java
@Deprecated
@Schema(description = "在线机台集合，如果没有返回空集合（已废弃，请使用 getAllOnlinePos() 方法）")
@JsonProperty("online")
private List<Integer> online;
```

#### 新增方法
```java
/**
 * 获取所有在线POS设备列表
 * 合并人工POS、自助POS和移动POS的在线设备
 */
public List<Integer> getAllOnlinePos() {
    List<Integer> allOnline = new ArrayList<>();
    
    // 添加在线人工POS
    if (onlManualPos != null && !onlManualPos.isEmpty()) {
        allOnline.addAll(onlManualPos);
    }
    
    // 添加在线自助POS
    if (onlSelfPos != null && !onlSelfPos.isEmpty()) {
        allOnline.addAll(onlSelfPos);
    }
    
    // 添加在线移动POS
    if (onlMobilePos != null && !onlMobilePos.isEmpty()) {
        allOnline.addAll(onlMobilePos);
    }
    
    // 如果新字段都为空，尝试使用旧的online字段（向后兼容）
    if (allOnline.isEmpty() && online != null && !online.isEmpty()) {
        allOnline.addAll(online);
    }
    
    return allOnline;
}

/**
 * 获取在线人工POS设备列表
 */
public List<Integer> getOnlineManualPos() {
    return onlManualPos != null ? onlManualPos : new ArrayList<>();
}

/**
 * 获取在线自助POS设备列表
 */
public List<Integer> getOnlineSelfPos() {
    return onlSelfPos != null ? onlSelfPos : new ArrayList<>();
}

/**
 * 获取在线移动POS设备列表
 */
public List<Integer> getOnlineMobilePos() {
    return onlMobilePos != null ? onlMobilePos : new ArrayList<>();
}
```

### 2. 更新 PosOperationLogSyncScheduler.java

将所有使用 `statusBody.getOnline()` 的地方替换为 `statusBody.getAllOnlinePos()`：

#### 修改前
```java
if (statusBody.getOnline() != null && !statusBody.getOnline().isEmpty()) {
    for (Integer posNo : statusBody.getOnline()) {
        // 同步逻辑
    }
}
```

#### 修改后
```java
List<Integer> allOnlinePos = statusBody.getAllOnlinePos();
if (allOnlinePos != null && !allOnlinePos.isEmpty()) {
    log.debug("Store {} has {} online POS devices (Manual: {}, Self: {}, Mobile: {})",
            store.getStoreId(), allOnlinePos.size(),
            statusBody.getOnlineManualPos().size(),
            statusBody.getOnlineSelfPos().size(),
            statusBody.getOnlineMobilePos().size());
            
    for (Integer posNo : allOnlinePos) {
        // 同步逻辑
    }
}
```

### 3. 更新 PosStoreStatusServiceImpl.java

更新日志记录，显示详细的设备类型统计：

```java
List<Integer> allOnlinePos = result.getBody().getAllOnlinePos();
log.debug("Store status details - Total POS: {}, Online: {} (Manual: {}, Self: {}, Mobile: {}), Offline: {}, Login: {}, Logout: {}", 
        result.getBody().getStoreAllPosCount(),
        allOnlinePos.size(),
        result.getBody().getOnlineManualPos().size(),
        result.getBody().getOnlineSelfPos().size(),
        result.getBody().getOnlineMobilePos().size(),
        result.getBody().getOffline() != null ? result.getBody().getOffline().size() : 0,
        result.getBody().getLogin() != null ? result.getBody().getLogin().size() : 0,
        result.getBody().getLogout() != null ? result.getBody().getLogout().size() : 0);
```

### 4. 更新文档

更新了 `POS_STORE_STATUS_API_GUIDE.md` 文档，包括：
- 新的响应示例，展示拆分后的字段结构
- 完整的字段说明表格
- 更新的 Java 使用示例

## 新的API响应格式

```json
{
  "rsCode": "00000000",
  "msg": "Success",
  "body": {
    "storeAllPosCount": 105,
    "allOnlPosCount": 12000,
    "allOffPosCount": 8999,
    "onlManualPosNum": 4,
    "manualPosCount": 50,
    "onlSelfPosNum": 3,
    "selfPosCount": 30,
    "onlMobilePosNum": 1,
    "mobilePosCount": 10,
    "onlManualPos": [14, 19, 285, 333],
    "onlSelfPos": [666, 895, 259],
    "onlMobilePos": [879],
    "online": [14, 19, 285, 333, 666, 895, 259, 879],
    "offline": [1, 21, 87, 995, 996, 997, 998, 999],
    "login": [144, 213, 285, 389, 859, 996],
    "logout": [1, 14, 19, 21, 87, 112, 123, 999]
  },
  "traceId": null
}
```

## 向后兼容性

- 保留了原有的 `online` 字段，标记为 `@Deprecated`
- `getAllOnlinePos()` 方法会优先使用新字段，如果新字段为空则回退到旧字段
- 现有的代码可以无缝迁移到新的方法

## 优势

1. **设备类型区分**: 现在可以明确区分人工POS、自助POS和移动POS
2. **更精确的同步策略**: 可以根据不同设备类型制定不同的同步策略
3. **更详细的日志**: 日志中会显示各种设备类型的详细统计
4. **向后兼容**: 不会破坏现有功能

## 测试建议

1. 测试新的API响应格式是否正确解析
2. 验证 `getAllOnlinePos()` 方法是否正确合并三种设备类型
3. 确认操作日志同步调度器是否正常工作
4. 检查日志输出是否包含详细的设备类型统计

## 后续优化建议

1. 可以考虑为不同类型的POS设备设置不同的同步优先级
2. 可以根据设备类型调整同步间隔时间
3. 可以添加设备类型相关的监控指标
