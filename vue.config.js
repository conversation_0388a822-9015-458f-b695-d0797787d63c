module.exports = {
  lintOnSave: false,
  devServer: {
    port: 8080,
    open: true,
    client: {
      overlay: false,
    },
    // 代理配置 - 解决开发环境跨域问题
    proxy: {
      "/api": {
        target: process.env.VUE_APP_API_BASE_URL || "http://localhost:8081",
        changeOrigin: true,
        secure: false,
        logLevel: "debug",
        onProxyReq: (proxyReq, req) => {
          console.log(
            `🔄 代理请求: ${req.method} ${req.url} -> ${proxyReq.getHeader(
              "host"
            )}${proxyReq.path}`
          );
        },
        onProxyRes: (proxyRes, req) => {
          console.log(`✅ 代理响应: ${proxyRes.statusCode} ${req.url}`);
        },
        onError: (err, req) => {
          console.error(`❌ 代理错误: ${req.url}`, err.message);
        },
      },
    },
  },

  configureWebpack: {
    devtool:
      process.env.NODE_ENV === "development"
        ? "source-map"
        : "none",
  },

  // 生产环境配置
  productionSourceMap: false,

  // 公共路径配置
  publicPath: process.env.NODE_ENV === "production" ? "./" : "/",
};
