#!/bin/bash

echo "=== RTPosServer 启动脚本 ==="
echo "正在检查环境..."

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请安装Java 17或更高版本"
    exit 1
fi

echo "Java版本:"
java -version

# 检查Redis是否运行（可选）
if command -v redis-cli &> /dev/null; then
    if redis-cli ping &> /dev/null; then
        echo "✓ Redis服务正在运行"
    else
        echo "⚠ Redis服务未运行，将使用内存缓存"
    fi
else
    echo "⚠ 未安装Redis，将使用内存缓存"
fi

echo ""
echo "正在编译项目..."
./mvnw clean compile

if [ $? -eq 0 ]; then
    echo "✓ 编译成功"
    echo ""
    echo "正在启动应用..."
    echo "访问地址:"
    echo "  - API文档: http://localhost:8081/api/v1/swagger-ui.html"
    echo "  - H2控制台: http://localhost:8081/api/v1/h2-console"
    echo "  - 健康检查: http://localhost:8081/api/v1/actuator/health"
    echo ""
    echo "默认认证信息:"
    echo "  - 用户名: admin"
    echo "  - 密码: admin123"
    echo ""
    echo "按 Ctrl+C 停止应用"
    echo "=========================="

    ./mvnw spring-boot:run -Dspring-boot.run.profiles=dev
else
    echo "✗ 编译失败，请检查错误信息"
    exit 1
fi
