#!/bin/bash

echo "=== RTPosServer API 测试脚本 ==="
echo ""

# 基础URL
BASE_URL="http://localhost:8080/api/v1"
AUTH="admin:admin123"

echo "1. 测试健康检查端点..."
curl -s -u $AUTH "$BASE_URL/actuator/health" | jq '.' || echo "健康检查失败"
echo ""

echo "2. 测试获取订单列表（应该为空）..."
curl -s -u $AUTH "$BASE_URL/orders" | jq '.' || echo "获取订单列表失败"
echo ""

echo "3. 测试创建订单..."
ORDER_JSON='{
  "orderNo": "TEST001",
  "storeId": 1,
  "storeName": "测试门店",
  "customerId": 1001,
  "customerName": "测试客户",
  "customerPhone": "13800138000",
  "totalAmount": 100.00,
  "discountAmount": 0.00,
  "actualAmount": 100.00,
  "paymentMethod": "CASH",
  "status": "PENDING",
  "orderTime": "2024-12-01T10:00:00",
  "remark": "测试订单",
  "orderItems": [
    {
      "productId": 1,
      "productName": "测试商品",
      "productCode": "TEST001",
      "categoryId": 1,
      "categoryName": "测试分类",
      "unitPrice": 100.00,
      "quantity": 1,
      "discountAmount": 0.00,
      "subtotal": 100.00,
      "specifications": "测试规格",
      "remark": "测试商品备注"
    }
  ]
}'

curl -s -u $AUTH -H "Content-Type: application/json" -X POST -d "$ORDER_JSON" "$BASE_URL/orders" | jq '.' || echo "创建订单失败"
echo ""

echo "4. 测试获取订单详情..."
curl -s -u $AUTH "$BASE_URL/orders/orderNo/TEST001" | jq '.' || echo "获取订单详情失败"
echo ""

echo "5. 测试门店统计..."
curl -s -u $AUTH "$BASE_URL/orders/stats/today/1" | jq '.' || echo "获取门店统计失败"
echo ""

echo "=== 测试完成 ==="
