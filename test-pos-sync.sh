#!/bin/bash

# POS订单同步功能测试脚本

BASE_URL="http://localhost:8081/api/v1"
API_BASE="$BASE_URL/api/pos-orders"

echo "=== POS订单同步功能测试 ==="

# 检查应用是否启动
echo "1. 检查应用状态..."
curl -s "$BASE_URL/actuator/health" > /dev/null
if [ $? -ne 0 ]; then
    echo "错误: 应用未启动或无法访问，请先运行 ./start-pos-sync.sh"
    exit 1
fi
echo "✓ 应用运行正常"

# 测试手动同步
echo ""
echo "2. 测试手动同步功能..."
echo "正在调用同步接口..."

# 计算时间范围（最近24小时）
END_TIME=$(date +%s)000  # 当前时间戳（毫秒）
START_TIME=$((END_TIME - 86400000))  # 24小时前

SYNC_REQUEST='{
    "storeId": "1001",
    "generalTimeStart": '$START_TIME',
    "generalTimeEnd": '$END_TIME',
    "currentPage": 1,
    "pageSize": 10
}'

echo "同步请求参数:"
echo "$SYNC_REQUEST"
echo ""

SYNC_RESPONSE=$(curl -s -X POST "$API_BASE/sync" \
    -H "Content-Type: application/json" \
    -d "$SYNC_REQUEST")

echo "同步响应:"
echo "$SYNC_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$SYNC_RESPONSE"
echo ""

# 测试查询功能
echo "3. 测试查询功能..."

# 查询门店订单
echo "查询门店1001的订单..."
QUERY_RESPONSE=$(curl -s "$API_BASE/store/1001?page=0&size=5")
echo "查询结果:"
echo "$QUERY_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$QUERY_RESPONSE"
echo ""

# 获取统计信息
echo "4. 获取统计信息..."
STATS_RESPONSE=$(curl -s "$API_BASE/store/1001/statistics?startTime=$START_TIME&endTime=$END_TIME")
echo "统计信息:"
echo "$STATS_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$STATS_RESPONSE"
echo ""

# 测试增量同步
echo "5. 测试增量同步..."
INCREMENTAL_RESPONSE=$(curl -s -X POST "$API_BASE/sync/incremental")
echo "增量同步结果:"
echo "$INCREMENTAL_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$INCREMENTAL_RESPONSE"
echo ""

# 获取最新订单
echo "6. 获取最新订单..."
LATEST_RESPONSE=$(curl -s "$API_BASE/latest?limit=3")
echo "最新订单:"
echo "$LATEST_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$LATEST_RESPONSE"
echo ""

echo "=== 测试完成 ==="
echo ""
echo "可用的API接口:"
echo "- Swagger文档: $BASE_URL/swagger-ui.html"
echo "- H2数据库控制台: $BASE_URL/h2-console"
echo "- 健康检查: $BASE_URL/actuator/health"
echo ""
echo "主要API端点:"
echo "- 手动同步: POST $API_BASE/sync"
echo "- 增量同步: POST $API_BASE/sync/incremental"
echo "- 查询订单: GET $API_BASE/store/{storeId}"
echo "- 获取统计: GET $API_BASE/store/{storeId}/statistics"
echo "- 最新订单: GET $API_BASE/latest"
