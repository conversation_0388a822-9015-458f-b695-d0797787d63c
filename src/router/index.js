import { createRouter, createWebHashHistory } from "vue-router";
import { useUserStore } from "@/stores/user";

const routes = [
  {
    path: "/login",
    name: "Login",
    component: () => import("@/views/Login.vue"),
    meta: {
      title: "登录",
      requiresAuth: false,
    },
  },
  {
    path: "/",
    name: "Dashboard",
    component: () => import("@/views/Dashboard.vue"),
    meta: {
      title: "仪表盘",
      requiresAuth: true,
    },
  },
  {
    path: "/pos-usage",
    name: "PosUsage",
    component: () => import("@/views/PosUsage.vue"),
    meta: {
      title: "POS使用情况",
      requiresAuth: true,
    },
  },
  {
    path: "/order-analysis",
    name: "OrderAnalysis",
    component: () => import("@/views/OrderAnalysis.vue"),
    meta: {
      title: "订单分析",
      requiresAuth: true,
    },
  },
  {
    path: "/device-status",
    name: "DeviceStatus",
    component: () => import("@/views/DeviceStatus.vue"),
    meta: {
      title: "设备状态",
      requiresAuth: true,
    },
  },
  {
    path: "/energy-optimization",
    name: "EnergyOptimization",
    component: () => import("@/views/EnergyOptimization.vue"),
    meta: {
      title: "能耗优化",
      requiresAuth: true,
    },
  },
  {
    path: "/account",
    name: "Account",
    component: () => import("@/views/Account.vue"),
    meta: {
      title: "账户管理",
      requiresAuth: true,
    },
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore();

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - POS看盘中台`;
  }

  // 检查是否需要登录
  if (to.meta.requiresAuth !== false) {
    if (!userStore.isLoggedIn) {
      // 未登录，跳转到登录页
      next({
        name: "Login",
        query: { redirect: to.fullPath },
      });
      return;
    }
  }

  // 已登录用户访问登录页，跳转到首页
  if (to.name === "Login" && userStore.isLoggedIn) {
    next({ name: "Dashboard" });
    return;
  }

  next();
});

export default router;
