package com.rtpos.server.dto;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Arrays;
import java.util.List;

/**
 * PosStoreStatusBody 测试类
 * 测试新的在线设备字段和方法
 *
 * <AUTHOR>
 */
public class PosStoreStatusBodyTest {

    @Test
    public void testGetAllOnlinePos_WithNewFields() {
        // 准备测试数据
        PosStoreStatusBody body = new PosStoreStatusBody();
        body.setOnlManualPos(Arrays.asList(14, 19, 285, 333));
        body.setOnlSelfPos(Arrays.asList(666, 895, 259));
        body.setOnlMobilePos(Arrays.asList(879));

        // 测试 getAllOnlinePos 方法
        List<Integer> allOnlinePos = body.getAllOnlinePos();

        // 验证结果
        assertNotNull(allOnlinePos);
        assertEquals(8, allOnlinePos.size());
        assertTrue(allOnlinePos.containsAll(Arrays.asList(14, 19, 285, 333, 666, 895, 259, 879)));
    }

    @Test
    public void testGetAllOnlinePos_WithOldFieldFallback() {
        // 准备测试数据 - 只有旧字段有数据
        PosStoreStatusBody body = new PosStoreStatusBody();
        body.setOnline(Arrays.asList(1, 2, 3, 4, 5));

        // 测试 getAllOnlinePos 方法
        List<Integer> allOnlinePos = body.getAllOnlinePos();

        // 验证结果 - 应该回退到使用旧字段
        assertNotNull(allOnlinePos);
        assertEquals(5, allOnlinePos.size());
        assertTrue(allOnlinePos.containsAll(Arrays.asList(1, 2, 3, 4, 5)));
    }

    @Test
    public void testGetAllOnlinePos_EmptyFields() {
        // 准备测试数据 - 所有字段都为空
        PosStoreStatusBody body = new PosStoreStatusBody();

        // 测试 getAllOnlinePos 方法
        List<Integer> allOnlinePos = body.getAllOnlinePos();

        // 验证结果
        assertNotNull(allOnlinePos);
        assertTrue(allOnlinePos.isEmpty());
    }

    @Test
    public void testGetOnlineManualPos() {
        // 准备测试数据
        PosStoreStatusBody body = new PosStoreStatusBody();
        body.setOnlManualPos(Arrays.asList(14, 19, 285));

        // 测试方法
        List<Integer> manualPos = body.getOnlineManualPos();

        // 验证结果
        assertNotNull(manualPos);
        assertEquals(3, manualPos.size());
        assertTrue(manualPos.containsAll(Arrays.asList(14, 19, 285)));
    }

    @Test
    public void testGetOnlineManualPos_Null() {
        // 准备测试数据 - 字段为null
        PosStoreStatusBody body = new PosStoreStatusBody();
        body.setOnlManualPos(null);

        // 测试方法
        List<Integer> manualPos = body.getOnlineManualPos();

        // 验证结果 - 应该返回空列表而不是null
        assertNotNull(manualPos);
        assertTrue(manualPos.isEmpty());
    }

    @Test
    public void testGetOnlineSelfPos() {
        // 准备测试数据
        PosStoreStatusBody body = new PosStoreStatusBody();
        body.setOnlSelfPos(Arrays.asList(666, 895));

        // 测试方法
        List<Integer> selfPos = body.getOnlineSelfPos();

        // 验证结果
        assertNotNull(selfPos);
        assertEquals(2, selfPos.size());
        assertTrue(selfPos.containsAll(Arrays.asList(666, 895)));
    }

    @Test
    public void testGetOnlineMobilePos() {
        // 准备测试数据
        PosStoreStatusBody body = new PosStoreStatusBody();
        body.setOnlMobilePos(Arrays.asList(879));

        // 测试方法
        List<Integer> mobilePos = body.getOnlineMobilePos();

        // 验证结果
        assertNotNull(mobilePos);
        assertEquals(1, mobilePos.size());
        assertTrue(mobilePos.contains(879));
    }

    @Test
    public void testNewFieldsPriorityOverOldField() {
        // 准备测试数据 - 新旧字段都有数据
        PosStoreStatusBody body = new PosStoreStatusBody();
        body.setOnlManualPos(Arrays.asList(1, 2));
        body.setOnlSelfPos(Arrays.asList(3, 4));
        body.setOnlMobilePos(Arrays.asList(5));
        body.setOnline(Arrays.asList(100, 200, 300)); // 旧字段数据

        // 测试 getAllOnlinePos 方法
        List<Integer> allOnlinePos = body.getAllOnlinePos();

        // 验证结果 - 应该优先使用新字段，忽略旧字段
        assertNotNull(allOnlinePos);
        assertEquals(5, allOnlinePos.size());
        assertTrue(allOnlinePos.containsAll(Arrays.asList(1, 2, 3, 4, 5)));
        assertFalse(allOnlinePos.containsAll(Arrays.asList(100, 200, 300)));
    }
}
