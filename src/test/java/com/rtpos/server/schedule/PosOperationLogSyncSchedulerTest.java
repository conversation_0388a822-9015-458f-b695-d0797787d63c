package com.rtpos.server.schedule;

import com.rtpos.server.service.PosOperationLogSyncService;
import com.rtpos.server.service.PosStoreService;
import com.rtpos.server.service.PosStoreStatusService;
import com.rtpos.server.service.SyncStatusService;
import com.rtpos.server.entity.PosStore;
import com.rtpos.server.entity.SyncStatus;
import com.rtpos.server.dto.PosStoreStatusRequest;
import com.rtpos.server.dto.PosStoreStatusResponse;
import com.rtpos.server.dto.PosStoreStatusBody;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * POS收银操作日志同步调度器测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class PosOperationLogSyncSchedulerTest {

    @Mock
    private PosOperationLogSyncService operationLogSyncService;

    @Mock
    private PosStoreService posStoreService;

    @Mock
    private PosStoreStatusService posStoreStatusService;

    @Mock
    private SyncStatusService syncStatusService;

    @InjectMocks
    private PosOperationLogSyncScheduler scheduler;

    @BeforeEach
    void setUp() {
        // 设置测试配置
        ReflectionTestUtils.setField(scheduler, "batchStoreSize", 2);
        ReflectionTestUtils.setField(scheduler, "timeSliceHours", 1);
        ReflectionTestUtils.setField(scheduler, "maxConcurrentStores", 1);
        ReflectionTestUtils.setField(scheduler, "onlinePriority", true);
        ReflectionTestUtils.setField(scheduler, "defaultPosNosConfig", "1,2,3");
    }

    @Test
    void testIncrementalSyncWithTrueIncrementalStrategy() {
        // 准备测试数据
        PosStore store1 = createTestStore("1001", "测试门店1");
        PosStore store2 = createTestStore("1002", "测试门店2");

        Page<PosStore> storePage = new PageImpl<>(Arrays.asList(store1, store2));

        // Mock门店服务
        when(posStoreService.getAllStores(any(PageRequest.class)))
            .thenReturn(storePage);

        // Mock同步状态服务 - 门店1有历史同步记录
        long lastSyncTime1 = System.currentTimeMillis() - 10 * 60 * 1000L; // 10分钟前
        when(syncStatusService.getLastSyncTime(eq(SyncStatus.Type.INCREMENTAL.name()), eq("1001")))
            .thenReturn(lastSyncTime1);

        // Mock同步状态服务 - 门店2没有历史同步记录
        when(syncStatusService.getLastSyncTime(eq(SyncStatus.Type.INCREMENTAL.name()), eq("1002")))
            .thenReturn(null);

        // Mock门店状态服务 - 门店1有在线设备
        PosStoreStatusResponse response1 = createStatusResponse(
            Arrays.asList(101, 102), // 在线设备
            Arrays.asList(103, 104)  // 离线设备
        );
        when(posStoreStatusService.getStoreStatus(argThat(req ->
            req.getStoreNo().equals(1001))))
            .thenReturn(response1);

        // Mock门店状态服务 - 门店2只有离线设备
        PosStoreStatusResponse response2 = createStatusResponse(
            Arrays.asList(),              // 无在线设备
            Arrays.asList(201, 202)       // 离线设备
        );
        when(posStoreStatusService.getStoreStatus(argThat(req ->
            req.getStoreNo().equals(1002))))
            .thenReturn(response2);

        // 执行测试
        scheduler.incrementalSync();

        // 验证调用
        verify(posStoreService, times(1)).getAllStores(any(PageRequest.class));
        verify(syncStatusService, times(2)).getLastSyncTime(eq(SyncStatus.Type.INCREMENTAL.name()), anyString());
        verify(syncStatusService, times(2)).recordSyncStart(eq(SyncStatus.Type.INCREMENTAL.name()), anyString(), anyLong(), anyLong());
        verify(syncStatusService, times(2)).recordSyncSuccess(eq(SyncStatus.Type.INCREMENTAL.name()), anyString(), anyInt());

        // 验证门店1的在线设备被同步（2个在线设备）
        verify(operationLogSyncService, times(2)).fullSyncOperationLogs(
            eq(1001), anyInt(), anyLong(), anyLong());

        // 验证门店2的离线设备被同步（因为没有在线设备，同步2个离线设备）
        verify(operationLogSyncService, times(2)).fullSyncOperationLogs(
            eq(1002), anyInt(), anyLong(), anyLong());
    }

    @Test
    void testIncrementalSyncWithSmallTimeInterval() {
        // 准备测试数据 - 最近刚同步过的门店
        PosStore store = createTestStore("1003", "最近同步门店");
        Page<PosStore> storePage = new PageImpl<>(Arrays.asList(store));

        when(posStoreService.getAllStores(any(PageRequest.class)))
            .thenReturn(storePage);

        // Mock同步状态服务 - 30秒前刚同步过（小于1分钟阈值）
        long recentSyncTime = System.currentTimeMillis() - 30 * 1000L; // 30秒前
        when(syncStatusService.getLastSyncTime(eq(SyncStatus.Type.INCREMENTAL.name()), eq("1003")))
            .thenReturn(recentSyncTime);

        // 执行测试
        scheduler.incrementalSync();

        // 验证调用
        verify(posStoreService, times(1)).getAllStores(any(PageRequest.class));
        verify(syncStatusService, times(1)).getLastSyncTime(eq(SyncStatus.Type.INCREMENTAL.name()), eq("1003"));

        // 验证由于时间间隔太小，没有进行同步操作
        verify(syncStatusService, never()).recordSyncStart(anyString(), anyString(), anyLong(), anyLong());
        verify(operationLogSyncService, never()).fullSyncOperationLogs(anyInt(), anyInt(), anyLong(), anyLong());
    }

    @Test
    void testIncrementalSyncWithFallbackStrategy() {
        // 准备测试数据
        PosStore store = createTestStore("1004", "状态获取失败门店");
        Page<PosStore> storePage = new PageImpl<>(Arrays.asList(store));

        when(posStoreService.getAllStores(any(PageRequest.class)))
            .thenReturn(storePage);

        // Mock同步状态服务 - 没有历史记录
        when(syncStatusService.getLastSyncTime(eq(SyncStatus.Type.INCREMENTAL.name()), eq("1004")))
            .thenReturn(null);

        // Mock状态服务失败
        when(posStoreStatusService.getStoreStatus(any(PosStoreStatusRequest.class)))
            .thenThrow(new RuntimeException("API调用失败"));

        // 执行测试
        scheduler.incrementalSync();

        // 验证调用
        verify(posStoreService, times(1)).getAllStores(any(PageRequest.class));
        verify(syncStatusService, times(1)).recordSyncStart(eq(SyncStatus.Type.INCREMENTAL.name()), eq("1004"), anyLong(), anyLong());

        // 验证使用降级策略同步默认设备（配置的1,2,3）
        verify(operationLogSyncService, times(3)).fullSyncOperationLogs(
            eq(1004), anyInt(), anyLong(), anyLong());

        // 验证记录同步成功
        verify(syncStatusService, times(1)).recordSyncSuccess(eq(SyncStatus.Type.INCREMENTAL.name()), eq("1004"), eq(3));
    }

    @Test
    void testIncrementalSyncWithMixedDevices() {
        // 准备测试数据 - 门店有少量在线设备和多个离线设备
        PosStore store = createTestStore("1003", "混合设备门店");
        Page<PosStore> storePage = new PageImpl<>(Arrays.asList(store));
        
        when(posStoreService.getAllStores(any(PageRequest.class)))
            .thenReturn(storePage);
        
        // 只有1个在线设备，5个离线设备
        PosStoreStatusResponse response = createStatusResponse(
            Arrays.asList(301),                    // 1个在线设备
            Arrays.asList(302, 303, 304, 305, 306) // 5个离线设备
        );
        when(posStoreStatusService.getStoreStatus(any(PosStoreStatusRequest.class)))
            .thenReturn(response);

        // 执行测试
        scheduler.incrementalSync();

        // 验证在线设备被同步
        verify(operationLogSyncService, times(1)).fullSyncOperationLogs(
            eq(1003), eq(301), anyLong(), anyLong());
        
        // 验证部分离线设备被同步（最多2个，因为在线设备<3个，补充到3个）
        verify(operationLogSyncService, times(2)).fullSyncOperationLogs(
            eq(1003), intThat(posNo -> posNo >= 302 && posNo <= 303), anyLong(), anyLong());
    }

    @Test
    void testIncrementalSyncWithStatusServiceFailure() {
        // 准备测试数据
        PosStore store = createTestStore("1004", "状态获取失败门店");
        Page<PosStore> storePage = new PageImpl<>(Arrays.asList(store));
        
        when(posStoreService.getAllStores(any(PageRequest.class)))
            .thenReturn(storePage);
        
        // Mock状态服务失败
        when(posStoreStatusService.getStoreStatus(any(PosStoreStatusRequest.class)))
            .thenThrow(new RuntimeException("API调用失败"));

        // 执行测试
        scheduler.incrementalSync();

        // 验证即使状态获取失败，也不会影响整体流程
        verify(posStoreService, times(1)).getAllStores(any(PageRequest.class));
        verify(posStoreStatusService, times(1)).getStoreStatus(any(PosStoreStatusRequest.class));
        
        // 由于状态获取失败，不应该调用同步服务
        verify(operationLogSyncService, never()).fullSyncOperationLogs(anyInt(), anyInt(), anyLong(), anyLong());
    }

    @Test
    void testIncrementalSyncWithOnlinePriorityDisabled() {
        // 禁用在线优先策略
        ReflectionTestUtils.setField(scheduler, "onlinePriority", false);
        
        // 执行测试
        scheduler.incrementalSync();
        
        // 验证使用传统同步方式
        verify(operationLogSyncService, times(1)).incrementalSyncOperationLogs();
        verify(posStoreService, never()).getAllStores(any(PageRequest.class));
        verify(posStoreStatusService, never()).getStoreStatus(any(PosStoreStatusRequest.class));
    }

    @Test
    void testFullSyncWithOptimizedStrategy() {
        // 准备测试数据
        PosStore store = createTestStore("1005", "全量同步门店");
        Page<PosStore> storePage = new PageImpl<>(Arrays.asList(store));
        
        when(posStoreService.getAllStores(any(PageRequest.class)))
            .thenReturn(storePage);
        
        PosStoreStatusResponse response = createStatusResponse(
            Arrays.asList(501, 502), // 在线设备
            Arrays.asList(503, 504)  // 离线设备
        );
        when(posStoreStatusService.getStoreStatus(any(PosStoreStatusRequest.class)))
            .thenReturn(response);

        // 执行测试
        scheduler.fullSync();

        // 验证调用
        verify(posStoreService, times(1)).getAllStores(any(PageRequest.class));
        verify(posStoreStatusService, times(1)).getStoreStatus(any(PosStoreStatusRequest.class));
        
        // 验证在线设备被同步
        verify(operationLogSyncService, times(2)).fullSyncOperationLogs(
            eq(1005), anyInt(), anyLong(), anyLong());
    }

    /**
     * 创建测试门店
     */
    private PosStore createTestStore(String storeId, String storeName) {
        PosStore store = new PosStore();
        store.setStoreId(storeId);
        store.setStoreName(storeName);
        return store;
    }

    /**
     * 创建门店状态响应
     */
    private PosStoreStatusResponse createStatusResponse(List<Integer> onlineDevices, List<Integer> offlineDevices) {
        PosStoreStatusResponse response = new PosStoreStatusResponse();
        response.setRsCode("00000000");
        response.setMsg("Success");
        
        PosStoreStatusBody body = new PosStoreStatusBody();
        body.setOnline(onlineDevices);
        body.setOffline(offlineDevices);
        body.setStoreAllPosCount(onlineDevices.size() + offlineDevices.size());
        
        response.setBody(body);
        return response;
    }
}
