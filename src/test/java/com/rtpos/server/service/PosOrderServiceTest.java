package com.rtpos.server.service;

import com.rtpos.server.dto.PosOrderDTO;
import com.rtpos.server.dto.PosOrderQueryRequest;
import com.rtpos.server.entity.PosOrder;
import com.rtpos.server.repository.PosOrderRepository;
import com.rtpos.server.service.impl.PosOrderServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * POS订单服务测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class PosOrderServiceTest {

    @Mock
    private PosOrderRepository posOrderRepository;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private PosOrderServiceImpl posOrderService;

    private PosOrderDTO testPosOrderDTO;
    private PosOrder testPosOrder;

    @BeforeEach
    void setUp() {
        // 创建测试数据
        testPosOrderDTO = new PosOrderDTO();
        testPosOrderDTO.setStoreId("1001");
        testPosOrderDTO.setBizOrderId(910010211748915822L);
        testPosOrderDTO.setMainOrderId(984758087376297984L);
        testPosOrderDTO.setOutOrderId("910010211748915822");
        testPosOrderDTO.setOrderTime(1748915822018L);
        testPosOrderDTO.setPayTime(1748915824578L);
        testPosOrderDTO.setOrderStatus(1);
        testPosOrderDTO.setOperatorId("20190231768");
        testPosOrderDTO.setOperatorName("陆潇华");
        testPosOrderDTO.setOriginalAmt(890);
        testPosOrderDTO.setDiscountAmt(880);
        testPosOrderDTO.setMemberDiscountAmt(0);
        testPosOrderDTO.setPackingFee(0);
        testPosOrderDTO.setPackingFeeTaxRate(BigDecimal.ZERO);
        testPosOrderDTO.setMemberCardNum("");
        testPosOrderDTO.setPosNo(21);
        testPosOrderDTO.setDeviceId("dadacc24-acfc-3676-86de-d27862839376");
        testPosOrderDTO.setMqTimestamp(1748915822042L);
        testPosOrderDTO.setDutyCode("25060309541620190231768");
        testPosOrderDTO.setExtendedJson("{\"posId\":\"VKSONBCL8bUDAI/vzALsxZ2W\",\"selfBuiltPos\":true,\"memDegrade\":\"1\",\"dutyCode\":\"25060309541620190231768\"}");
        testPosOrderDTO.setDeleted(0);
        testPosOrderDTO.setDeletedTime(0L);
        testPosOrderDTO.setJobStatus(2);
        testPosOrderDTO.setJobTimes(1);
        testPosOrderDTO.setGmtCreate(1748915824000L);
        testPosOrderDTO.setGmtModified(1748915826000L);

        testPosOrder = new PosOrder();
        testPosOrder.setId(1L);
        testPosOrder.setStoreId("1001");
        testPosOrder.setBizOrderId(910010211748915822L);
        testPosOrder.setMainOrderId(984758087376297984L);
        testPosOrder.setOutOrderId("910010211748915822");
        testPosOrder.setOrderTime(1748915822018L);
        testPosOrder.setPayTime(1748915824578L);
        testPosOrder.setOrderStatus(1);
        testPosOrder.setOperatorId("20190231768");
        testPosOrder.setOperatorName("陆潇华");
        testPosOrder.setOriginalAmt(890);
        testPosOrder.setDiscountAmt(880);
        testPosOrder.setMemberDiscountAmt(0);
        testPosOrder.setPackingFee(0);
        testPosOrder.setPackingFeeTaxRate(BigDecimal.ZERO);
        testPosOrder.setMemberCardNum("");
        testPosOrder.setPosNo(21);
        testPosOrder.setDeviceId("dadacc24-acfc-3676-86de-d27862839376");
        testPosOrder.setMqTimestamp(1748915822042L);
        testPosOrder.setDutyCode("25060309541620190231768");
        testPosOrder.setExtendedJson("{\"posId\":\"VKSONBCL8bUDAI/vzALsxZ2W\",\"selfBuiltPos\":true,\"memDegrade\":\"1\",\"dutyCode\":\"25060309541620190231768\"}");
        testPosOrder.setOriginalDeleted(0);
        testPosOrder.setDeletedTime(0L);
        testPosOrder.setJobStatus(2);
        testPosOrder.setJobTimes(1);
        testPosOrder.setGmtCreate(1748915824000L);
        testPosOrder.setGmtModified(1748915826000L);
        testPosOrder.setCreatedAt(LocalDateTime.now());
        testPosOrder.setUpdatedAt(LocalDateTime.now());
        testPosOrder.setDeleted(false);
    }

    @Test
    void testSavePosOrder() {
        // Given
        when(posOrderRepository.save(any(PosOrder.class))).thenReturn(testPosOrder);

        // When
        PosOrderDTO result = posOrderService.savePosOrder(testPosOrderDTO);

        // Then
        assertNotNull(result);
        assertEquals(testPosOrderDTO.getBizOrderId(), result.getBizOrderId());
        assertEquals(testPosOrderDTO.getStoreId(), result.getStoreId());
        verify(posOrderRepository, times(1)).save(any(PosOrder.class));
    }

    @Test
    void testFindByBizOrderId() {
        // Given
        when(posOrderRepository.findByBizOrderId(910010211748915822L)).thenReturn(Optional.of(testPosOrder));

        // When
        PosOrderDTO result = posOrderService.findByBizOrderId(910010211748915822L);

        // Then
        assertNotNull(result);
        assertEquals(910010211748915822L, result.getBizOrderId());
        verify(posOrderRepository, times(1)).findByBizOrderId(910010211748915822L);
    }

    @Test
    void testFindByBizOrderIdNotFound() {
        // Given
        when(posOrderRepository.findByBizOrderId(999L)).thenReturn(Optional.empty());

        // When
        PosOrderDTO result = posOrderService.findByBizOrderId(999L);

        // Then
        assertNull(result);
        verify(posOrderRepository, times(1)).findByBizOrderId(999L);
    }

    @Test
    void testFindPosOrders() {
        // Given
        PosOrderQueryRequest request = new PosOrderQueryRequest();
        request.setStoreId("1001");
        request.setGeneralTimeStart(1748915822000L);
        request.setGeneralTimeEnd(1748915826000L);
        request.setCurrentPage(1);
        request.setPageSize(20);

        List<PosOrder> orders = Arrays.asList(testPosOrder);
        Page<PosOrder> page = new PageImpl<>(orders, PageRequest.of(0, 20), 1);
        
        when(posOrderRepository.findByStoreIdAndOrderTimeBetween(
                eq("1001"), eq(1748915822000L), eq(1748915826000L), any(Pageable.class)))
                .thenReturn(page);

        // When
        Page<PosOrderDTO> result = posOrderService.findPosOrders(request);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(1, result.getContent().size());
        assertEquals("1001", result.getContent().get(0).getStoreId());
    }

    @Test
    void testCountByStoreId() {
        // Given
        when(posOrderRepository.countByStoreId("1001")).thenReturn(5L);

        // When
        Long result = posOrderService.countByStoreId("1001");

        // Then
        assertEquals(5L, result);
        verify(posOrderRepository, times(1)).countByStoreId("1001");
    }

    @Test
    void testExistsByBizOrderId() {
        // Given
        when(posOrderRepository.existsByBizOrderId(910010211748915822L)).thenReturn(true);

        // When
        boolean result = posOrderService.existsByBizOrderId(910010211748915822L);

        // Then
        assertTrue(result);
        verify(posOrderRepository, times(1)).existsByBizOrderId(910010211748915822L);
    }

    @Test
    void testSavePosOrders() {
        // Given
        List<PosOrderDTO> orderDTOs = Arrays.asList(testPosOrderDTO);
        List<PosOrder> savedOrders = Arrays.asList(testPosOrder);
        
        when(posOrderRepository.saveAll(anyList())).thenReturn(savedOrders);

        // When
        List<PosOrderDTO> result = posOrderService.savePosOrders(orderDTOs);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testPosOrderDTO.getBizOrderId(), result.get(0).getBizOrderId());
        verify(posOrderRepository, times(1)).saveAll(anyList());
    }
}
