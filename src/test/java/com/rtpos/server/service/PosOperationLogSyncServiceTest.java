package com.rtpos.server.service;

import com.rtpos.server.dto.PosOperationLogQueryRequest;
import com.rtpos.server.dto.PosOperationLogQueryResponse;
import com.rtpos.server.entity.PosOperationLog;
import com.rtpos.server.repository.PosOperationLogRepository;
import com.rtpos.server.service.impl.PosOperationLogSyncServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * POS收银操作日志同步服务测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class PosOperationLogSyncServiceTest {

    @Mock
    private PosOperationLogRepository operationLogRepository;

    @Mock
    private RestTemplate restTemplate;

    @InjectMocks
    private PosOperationLogSyncServiceImpl operationLogSyncService;

    @BeforeEach
    void setUp() {
        // 设置测试配置
        ReflectionTestUtils.setField(operationLogSyncService, "apiBaseUrl", "http://rt-pos-api.beta1.fn");
        ReflectionTestUtils.setField(operationLogSyncService, "operationLogPath", "/api/pos/getOperationLogInfo");
        ReflectionTestUtils.setField(operationLogSyncService, "batchSize", 20);
        ReflectionTestUtils.setField(operationLogSyncService, "maxPages", 50);
        ReflectionTestUtils.setField(operationLogSyncService, "configuredStoreNos", "1001");
        ReflectionTestUtils.setField(operationLogSyncService, "configuredPosNos", "895");
    }

    @Test
    void testSyncOperationLogsByStoreAndPos() {
        // 准备测试数据
        Integer storeNo = 1001;
        Integer posNo = 895;
        Long startTime = System.currentTimeMillis() - 3600000L; // 1小时前
        Long endTime = System.currentTimeMillis();

        // 模拟API响应
        PosOperationLogQueryResponse mockResponse = createMockResponse();
        when(restTemplate.exchange(anyString(), any(), any(), eq(PosOperationLogQueryResponse.class)))
                .thenReturn(org.springframework.http.ResponseEntity.ok(mockResponse));

        // 模拟数据库查询
        when(operationLogRepository.findByUniqueKey(anyInt(), anyInt(), anyLong(), anyInt()))
                .thenReturn(Optional.empty());

        // 模拟数据库保存
        when(operationLogRepository.save(any(PosOperationLog.class)))
                .thenReturn(new PosOperationLog());

        // 执行测试
        assertDoesNotThrow(() -> {
            operationLogSyncService.syncOperationLogsByStoreAndPos(storeNo, posNo, startTime, endTime);
        });

        // 验证调用
        verify(restTemplate, atLeastOnce()).exchange(anyString(), any(), any(), eq(PosOperationLogQueryResponse.class));
        verify(operationLogRepository, atLeastOnce()).save(any(PosOperationLog.class));
    }

    @Test
    void testIncrementalSyncOperationLogs() {
        // 模拟API响应
        PosOperationLogQueryResponse mockResponse = createMockResponse();
        when(restTemplate.exchange(anyString(), any(), any(), eq(PosOperationLogQueryResponse.class)))
                .thenReturn(org.springframework.http.ResponseEntity.ok(mockResponse));

        // 模拟数据库操作
        when(operationLogRepository.findByUniqueKey(anyInt(), anyInt(), anyLong(), anyInt()))
                .thenReturn(Optional.empty());
        when(operationLogRepository.save(any(PosOperationLog.class)))
                .thenReturn(new PosOperationLog());

        // 执行测试
        assertDoesNotThrow(() -> {
            operationLogSyncService.incrementalSyncOperationLogs();
        });

        // 验证调用
        verify(restTemplate, atLeastOnce()).exchange(anyString(), any(), any(), eq(PosOperationLogQueryResponse.class));
    }

    @Test
    void testBatchSyncOperationLogs() {
        // 准备测试数据
        String storeNos = "1001,1002";
        String posNos = "895,896";
        Long startTime = System.currentTimeMillis() - 3600000L;
        Long endTime = System.currentTimeMillis();

        // 模拟API响应
        PosOperationLogQueryResponse mockResponse = createMockResponse();
        when(restTemplate.exchange(anyString(), any(), any(), eq(PosOperationLogQueryResponse.class)))
                .thenReturn(org.springframework.http.ResponseEntity.ok(mockResponse));

        // 模拟数据库操作
        when(operationLogRepository.findByUniqueKey(anyInt(), anyInt(), anyLong(), anyInt()))
                .thenReturn(Optional.empty());
        when(operationLogRepository.save(any(PosOperationLog.class)))
                .thenReturn(new PosOperationLog());

        // 执行测试
        assertDoesNotThrow(() -> {
            operationLogSyncService.batchSyncOperationLogs(storeNos, posNos, startTime, endTime);
        });

        // 验证调用次数（2个门店 * 2个POS机 = 4次调用）
        verify(restTemplate, times(4)).exchange(anyString(), any(), any(), eq(PosOperationLogQueryResponse.class));
    }

    @Test
    void testSyncOperationLogsFromApiWithValidRequest() {
        // 准备测试数据
        PosOperationLogQueryRequest request = new PosOperationLogQueryRequest();
        request.setStoreNo(1001);
        request.setPosNo(895);
        request.setBeginTime(System.currentTimeMillis() - 3600000L);
        request.setEndTime(System.currentTimeMillis());
        request.setLimit(20);
        request.setPage(1);

        // 模拟API响应
        PosOperationLogQueryResponse mockResponse = createMockResponse();
        when(restTemplate.exchange(anyString(), any(), any(), eq(PosOperationLogQueryResponse.class)))
                .thenReturn(org.springframework.http.ResponseEntity.ok(mockResponse));

        // 模拟数据库操作
        when(operationLogRepository.findByUniqueKey(anyInt(), anyInt(), anyLong(), anyInt()))
                .thenReturn(Optional.empty());
        when(operationLogRepository.save(any(PosOperationLog.class)))
                .thenReturn(new PosOperationLog());

        // 执行测试
        PosOperationLogQueryResponse result = operationLogSyncService.syncOperationLogsFromApi(request);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("Success", result.getMsg());
        assertNotNull(result.getOperationList());
        assertEquals(1, result.getOperationList().size());

        // 验证调用
        verify(restTemplate, times(1)).exchange(anyString(), any(), any(), eq(PosOperationLogQueryResponse.class));
        verify(operationLogRepository, times(1)).save(any(PosOperationLog.class));
    }

    /**
     * 创建模拟响应数据
     */
    private PosOperationLogQueryResponse createMockResponse() {
        PosOperationLogQueryResponse response = new PosOperationLogQueryResponse();
        response.setRsCode("00000000");
        response.setMsg("Success");
        response.setTraceId("test-trace-id");

        PosOperationLogQueryResponse.PosOperationLogResponseBody body = 
                new PosOperationLogQueryResponse.PosOperationLogResponseBody();
        body.setTotal(1);

        // 创建模拟操作日志
        com.rtpos.server.dto.PosOperationLogDTO logDTO = new com.rtpos.server.dto.PosOperationLogDTO();
        logDTO.setStoreNo(1001);
        logDTO.setPosNo(895);
        logDTO.setDeviceNo("8bf46c77-e33c-3534-8a5a-efa1602daade");
        logDTO.setPosType(1);
        logDTO.setOperationType(10005);
        logDTO.setFlowId("test-flow-id");
        logDTO.setShiftNo("250610094215");
        logDTO.setOrderId("810018951749519777");
        logDTO.setMembershipCardId("");
        logDTO.setOccurrenceTime(System.currentTimeMillis());
        logDTO.setWorkTime("");

        body.setOperationList(java.util.Arrays.asList(logDTO));
        response.setBody(body);

        return response;
    }
}
