package com.rtpos.server.service;

import com.rtpos.server.dto.PosStoreStatusBody;
import com.rtpos.server.dto.PosStoreStatusRequest;
import com.rtpos.server.dto.PosStoreStatusResponse;
import com.rtpos.server.service.impl.PosStoreStatusServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * POS门店状态服务测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class PosStoreStatusServiceTest {

    @Mock
    private RestTemplate restTemplate;

    @InjectMocks
    private PosStoreStatusServiceImpl storeStatusService;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(storeStatusService, "apiBaseUrl", "http://test-api.example.com");
        ReflectionTestUtils.setField(storeStatusService, "storeStatusPath", "/api/pos/getPosByStore");
    }

    @Test
    void testGetStoreStatus_Success() {
        // 准备测试数据
        PosStoreStatusRequest request = new PosStoreStatusRequest();
        request.setStoreNo(1001);

        // 模拟外部API响应
        PosStoreStatusResponse mockResponse = new PosStoreStatusResponse();
        mockResponse.setRsCode("00000000");
        mockResponse.setMsg("Success");
        
        PosStoreStatusBody body = new PosStoreStatusBody();
        body.setStoreAllPosCount(105);
        body.setAllOnlPosCount(12000);
        body.setAllOffPosCount(8999);
        body.setOnline(Arrays.asList(14, 144, 213, 228, 266, 285, 342, 389, 666, 859));
        body.setOffline(Arrays.asList(1, 19, 21, 87, 995, 996, 997, 998, 999));
        body.setLogin(Arrays.asList(144, 213, 285, 389, 859, 996));
        body.setLogout(Arrays.asList(1, 14, 19, 21, 87, 112, 123, 999));
        
        mockResponse.setBody(body);
        mockResponse.setTraceId(null);

        // 模拟RestTemplate调用
        when(restTemplate.exchange(
                anyString(),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(PosStoreStatusResponse.class)
        )).thenReturn(ResponseEntity.ok(mockResponse));

        // 执行测试
        PosStoreStatusResponse result = storeStatusService.getStoreStatus(request);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("00000000", result.getRsCode());
        assertEquals("Success", result.getMsg());
        
        assertNotNull(result.getBody());
        assertEquals(105, result.getBody().getStoreAllPosCount());
        assertEquals(12000, result.getBody().getAllOnlPosCount());
        assertEquals(8999, result.getBody().getAllOffPosCount());
        
        assertNotNull(result.getBody().getOnline());
        assertEquals(10, result.getBody().getOnline().size());
        assertTrue(result.getBody().getOnline().contains(144));
        
        assertNotNull(result.getBody().getOffline());
        assertEquals(9, result.getBody().getOffline().size());
        assertTrue(result.getBody().getOffline().contains(995));
        
        assertNotNull(result.getBody().getLogin());
        assertEquals(6, result.getBody().getLogin().size());
        assertTrue(result.getBody().getLogin().contains(996));
        
        assertNotNull(result.getBody().getLogout());
        assertEquals(8, result.getBody().getLogout().size());
        assertTrue(result.getBody().getLogout().contains(112));
    }

    @Test
    void testGetStoreStatus_ApiError() {
        // 准备测试数据
        PosStoreStatusRequest request = new PosStoreStatusRequest();
        request.setStoreNo(1001);

        // 模拟外部API错误响应
        PosStoreStatusResponse mockResponse = new PosStoreStatusResponse();
        mockResponse.setRsCode("99999999");
        mockResponse.setMsg("门店不存在");

        // 模拟RestTemplate调用
        when(restTemplate.exchange(
                anyString(),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(PosStoreStatusResponse.class)
        )).thenReturn(ResponseEntity.ok(mockResponse));

        // 执行测试
        PosStoreStatusResponse result = storeStatusService.getStoreStatus(request);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("99999999", result.getRsCode());
        assertEquals("门店不存在", result.getMsg());
    }

    @Test
    void testGetStoreStatus_Exception() {
        // 准备测试数据
        PosStoreStatusRequest request = new PosStoreStatusRequest();
        request.setStoreNo(1001);

        // 模拟RestTemplate抛出异常
        when(restTemplate.exchange(
                anyString(),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(PosStoreStatusResponse.class)
        )).thenThrow(new RuntimeException("网络连接失败"));

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            storeStatusService.getStoreStatus(request);
        });

        assertTrue(exception.getMessage().contains("获取门店状态信息失败"));
        assertTrue(exception.getMessage().contains("网络连接失败"));
    }
}
