// 模拟数据

const storeList = [
  { id: 'store001', name: '北京朝阳门店' },
  { id: 'store002', name: '上海南京路店' },
  { id: 'store003', name: '广州天河店' },
  { id: 'store004', name: '深圳福田店' },
  { id: 'store005', name: '成都春熙路店' }
]

// 首页概览数据
const dashboardData = {
  totalDevices: 12000,
  onlineDevices: 10240,
  manualPosCount: 6000,
  selfServicePosCount: 6000,
  averageUsageRate: 45.2,
  dailyPowerConsumption: 18720,
  monthlySavings: 570000,
  yearlyProjectedSavings: 6830000,
  storeCount: 500,
  // 各时段订单量概览
  hourlyOrderTrend: [
    { hour: '08:00', manual: 120, selfService: 80 },
    { hour: '09:00', manual: 180, selfService: 120 },
    { hour: '10:00', manual: 240, selfService: 150 },
    { hour: '11:00', manual: 300, selfService: 180 },
    { hour: '12:00', manual: 420, selfService: 260 },
    { hour: '13:00', manual: 380, selfService: 240 },
    { hour: '14:00', manual: 280, selfService: 180 },
    { hour: '15:00', manual: 250, selfService: 150 },
    { hour: '16:00', manual: 300, selfService: 160 },
    { hour: '17:00', manual: 350, selfService: 200 },
    { hour: '18:00', manual: 420, selfService: 260 },
    { hour: '19:00', manual: 450, selfService: 300 },
    { hour: '20:00', manual: 380, selfService: 250 },
    { hour: '21:00', manual: 250, selfService: 180 }
  ],
  // 设备使用率趋势
  usageRateTrend: [
    { date: '07-01', manual: 42, selfService: 38 },
    { date: '07-02', manual: 43, selfService: 39 },
    { date: '07-03', manual: 45, selfService: 40 },
    { date: '07-04', manual: 44, selfService: 41 },
    { date: '07-05', manual: 46, selfService: 42 },
    { date: '07-06', manual: 47, selfService: 43 },
    { date: '07-07', manual: 48, selfService: 44 }
  ],
  // 设备状态分布
  deviceStatusDistribution: {
    manual: {
      active: 2400, // 使用中
      idle: 2800,   // 闲置
      offline: 800  // 离线
    },
    selfService: {
      active: 1800,
      idle: 3400,
      offline: 800
    }
  },
  // 能耗节约趋势
  energySavingTrend: [
    { month: '1月', saving: 520000 },
    { month: '2月', saving: 530000 },
    { month: '3月', saving: 545000 },
    { month: '4月', saving: 550000 },
    { month: '5月', saving: 560000 },
    { month: '6月', saving: 565000 },
    { month: '7月', saving: 570000 }
  ]
}

// POS使用率分析数据
const posUsageData = [
  {
    storeId: 'store001',
    storeName: '北京朝阳门店',
    date: '2023-07-07',
    manual: {
      totalHours: 144,  // 总运行时长(小时)
      activeHours: 62,  // 有效使用时长(小时)
      usageRate: 43.1,  // 使用率(%)
      devices: [
        { deviceId: 'M001', usageRate: 45.2, activeHours: 5.4, idleHours: 6.6 },
        { deviceId: 'M002', usageRate: 42.5, activeHours: 5.1, idleHours: 6.9 },
        { deviceId: 'M003', usageRate: 38.3, activeHours: 4.6, idleHours: 7.4 },
        { deviceId: 'M004', usageRate: 47.5, activeHours: 5.7, idleHours: 6.3 },
        { deviceId: 'M005', usageRate: 41.7, activeHours: 5.0, idleHours: 7.0 },
        { deviceId: 'M006', usageRate: 46.7, activeHours: 5.6, idleHours: 6.4 }
      ]
    },
    selfService: {
      totalHours: 120,  // 总运行时长(小时)
      activeHours: 42,  // 有效使用时长(小时)
      usageRate: 35.0,  // 使用率(%)
      devices: [
        { deviceId: 'S001', usageRate: 36.7, activeHours: 4.4, idleHours: 7.6 },
        { deviceId: 'S002', usageRate: 32.5, activeHours: 3.9, idleHours: 8.1 },
        { deviceId: 'S003', usageRate: 34.2, activeHours: 4.1, idleHours: 7.9 },
        { deviceId: 'S004', usageRate: 37.5, activeHours: 4.5, idleHours: 7.5 },
        { deviceId: 'S005', usageRate: 34.2, activeHours: 4.1, idleHours: 7.9 }
      ]
    },
    // 各小时段使用率
    hourlyUsage: [
      { hour: '08:00', manual: 20, selfService: 15 },
      { hour: '09:00', manual: 35, selfService: 25 },
      { hour: '10:00', manual: 45, selfService: 30 },
      { hour: '11:00', manual: 60, selfService: 40 },
      { hour: '12:00', manual: 75, selfService: 55 },
      { hour: '13:00', manual: 70, selfService: 50 },
      { hour: '14:00', manual: 50, selfService: 35 },
      { hour: '15:00', manual: 45, selfService: 30 },
      { hour: '16:00', manual: 55, selfService: 35 },
      { hour: '17:00', manual: 65, selfService: 45 },
      { hour: '18:00', manual: 75, selfService: 55 },
      { hour: '19:00', manual: 80, selfService: 60 },
      { hour: '20:00', manual: 70, selfService: 50 },
      { hour: '21:00', manual: 45, selfService: 35 }
    ]
  },
  // 更多门店数据...
  {
    storeId: 'store002',
    storeName: '上海南京路店',
    date: '2023-07-07',
    manual: {
      totalHours: 156,
      activeHours: 78,
      usageRate: 50.0,
      devices: [
        { deviceId: 'M007', usageRate: 52.5, activeHours: 6.3, idleHours: 5.7 },
        { deviceId: 'M008', usageRate: 48.3, activeHours: 5.8, idleHours: 6.2 },
        { deviceId: 'M009', usageRate: 50.8, activeHours: 6.1, idleHours: 5.9 },
        { deviceId: 'M010', usageRate: 49.2, activeHours: 5.9, idleHours: 6.1 },
        { deviceId: 'M011', usageRate: 47.5, activeHours: 5.7, idleHours: 6.3 },
        { deviceId: 'M012', usageRate: 51.7, activeHours: 6.2, idleHours: 5.8 }
      ]
    },
    selfService: {
      totalHours: 132,
      activeHours: 58,
      usageRate: 43.9,
      devices: [
        { deviceId: 'S006', usageRate: 45.8, activeHours: 5.5, idleHours: 6.5 },
        { deviceId: 'S007', usageRate: 42.5, activeHours: 5.1, idleHours: 6.9 },
        { deviceId: 'S008', usageRate: 44.2, activeHours: 5.3, idleHours: 6.7 },
        { deviceId: 'S009', usageRate: 45.0, activeHours: 5.4, idleHours: 6.6 },
        { deviceId: 'S010', usageRate: 42.5, activeHours: 5.1, idleHours: 6.9 }
      ]
    },
    hourlyUsage: [
      { hour: '08:00', manual: 25, selfService: 20 },
      { hour: '09:00', manual: 45, selfService: 35 },
      { hour: '10:00', manual: 55, selfService: 40 },
      { hour: '11:00', manual: 65, selfService: 45 },
      { hour: '12:00', manual: 80, selfService: 60 },
      { hour: '13:00', manual: 75, selfService: 55 },
      { hour: '14:00', manual: 60, selfService: 45 },
      { hour: '15:00', manual: 55, selfService: 40 },
      { hour: '16:00', manual: 60, selfService: 45 },
      { hour: '17:00', manual: 70, selfService: 50 },
      { hour: '18:00', manual: 85, selfService: 65 },
      { hour: '19:00', manual: 90, selfService: 70 },
      { hour: '20:00', manual: 75, selfService: 55 },
      { hour: '21:00', manual: 50, selfService: 40 }
    ]
  }
]

// 订单量时段分析数据
const orderAnalysisData = {
  // 今日订单量分时段分布
  todayOrders: [
    { hour: '08:00', total: 200, manual: 120, selfService: 80, canceled: 5 },
    { hour: '09:00', total: 300, manual: 180, selfService: 120, canceled: 8 },
    { hour: '10:00', total: 390, manual: 240, selfService: 150, canceled: 10 },
    { hour: '11:00', total: 480, manual: 300, selfService: 180, canceled: 12 },
    { hour: '12:00', total: 680, manual: 420, selfService: 260, canceled: 15 },
    { hour: '13:00', total: 620, manual: 380, selfService: 240, canceled: 14 },
    { hour: '14:00', total: 460, manual: 280, selfService: 180, canceled: 10 },
    { hour: '15:00', total: 400, manual: 250, selfService: 150, canceled: 8 },
    { hour: '16:00', total: 460, manual: 300, selfService: 160, canceled: 9 },
    { hour: '17:00', total: 550, manual: 350, selfService: 200, canceled: 11 },
    { hour: '18:00', total: 680, manual: 420, selfService: 260, canceled: 15 },
    { hour: '19:00', total: 750, manual: 450, selfService: 300, canceled: 18 },
    { hour: '20:00', total: 630, manual: 380, selfService: 250, canceled: 14 },
    { hour: '21:00', total: 430, manual: 250, selfService: 180, canceled: 10 }
  ],
  // 历史同期对比
  compareWithHistory: {
    // 工作日对比（周一到周五）
    weekday: [
      { hour: '08:00', today: 200, lastWeek: 190, avg: 185 },
      { hour: '09:00', today: 300, lastWeek: 280, avg: 275 },
      { hour: '10:00', today: 390, lastWeek: 370, avg: 360 },
      { hour: '11:00', today: 480, lastWeek: 450, avg: 440 },
      { hour: '12:00', today: 680, lastWeek: 650, avg: 630 },
      { hour: '13:00', today: 620, lastWeek: 590, avg: 580 },
      { hour: '14:00', today: 460, lastWeek: 440, avg: 430 },
      { hour: '15:00', today: 400, lastWeek: 380, avg: 370 },
      { hour: '16:00', today: 460, lastWeek: 430, avg: 420 },
      { hour: '17:00', today: 550, lastWeek: 520, avg: 510 },
      { hour: '18:00', today: 680, lastWeek: 640, avg: 630 },
      { hour: '19:00', today: 750, lastWeek: 710, avg: 700 },
      { hour: '20:00', today: 630, lastWeek: 600, avg: 590 },
      { hour: '21:00', today: 430, lastWeek: 410, avg: 400 }
    ],
    // 周末对比
    weekend: [
      { hour: '08:00', today: 230, lastWeek: 220, avg: 210 },
      { hour: '09:00', today: 340, lastWeek: 320, avg: 310 },
      { hour: '10:00', today: 450, lastWeek: 430, avg: 420 },
      { hour: '11:00', today: 550, lastWeek: 520, avg: 510 },
      { hour: '12:00', today: 750, lastWeek: 720, avg: 700 },
      { hour: '13:00', today: 690, lastWeek: 660, avg: 650 },
      { hour: '14:00', today: 520, lastWeek: 500, avg: 490 },
      { hour: '15:00', today: 460, lastWeek: 440, avg: 430 },
      { hour: '16:00', today: 530, lastWeek: 500, avg: 490 },
      { hour: '17:00', today: 620, lastWeek: 590, avg: 580 },
      { hour: '18:00', today: 750, lastWeek: 720, avg: 700 },
      { hour: '19:00', today: 820, lastWeek: 790, avg: 770 },
      { hour: '20:00', today: 700, lastWeek: 670, avg: 660 },
      { hour: '21:00', today: 500, lastWeek: 480, avg: 470 }
    ]
  },
  // 高峰期分析
  peakAnalysis: {
    morningPeak: { startTime: '11:00', endTime: '13:00', avgOrders: 680 },
    eveningPeak: { startTime: '18:00', endTime: '20:00', avgOrders: 750 },
    lunchPeak: { startTime: '12:00', endTime: '13:00', avgOrders: 680 }
  },
  // 各门店订单量对比
  storeComparison: [
    { storeId: 'store001', storeName: '北京朝阳门店', totalOrders: 7630, manualOrders: 4640, selfServiceOrders: 2990 },
    { storeId: 'store002', storeName: '上海南京路店', totalOrders: 8250, manualOrders: 4950, selfServiceOrders: 3300 },
    { storeId: 'store003', storeName: '广州天河店', totalOrders: 7120, manualOrders: 4270, selfServiceOrders: 2850 },
    { storeId: 'store004', storeName: '深圳福田店', totalOrders: 7380, manualOrders: 4430, selfServiceOrders: 2950 },
    { storeId: 'store005', storeName: '成都春熙路店', totalOrders: 6850, manualOrders: 4110, selfServiceOrders: 2740 }
  ]
}

// 设备状态监控数据
const deviceStatusData = {
  totalDevices: 120,
  onlineDevices: 110,
  activeDevices: 58,
  abnormalDevices: 3,
  manualCount: 60,
  selfServiceCount: 60,
  devices: [
    { deviceId: 'M001', type: 'manual', location: '收银区1号', status: 'active', lastActiveTime: '2023-07-07 12:35:42', runningTime: '10小时25分钟' },
    { deviceId: 'M002', type: 'manual', location: '收银区2号', status: 'idle', lastActiveTime: '2023-07-07 11:12:18', runningTime: '10小时25分钟' },
    { deviceId: 'M003', type: 'manual', location: '收银区3号', status: 'idle', lastActiveTime: '2023-07-07 11:47:56', runningTime: '10小时25分钟' },
    { deviceId: 'M004', type: 'manual', location: '收银区4号', status: 'active', lastActiveTime: '2023-07-07 12:38:21', runningTime: '10小时25分钟' },
    { deviceId: 'M005', type: 'manual', location: '收银区5号', status: 'offline', lastActiveTime: '2023-07-06 21:05:12', runningTime: '0小时0分钟' },
    { deviceId: 'S001', type: 'selfService', location: '自助区1号', status: 'active', lastActiveTime: '2023-07-07 12:32:18', runningTime: '10小时25分钟' },
    { deviceId: 'S002', type: 'selfService', location: '自助区2号', status: 'idle', lastActiveTime: '2023-07-07 10:48:35', runningTime: '10小时25分钟' },
    { deviceId: 'S003', type: 'selfService', location: '自助区3号', status: 'abnormal', lastActiveTime: '2023-07-07 09:23:11', runningTime: '9小时15分钟' },
    { deviceId: 'S004', type: 'selfService', location: '自助区4号', status: 'active', lastActiveTime: '2023-07-07 12:40:53', runningTime: '10小时25分钟' },
    { deviceId: 'S005', type: 'selfService', location: '自助区5号', status: 'idle', lastActiveTime: '2023-07-07 11:35:27', runningTime: '10小时25分钟' }
  ]
}

// 能耗优化建议数据
const energyOptimizationData = {
  dailyConsumption: 560,
  dailyChange: -5.2,
  monthlyConsumption: 16800,
  monthlyChange: -8.3,
  projectedConsumption: 480,
  savingPercentage: 14.3,
  yearlySavingEnergy: 29200,
  yearlySavingCost: 175000,
  optimizationSuggestions: [
    {
      title: '非营业时段POS自动休眠',
      type: '软件调整',
      description: '通过软件升级，使POS机在非营业时段自动进入深度休眠状态，减少待机能耗。特别是在夜间21:00至次日7:00期间。',
      potentialSaving: 120,
      costSaving: 26280,
      difficulty: 2,
      roi: 1,
      investment: 5000
    },
    {
      title: '替换高能耗旧设备',
      type: '硬件更新',
      description: '将运行超过5年的老旧POS设备更换为新一代节能设备，平均每台可节省40%的能耗。',
      potentialSaving: 180,
      costSaving: 39400,
      difficulty: 4,
      roi: 18,
      investment: 60000
    },
    {
      title: '优化POS开机轮次',
      type: '运营优化',
      description: '根据客流量预测，调整POS机开机数量和时间。在客流低谷期仅保持60%的设备运行，高峰期再开启全部设备。',
      potentialSaving: 85,
      costSaving: 18600,
      difficulty: 3,
      roi: 3,
      investment: 8000
    },
    {
      title: '培训收银员节能操作',
      type: '使用习惯',
      description: '对收银员进行培训，指导正确使用POS设备的节能模式，减少不必要的高能耗操作，如在不使用时及时进入待机状态。',
      potentialSaving: 45,
      costSaving: 9850,
      difficulty: 1,
      roi: 2,
      investment: 3000
    }
  ],
  deviceConsumptionRanking: [
    { rank: 1, deviceId: 'M005', type: 'manual', location: '收银区5号', consumption: 6.8, usageRate: 32.5, optimizationStatus: 'not_optimized' },
    { rank: 2, deviceId: 'S003', type: 'selfService', location: '自助区3号', consumption: 6.5, usageRate: 28.3, optimizationStatus: 'not_optimized' },
    { rank: 3, deviceId: 'M002', type: 'manual', location: '收银区2号', consumption: 6.2, usageRate: 42.5, optimizationStatus: 'pending' },
    { rank: 4, deviceId: 'S001', type: 'selfService', location: '自助区1号', consumption: 5.9, usageRate: 45.8, optimizationStatus: 'pending' },
    { rank: 5, deviceId: 'M001', type: 'manual', location: '收银区1号', consumption: 5.8, usageRate: 48.2, optimizationStatus: 'optimized' },
    { rank: 6, deviceId: 'S002', type: 'selfService', location: '自助区2号', consumption: 5.5, usageRate: 38.7, optimizationStatus: 'optimized' },
    { rank: 7, deviceId: 'M003', type: 'manual', location: '收银区3号', consumption: 5.4, usageRate: 44.5, optimizationStatus: 'optimized' },
    { rank: 8, deviceId: 'S004', type: 'selfService', location: '自助区4号', consumption: 5.2, usageRate: 47.3, optimizationStatus: 'optimized' },
    { rank: 9, deviceId: 'M004', type: 'manual', location: '收银区4号', consumption: 5.0, usageRate: 51.2, optimizationStatus: 'optimized' },
    { rank: 10, deviceId: 'S005', type: 'selfService', location: '自助区5号', consumption: 4.8, usageRate: 42.8, optimizationStatus: 'optimized' }
  ]
}

// 能耗数据
const energyConsumptionData = {
  dailyConsumption: 560,
  monthlyConsumption: 16800,
  yearlyConsumption: 204000,
  devices: {
    manual: {
      count: 60,
      totalConsumption: 320,
      avgPerDevice: 5.33
    },
    selfService: {
      count: 60,
      totalConsumption: 240,
      avgPerDevice: 4.0
    }
  },
  // 各小时段能耗
  hourlyConsumption: [
    { hour: '08:00', manual: 18, selfService: 15 },
    { hour: '09:00', manual: 25, selfService: 18 },
    { hour: '10:00', manual: 28, selfService: 20 },
    { hour: '11:00', manual: 30, selfService: 22 },
    { hour: '12:00', manual: 35, selfService: 26 },
    { hour: '13:00', manual: 32, selfService: 24 },
    { hour: '14:00', manual: 25, selfService: 18 },
    { hour: '15:00', manual: 22, selfService: 16 },
    { hour: '16:00', manual: 24, selfService: 17 },
    { hour: '17:00', manual: 28, selfService: 20 },
    { hour: '18:00', manual: 32, selfService: 24 },
    { hour: '19:00', manual: 35, selfService: 25 },
    { hour: '20:00', manual: 30, selfService: 22 },
    { hour: '21:00', manual: 20, selfService: 15 }
  ],
  // 与历史同期对比
  historicalComparison: {
    daily: [
      { date: '07-01', consumption: 580 },
      { date: '07-02', consumption: 575 },
      { date: '07-03', consumption: 570 },
      { date: '07-04', consumption: 568 },
      { date: '07-05', consumption: 565 },
      { date: '07-06', consumption: 562 },
      { date: '07-07', consumption: 560 }
    ],
    monthly: [
      { month: '2023-01', consumption: 18500 },
      { month: '2023-02', consumption: 18200 },
      { month: '2023-03', consumption: 18000 },
      { month: '2023-04', consumption: 17800 },
      { month: '2023-05', consumption: 17500 },
      { month: '2023-06', consumption: 17000 },
      { month: '2023-07', consumption: 16800 }
    ]
  },
  // 各设备类型能耗占比
  consumptionByType: [
    { type: '人工POS', percentage: 57 },
    { type: '自助POS', percentage: 43 }
  ],
  // 节能效果分析
  savingAnalysis: {
    beforeOptimization: 650,
    afterOptimization: 560,
    savingPercentage: 13.8,
    monthlySaving: 2700,
    yearlySaving: 32850,
    costSaving: 19700
  }
}

export default {
  storeList,
  dashboardData,
  posUsageData,
  orderAnalysisData,
  deviceStatusData,
  energyOptimizationData,
  energyConsumptionData
} 