<template>
  <div class="dashboard-container">
    <div class="page-header">
      <div class="left-section">
        <h1>POS看盘中台</h1>
        <RegionSelector @store-selected="handleStoreSelected" />
      </div>
      <div class="right-section">
        <el-date-picker
          v-model="currentDate"
          type="date"
          placeholder="选择日期"
        />
      </div>
    </div>

    <!-- 概览卡片 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card shadow="hover" class="overview-card">
          <div class="card-title">在线设备数</div>
          <div class="card-value">
            {{ dashboardData.onlineDevices.toLocaleString() }}
          </div>
          <div class="card-ratio">
            总设备: {{ dashboardData.totalDevices.toLocaleString() }}
            <el-progress
              :percentage="
                Math.round(
                  (dashboardData.onlineDevices / dashboardData.totalDevices) *
                    100
                )
              "
              :stroke-width="6"
              status="success"
            />
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="overview-card">
          <div class="card-title">平均使用率</div>
          <div class="card-value">{{ dashboardData.averageUsageRate }}%</div>
          <div class="card-ratio">
            <div>人工POS: {{ getDailyRateForType("manual") }}%</div>
            <el-progress
              :percentage="getDailyRateForType('manual')"
              :stroke-width="6"
              color="#409EFF"
            />
            <div>自助POS: {{ getDailyRateForType("selfService") }}%</div>
            <el-progress
              :percentage="getDailyRateForType('selfService')"
              :stroke-width="6"
              color="#67C23A"
            />
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="overview-card">
          <div class="card-title">日耗电量预估</div>
          <div class="card-value">
            {{ dashboardData.dailyPowerConsumption }} kWh
          </div>
          <div class="card-ratio">
            <div>
              预计年节电:
              {{
                (dashboardData.yearlyProjectedSavings / 10000).toFixed(2)
              }}万度
            </div>
            <div>
              每日预计节约: ¥{{
                (dashboardData.dailyPowerConsumption * 0.4).toLocaleString()
              }}
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="overview-card">
          <div class="card-title">设备在线状态</div>
          <div class="card-value">
            {{
              Math.round(
                (dashboardData.onlineDevices / dashboardData.totalDevices) * 100
              )
            }}%
          </div>
          <div class="card-ratio">
            <div>
              人工POS:
              {{
                dashboardData.deviceStatusDistribution.manual.active +
                dashboardData.deviceStatusDistribution.manual.idle
              }}/{{ dashboardData.manualPosCount }}
            </div>
            <div>
              自助POS:
              {{
                dashboardData.deviceStatusDistribution.selfService.active +
                dashboardData.deviceStatusDistribution.selfService.idle
              }}/{{ dashboardData.selfServicePosCount }}
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 订单量/时间柱状图 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="16">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>订单量时段分布</span>
              <div class="header-actions">
                <el-radio-group v-model="orderChartType" size="small">
                  <el-radio-button label="today">今日</el-radio-button>
                  <el-radio-button label="week">本周</el-radio-button>
                  <el-radio-button label="month">本月</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <div class="chart-container">
            <OrderTimeChart :chart-data="dashboardData.hourlyOrderTrend" />
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>设备使用状态分布</span>
            </div>
          </template>
          <div class="chart-container">
            <DeviceStatusPieChart
              :chart-data="dashboardData.deviceStatusDistribution"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 使用率趋势图 & 能耗节约趋势 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>设备使用率趋势</span>
              <div class="header-actions">
                <el-select
                  v-model="usageTrendPeriod"
                  size="small"
                  placeholder="选择时间范围"
                >
                  <el-option label="最近7天" value="week" />
                  <el-option label="最近30天" value="month" />
                </el-select>
              </div>
            </div>
          </template>
          <div class="chart-container">
            <UsageRateTrendChart :chart-data="dashboardData.usageRateTrend" />
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>能耗节约趋势</span>
            </div>
          </template>
          <div class="chart-container">
            <EnergySavingTrendChart
              :chart-data="dashboardData.energySavingTrend"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 门店TOP5 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="24">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>门店使用效率TOP5</span>
            </div>
          </template>
          <el-table :data="storeTopData" style="width: 100%">
            <el-table-column prop="rank" label="排名" width="80" />
            <el-table-column prop="storeName" label="门店名称" />
            <el-table-column prop="usageRate" label="设备使用率">
              <template #default="scope">
                <div class="usage-rate-cell">
                  {{ scope.row.usageRate }}%
                  <el-progress
                    :percentage="scope.row.usageRate"
                    :stroke-width="6"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="orderCount" label="日订单量" />
            <el-table-column prop="energySaving" label="节能效果">
              <template #default="scope">
                {{ scope.row.energySaving }} kWh/天
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { getDashboardData, getPosStatus } from "@/api";
import OrderTimeChart from "@/components/charts/OrderTimeChart.vue";
import DeviceStatusPieChart from "@/components/charts/DeviceStatusPieChart.vue";
import UsageRateTrendChart from "@/components/charts/UsageRateTrendChart.vue";
import EnergySavingTrendChart from "@/components/charts/EnergySavingTrendChart.vue";
import RegionSelector from "@/components/RegionSelector.vue";

// 状态
const currentDate = ref(new Date());
const dashboardData = ref({
  totalDevices: 0,
  onlineDevices: 0,
  manualPosCount: 0,
  selfServicePosCount: 0,
  averageUsageRate: 0,
  dailyPowerConsumption: 0,
  monthlyPowerConsumption: 0,
  yearlyProjectedSavings: 0,
  hourlyOrderTrend: [],
  usageRateTrend: [],
  deviceStatusDistribution: {
    manual: { active: 0, idle: 0, offline: 0 },
    selfService: { active: 0, idle: 0, offline: 0 },
  },
  energySavingTrend: [],
});

const orderChartType = ref("today");
const usageTrendPeriod = ref("week");
const currentStoreId = ref("1001"); // 当前选中的门店ID

// 门店TOP5数据
const storeTopData = ref([
  {
    rank: 1,
    storeName: "上海南京路店",
    usageRate: 56.3,
    orderCount: 8250,
    energySaving: 9.6,
  },
  {
    rank: 2,
    storeName: "北京朝阳门店",
    usageRate: 52.1,
    orderCount: 7630,
    energySaving: 8.4,
  },
  {
    rank: 3,
    storeName: "深圳福田店",
    usageRate: 48.9,
    orderCount: 7380,
    energySaving: 8.4,
  },
  {
    rank: 4,
    storeName: "广州天河店",
    usageRate: 46.7,
    orderCount: 7120,
    energySaving: 7.2,
  },
  {
    rank: 5,
    storeName: "成都春熙路店",
    usageRate: 44.2,
    orderCount: 6850,
    energySaving: 7.2,
  },
]);

// 方法
const getDailyRateForType = (type) => {
  if (
    !dashboardData.value.usageRateTrend ||
    dashboardData.value.usageRateTrend.length === 0
  ) {
    return 0;
  }
  // 获取最新的使用率数据
  const latestData =
    dashboardData.value.usageRateTrend[
      dashboardData.value.usageRateTrend.length - 1
    ];
  return latestData[type];
};

const handleRegionSelected = (region) => {
  console.log("Selected region:", region);
  // Perform actions based on the selected region
};

// 刷新Dashboard数据的方法
const refreshDashboardData = async (storeId = null, timeRange = "today") => {
  const targetStoreId = storeId || currentStoreId.value;
  try {
    console.log(`刷新Dashboard数据: 门店${targetStoreId}, 时间范围${timeRange}`);
    const data = await getDashboardData(targetStoreId, null, timeRange);
    dashboardData.value = data;
    console.log("Dashboard数据刷新成功:", data);
  } catch (error) {
    console.error("刷新Dashboard数据失败:", error);
  }
};

// 处理时间范围切换
const handleTimeRangeChange = async (newTimeRange) => {
  orderChartType.value = newTimeRange;
  await refreshDashboardData(currentStoreId.value, newTimeRange);
};

// 处理使用率趋势周期切换
const handleUsageTrendPeriodChange = async (newPeriod) => {
  usageTrendPeriod.value = newPeriod;
  // 可以根据需要调用不同的时间范围
  const timeRangeMap = {
    'day': 'today',
    'week': 'week',
    'month': 'month'
  };
  await refreshDashboardData(currentStoreId.value, timeRangeMap[newPeriod] || 'week');
};

const handleStoreSelected = async (selection) => {
  console.log("Selected store:", selection);

  if (selection && selection.storeInfo) {
    const { storeInfo } = selection;
    console.log(`选择的门店信息:`, {
      区域: storeInfo.pgName,
      子区域: storeInfo.subArea,
      门店: `${storeInfo.storeId}-${storeInfo.storeName}`,
    });

    // 更新当前选中的门店ID
    currentStoreId.value = storeInfo.storeId;

    // 使用新的Dashboard API获取完整的门店数据
    try {
      const data = await getDashboardData(storeInfo.storeId, null, orderChartType.value);
      dashboardData.value = data;
      console.log("Dashboard数据更新成功:", data);
    } catch (error) {
      console.error("获取门店Dashboard数据失败:", error);

      // 如果Dashboard API失败，回退到原来的逻辑
      try {
        const posStatusData = await getPosStatus(storeInfo.storeId);

        let refreshDash = { ...dashboardData.value };
        refreshDash.totalDevices = posStatusData.storeAllPosCount || (posStatusData.allOnlPosCount + posStatusData.allOffPosCount);
        refreshDash.onlineDevices = posStatusData.allOnlPosCount;
        refreshDash.manualPosCount = posStatusData.manualPosCount;
        refreshDash.selfServicePosCount = posStatusData.selfPosCount;

        // 更新设备状态分布
        refreshDash.deviceStatusDistribution.manual.active = posStatusData.onlManualPosNum;
        refreshDash.deviceStatusDistribution.manual.offline = posStatusData.manualPosCount - posStatusData.onlManualPosNum;
        refreshDash.deviceStatusDistribution.selfService.active = posStatusData.onlSelfPosNum;
        refreshDash.deviceStatusDistribution.selfService.offline = posStatusData.selfPosCount - posStatusData.onlSelfPosNum;

        dashboardData.value = refreshDash;
        console.log("使用POS状态数据更新Dashboard:", refreshDash);
      } catch (posError) {
        console.error("获取POS状态数据也失败:", posError);
      }
    }
  }
};

// 生命周期
onMounted(async () => {
  try {
    // 使用默认门店ID 1001 获取Dashboard数据
    const data = await getDashboardData("1001", null, "today");
    dashboardData.value = data;
    console.log("初始Dashboard数据加载成功:", data);
  } catch (error) {
    console.error("获取仪表盘数据失败:", error);
    // 如果API失败，保持使用模拟数据
  }
});
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .left-section {
      display: flex;
      align-items: center;
      gap: 20px;

      h1 {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
      }
    }

    .right-section {
      display: flex;
      align-items: center;
    }
  }

  .overview-card {
    height: 180px;

    .card-title {
      font-size: 14px;
      color: #606266;
      margin-bottom: 10px;
    }

    .card-value {
      font-size: 28px;
      font-weight: 500;
      margin-bottom: 20px;
    }

    .card-ratio {
      font-size: 12px;
      color: #909399;
    }
  }

  .chart-row {
    margin-top: 20px;
  }

  .chart-container {
    height: 350px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .usage-rate-cell {
    width: 200px;
  }
}
</style>
