<template>
  <div class="energy-consumption">
    <div class="page-header">
      <h2>能耗分析</h2>
      <div class="filter-options">
        <el-select
          v-model="selectedStore"
          placeholder="选择门店"
          @change="handleStoreChange"
        >
          <el-option
            v-for="store in storeList"
            :key="store.id"
            :label="store.name"
            :value="store.id"
          />
        </el-select>
        <el-date-picker
          v-model="selectedDate"
          type="date"
          placeholder="选择日期"
          @change="handleDateChange"
        />
      </div>
    </div>

    <!-- 能耗概览 -->
    <el-row :gutter="20" class="overview-cards">
      <el-col :span="6">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>总能耗</span>
              <el-tag type="success">{{ totalConsumption }}kWh</el-tag>
            </div>
          </template>
          <div class="card-content">
            <div class="trend">
              <span>较昨日</span>
              <span :class="['rate', consumptionChange >= 0 ? 'up' : 'down']">
                {{ Math.abs(consumptionChange) }}%
                <el-icon
                  ><component
                    :is="consumptionChange >= 0 ? 'ArrowUp' : 'ArrowDown'"
                /></el-icon>
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>平均能耗</span>
              <el-tag type="warning">{{ averageConsumption }}kWh/台</el-tag>
            </div>
          </template>
          <div class="card-content">
            <div class="trend">
              <span>较昨日</span>
              <span :class="['rate', averageChange >= 0 ? 'up' : 'down']">
                {{ Math.abs(averageChange) }}%
                <el-icon
                  ><component
                    :is="averageChange >= 0 ? 'ArrowUp' : 'ArrowDown'"
                /></el-icon>
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>节能率</span>
              <el-tag type="success">{{ energySavingRate }}%</el-tag>
            </div>
          </template>
          <div class="card-content">
            <div class="trend">
              <span>较昨日</span>
              <span :class="['rate', savingChange >= 0 ? 'up' : 'down']">
                {{ Math.abs(savingChange) }}%
                <el-icon
                  ><component :is="savingChange >= 0 ? 'ArrowUp' : 'ArrowDown'"
                /></el-icon>
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>节能金额</span>
              <el-tag type="success">¥{{ energySavingAmount }}</el-tag>
            </div>
          </template>
          <div class="card-content">
            <div class="trend">
              <span>较昨日</span>
              <span :class="['rate', amountChange >= 0 ? 'up' : 'down']">
                {{ Math.abs(amountChange) }}%
                <el-icon
                  ><component :is="amountChange >= 0 ? 'ArrowUp' : 'ArrowDown'"
                /></el-icon>
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 能耗趋势图 -->
    <el-card class="chart-card">
      <template #header>
        <div class="card-header">
          <span>能耗趋势</span>
          <el-radio-group v-model="chartType" size="small">
            <el-radio-button label="daily">日趋势</el-radio-button>
            <el-radio-button label="weekly">周趋势</el-radio-button>
            <el-radio-button label="monthly">月趋势</el-radio-button>
          </el-radio-group>
        </div>
      </template>
      <div class="chart-container">
        <div ref="consumptionChart" style="width: 100%; height: 400px"></div>
      </div>
    </el-card>

    <!-- 能耗分布 -->
    <el-row :gutter="20" class="distribution-section">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>设备类型能耗分布</span>
            </div>
          </template>
          <div class="chart-container">
            <div ref="deviceTypeChart" style="width: 100%; height: 300px"></div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>时段能耗分布</span>
            </div>
          </template>
          <div class="chart-container">
            <div
              ref="timeDistributionChart"
              style="width: 100%; height: 300px"
            ></div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 能耗详情表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>能耗详情</span>
        </div>
      </template>
      <el-table :data="energyData" style="width: 100%">
        <el-table-column prop="deviceId" label="设备ID" width="120" />
        <el-table-column prop="deviceType" label="设备类型" width="120">
          <template #default="{ row }">
            <el-tag :type="row.deviceType === 'manual' ? 'primary' : 'success'">
              {{ row.deviceType === "manual" ? "人工POS" : "自助POS" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="totalConsumption"
          label="总能耗(kWh)"
          width="120"
        />
        <el-table-column
          prop="activeConsumption"
          label="工作能耗(kWh)"
          width="120"
        />
        <el-table-column
          prop="idleConsumption"
          label="待机能耗(kWh)"
          width="120"
        />
        <el-table-column prop="energySavingRate" label="节能率" width="120">
          <template #default="{ row }">
            <el-progress
              :percentage="row.energySavingRate"
              :status="row.energySavingRate >= 20 ? 'success' : 'warning'"
            />
          </template>
        </el-table-column>
        <el-table-column prop="savingAmount" label="节能金额(元)" width="120" />
        <el-table-column label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="row.status === 'normal' ? 'success' : 'danger'">
              {{ row.status === "normal" ? "正常" : "异常" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button type="primary" link @click="showDeviceDetail(row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 设备详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="设备能耗详情" width="60%">
      <div v-if="selectedDevice" class="device-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="设备ID">{{
            selectedDevice.deviceId
          }}</el-descriptions-item>
          <el-descriptions-item label="设备类型">
            {{ selectedDevice.deviceType === "manual" ? "人工POS" : "自助POS" }}
          </el-descriptions-item>
          <el-descriptions-item label="总能耗"
            >{{ selectedDevice.totalConsumption }}kWh</el-descriptions-item
          >
          <el-descriptions-item label="节能率"
            >{{ selectedDevice.energySavingRate }}%</el-descriptions-item
          >
          <el-descriptions-item label="节能金额"
            >¥{{ selectedDevice.savingAmount }}</el-descriptions-item
          >
          <el-descriptions-item label="状态">
            <el-tag
              :type="selectedDevice.status === 'normal' ? 'success' : 'danger'"
            >
              {{ selectedDevice.status === "normal" ? "正常" : "异常" }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <div class="detail-chart">
          <div ref="detailChart" style="width: 100%; height: 300px"></div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import * as echarts from "echarts";
import { getStoreList, getEnergyConsumption } from "@/api";

// 状态变量
const selectedStore = ref("");
const selectedDate = ref(new Date());
const storeList = ref([]);
const energyData = ref([]);
const chartType = ref("daily");
const detailDialogVisible = ref(false);
const selectedDevice = ref(null);

// 图表实例
let consumptionChart = null;
let deviceTypeChart = null;
let timeDistributionChart = null;
let detailChart = null;

// 计算属性
const totalConsumption = computed(() => {
  return energyData.value
    .reduce((sum, item) => sum + item.totalConsumption, 0)
    .toFixed(2);
});

const averageConsumption = computed(() => {
  return (totalConsumption.value / energyData.value.length).toFixed(2);
});

const energySavingRate = computed(() => {
  return (
    energyData.value.reduce((sum, item) => sum + item.energySavingRate, 0) /
    energyData.value.length
  );
});

const energySavingAmount = computed(() => {
  return energyData.value
    .reduce((sum, item) => sum + item.savingAmount, 0)
    .toFixed(2);
});

// 变化率（模拟数据）
const consumptionChange = ref(5.2);
const averageChange = ref(-2.1);
const savingChange = ref(3.5);
const amountChange = ref(4.8);

// 方法
const handleStoreChange = () => {
  fetchEnergyData();
};

const handleDateChange = () => {
  fetchEnergyData();
};

const showDeviceDetail = (device) => {
  selectedDevice.value = device;
  detailDialogVisible.value = true;
  nextTick(() => {
    initDetailChart();
  });
};

// 初始化图表
const initConsumptionChart = () => {
  if (!consumptionChart) {
    consumptionChart = echarts.init(
      document.querySelector(".chart-container div")
    );
  }

  const option = {
    tooltip: {
      trigger: "axis",
    },
    legend: {
      data: ["总能耗", "工作能耗", "待机能耗"],
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [
        "00:00",
        "03:00",
        "06:00",
        "09:00",
        "12:00",
        "15:00",
        "18:00",
        "21:00",
      ],
    },
    yAxis: {
      type: "value",
      name: "能耗(kWh)",
    },
    series: [
      {
        name: "总能耗",
        type: "line",
        data: [120, 132, 101, 134, 90, 230, 210, 180],
      },
      {
        name: "工作能耗",
        type: "line",
        data: [80, 92, 71, 94, 60, 180, 160, 140],
      },
      {
        name: "待机能耗",
        type: "line",
        data: [40, 40, 30, 40, 30, 50, 50, 40],
      },
    ],
  };

  consumptionChart.setOption(option);
};

const initDeviceTypeChart = () => {
  if (!deviceTypeChart) {
    deviceTypeChart = echarts.init(
      document.querySelector(".distribution-section .chart-container div")
    );
  }

  const option = {
    tooltip: {
      trigger: "item",
      formatter: "{b}: {c}kWh ({d}%)",
    },
    legend: {
      orient: "vertical",
      left: "left",
    },
    series: [
      {
        type: "pie",
        radius: "50%",
        data: [
          { value: 735, name: "人工POS" },
          { value: 580, name: "自助POS" },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
  };

  deviceTypeChart.setOption(option);
};

const initTimeDistributionChart = () => {
  if (!timeDistributionChart) {
    timeDistributionChart = echarts.init(
      document.querySelector(
        ".distribution-section .chart-container:last-child div"
      )
    );
  }

  const option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      data: ["工作能耗", "待机能耗"],
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: ["00:00-06:00", "06:00-12:00", "12:00-18:00", "18:00-24:00"],
    },
    yAxis: {
      type: "value",
      name: "能耗(kWh)",
    },
    series: [
      {
        name: "工作能耗",
        type: "bar",
        stack: "total",
        data: [320, 302, 301, 334],
      },
      {
        name: "待机能耗",
        type: "bar",
        stack: "total",
        data: [120, 132, 101, 134],
      },
    ],
  };

  timeDistributionChart.setOption(option);
};

const initDetailChart = () => {
  if (!detailChart) {
    detailChart = echarts.init(document.querySelector(".detail-chart div"));
  }

  const option = {
    tooltip: {
      trigger: "axis",
    },
    legend: {
      data: ["能耗"],
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [
        "00:00",
        "03:00",
        "06:00",
        "09:00",
        "12:00",
        "15:00",
        "18:00",
        "21:00",
      ],
    },
    yAxis: {
      type: "value",
      name: "能耗(kWh)",
    },
    series: [
      {
        name: "能耗",
        type: "line",
        data: [120, 132, 101, 134, 90, 230, 210, 180],
        areaStyle: {},
      },
    ],
  };

  detailChart.setOption(option);
};

// 获取数据
const fetchStoreList = async () => {
  try {
    const response = await getStoreList();
    storeList.value = response.data;
    if (storeList.value.length > 0) {
      selectedStore.value = storeList.value[0].id;
    }
  } catch (error) {
    console.error("获取门店列表失败:", error);
  }
};

const fetchEnergyData = async () => {
  if (!selectedStore.value) return;

  try {
    const response = await getEnergyConsumption({
      storeId: selectedStore.value,
      date: selectedDate.value,
    });
    energyData.value = response.data;
    initCharts();
  } catch (error) {
    console.error("获取能耗数据失败:", error);
  }
};

const initCharts = () => {
  nextTick(() => {
    initConsumptionChart();
    initDeviceTypeChart();
    initTimeDistributionChart();
  });
};

// 生命周期钩子
onMounted(() => {
  fetchStoreList();
});

// 监听器
watch([selectedStore, selectedDate], () => {
  fetchEnergyData();
});

// 窗口大小改变时重绘图表
window.addEventListener("resize", () => {
  consumptionChart?.resize();
  deviceTypeChart?.resize();
  timeDistributionChart?.resize();
  detailChart?.resize();
});
</script>

<style lang="scss" scoped>
.energy-consumption {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
    }

    .filter-options {
      display: flex;
      gap: 16px;
    }
  }

  .overview-cards {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .card-content {
      .trend {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 8px;
        font-size: 14px;
        color: #666;

        .rate {
          display: flex;
          align-items: center;
          gap: 4px;

          &.up {
            color: #f56c6c;
          }

          &.down {
            color: #67c23a;
          }
        }
      }
    }
  }

  .chart-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .chart-container {
      height: 400px;
    }
  }

  .distribution-section {
    margin-bottom: 20px;

    .chart-container {
      height: 300px;
    }
  }

  .table-card {
    .el-table {
      margin-top: 16px;
    }
  }
}

.device-detail {
  .detail-chart {
    margin-top: 20px;
  }
}
</style>
