<template>
  <div class="energy-optimization-container">
    <div class="page-header">
      <h1>能耗优化建议</h1>
      <div class="filter-area">
        <el-select v-model="selectedStore" placeholder="选择门店">
          <el-option
            v-for="store in storeList"
            :key="store.id"
            :label="store.name"
            :value="store.id"
          ></el-option>
        </el-select>
      </div>
    </div>

    <!-- 能耗概览卡片 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card shadow="hover" class="overview-card">
          <div class="card-title">总能耗 (今日)</div>
          <div class="card-value">{{ energyData.dailyConsumption }} kWh</div>
          <div class="card-footer">
            较昨日:
            <span :class="energyData.dailyChange < 0 ? 'down' : 'up'">
              {{ energyData.dailyChange > 0 ? "+" : ""
              }}{{ energyData.dailyChange }}%
            </span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="overview-card">
          <div class="card-title">本月总能耗</div>
          <div class="card-value">{{ energyData.monthlyConsumption }} kWh</div>
          <div class="card-footer">
            较上月:
            <span :class="energyData.monthlyChange < 0 ? 'down' : 'up'">
              {{ energyData.monthlyChange > 0 ? "+" : ""
              }}{{ energyData.monthlyChange }}%
            </span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="overview-card">
          <div class="card-title">优化后预计能耗</div>
          <div class="card-value">
            {{ energyData.projectedConsumption }} kWh
          </div>
          <div class="card-footer">
            预计节约:
            <span class="down"> -{{ energyData.savingPercentage }}% </span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="overview-card">
          <div class="card-title">年度节约预估</div>
          <div class="card-value">
            ¥{{ (energyData.yearlySavingCost / 10000).toFixed(2) }}万
          </div>
          <div class="card-footer">
            节电量: {{ (energyData.yearlySavingEnergy / 10000).toFixed(2) }}万度
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 优化建议卡片 -->
    <el-card shadow="hover" class="optimization-card">
      <template #header>
        <div class="card-header">
          <span>能耗优化建议</span>
        </div>
      </template>
      <el-table :data="energyData.optimizationSuggestions" style="width: 100%">
        <el-table-column type="expand">
          <template #default="props">
            <div class="suggestion-detail">
              <p>{{ props.row.description }}</p>
              <div class="detail-metrics">
                <div class="metric">
                  <div class="metric-label">实施难度</div>
                  <el-rate
                    v-model="props.row.difficulty"
                    disabled
                    text-color="#ff9900"
                  />
                </div>
                <div class="metric">
                  <div class="metric-label">投资回报周期</div>
                  <span>{{ props.row.roi }} 个月</span>
                </div>
                <div class="metric">
                  <div class="metric-label">预计投资</div>
                  <span>¥{{ props.row.investment.toLocaleString() }}</span>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="优化措施" />
        <el-table-column prop="type" label="类型">
          <template #default="scope">
            <el-tag :type="getSuggestionTagType(scope.row.type)">
              {{ scope.row.type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="potentialSaving" label="潜在节约">
          <template #default="scope">
            {{ scope.row.potentialSaving }} kWh/天
          </template>
        </el-table-column>
        <el-table-column prop="costSaving" label="每年节约成本">
          <template #default="scope">
            ¥{{ scope.row.costSaving.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button
              size="small"
              type="success"
              @click="handleImplement(scope.row)"
            >
              实施
            </el-button>
            <el-button size="small" @click="handleDetail(scope.row)"
              >详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 设备耗电量排行 -->
    <el-card shadow="hover" class="ranking-card">
      <template #header>
        <div class="card-header">
          <span>设备耗电量排行</span>
          <div class="header-actions">
            <el-radio-group v-model="rankingTimeRange" size="small">
              <el-radio-button label="day">今日</el-radio-button>
              <el-radio-button label="week">本周</el-radio-button>
              <el-radio-button label="month">本月</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </template>
      <el-table :data="energyData.deviceConsumptionRanking" style="width: 100%">
        <el-table-column prop="rank" label="排名" width="80" />
        <el-table-column prop="deviceId" label="设备ID" width="120" />
        <el-table-column prop="type" label="设备类型">
          <template #default="scope">
            {{ scope.row.type === "manual" ? "人工POS" : "自助POS" }}
          </template>
        </el-table-column>
        <el-table-column prop="location" label="位置" />
        <el-table-column prop="consumption" label="耗电量">
          <template #default="scope">
            {{ scope.row.consumption }} kWh
          </template>
        </el-table-column>
        <el-table-column prop="usageRate" label="使用率">
          <template #default="scope">
            <div class="usage-rate-cell">
              {{ scope.row.usageRate }}%
              <el-progress
                :percentage="scope.row.usageRate"
                :stroke-width="6"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="优化状态">
          <template #default="scope">
            <el-tag
              :type="getOptimizationStatusType(scope.row.optimizationStatus)"
            >
              {{ getOptimizationStatusText(scope.row.optimizationStatus) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { getEnergyOptimizationData, getStoreList } from "@/api";

// 状态
const selectedStore = ref("");
const storeList = ref([]);
const rankingTimeRange = ref("day");
const energyData = ref({
  dailyConsumption: 0,
  dailyChange: 0,
  monthlyConsumption: 0,
  monthlyChange: 0,
  projectedConsumption: 0,
  savingPercentage: 0,
  yearlySavingEnergy: 0,
  yearlySavingCost: 0,
  optimizationSuggestions: [],
  deviceConsumptionRanking: [],
});

// 方法
const getSuggestionTagType = (type) => {
  const types = {
    硬件更新: "danger",
    运营优化: "warning",
    软件调整: "success",
    使用习惯: "info",
  };
  return types[type] || "info";
};

const getOptimizationStatusType = (status) => {
  const types = {
    optimized: "success",
    pending: "warning",
    not_optimized: "info",
  };
  return types[status] || "info";
};

const getOptimizationStatusText = (status) => {
  const texts = {
    optimized: "已优化",
    pending: "优化中",
    not_optimized: "未优化",
  };
  return texts[status] || status;
};

const handleImplement = (suggestion) => {
  console.log("实施优化建议", suggestion);
  // 实现实施优化建议的逻辑
};

const handleDetail = (suggestion) => {
  console.log("查看优化建议详情", suggestion);
  // 实现查看优化建议详情的逻辑
};

// 生命周期
onMounted(async () => {
  try {
    // 获取门店列表
    const stores = await getStoreList();
    storeList.value = stores;
    if (stores.length > 0) {
      selectedStore.value = stores[0].id;
    }

    // 获取能耗优化数据
    const data = await getEnergyOptimizationData({
      storeId: selectedStore.value,
    });
    energyData.value = data;
  } catch (error) {
    console.error("获取能耗优化数据失败:", error);
  }
});
</script>

<style lang="scss" scoped>
.energy-optimization-container {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
    }

    .filter-area {
      display: flex;
      gap: 15px;
    }
  }

  .overview-card {
    height: 180px;

    .card-title {
      font-size: 14px;
      color: #606266;
      margin-bottom: 10px;
    }

    .card-value {
      font-size: 28px;
      font-weight: 500;
      margin-bottom: 20px;
    }

    .card-footer {
      font-size: 14px;

      .up {
        color: #f56c6c;
      }

      .down {
        color: #67c23a;
      }
    }
  }

  .optimization-card,
  .ranking-card {
    margin-top: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .suggestion-detail {
    padding: 20px;
    background-color: #f5f7fa;

    .detail-metrics {
      display: flex;
      margin-top: 15px;
      gap: 40px;

      .metric {
        display: flex;
        flex-direction: column;

        .metric-label {
          font-size: 14px;
          color: #909399;
          margin-bottom: 5px;
        }
      }
    }
  }

  .usage-rate-cell {
    width: 200px;
  }
}
</style>
