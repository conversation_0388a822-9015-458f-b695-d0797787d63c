<template>
  <div class="account-container">
    <el-card class="account-card">
      <template #header>
        <div class="card-header">
          <h3>个人中心</h3>
        </div>
      </template>
      <el-form :model="accountForm" label-width="100px">
        <el-form-item label="用户名">
          <el-input v-model="accountForm.username" disabled></el-input>
        </el-form-item>
        <el-form-item label="姓名">
          <el-input v-model="accountForm.name"></el-input>
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input v-model="accountForm.email"></el-input>
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="accountForm.phone"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="saveChanges">保存修改</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { reactive } from "vue";

const accountForm = reactive({
  username: "admin",
  name: "管理员",
  email: "<EMAIL>",
  phone: "***********",
});

const saveChanges = () => {
  // 实现保存逻辑
  ElMessage.success("保存成功");
};
</script>

<style scoped>
.account-container {
  padding: 20px;
}

.account-card {
  max-width: 600px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
