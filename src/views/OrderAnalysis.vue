<template>
  <div class="order-analysis-container">
    <div class="page-header">
      <h1>订单量时段分析</h1>
      <div class="filter-container">
        <el-select
          v-model="selectedStore"
          placeholder="选择门店"
          style="width: 180px; margin-right: 15px"
        >
          <el-option
            v-for="store in storeList"
            :key="store.id"
            :label="store.name"
            :value="store.id"
          />
        </el-select>
        <el-date-picker
          v-model="selectedDate"
          type="date"
          placeholder="选择日期"
        />
      </div>
    </div>

    <!-- 今日订单量概览 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card shadow="hover" class="overview-card">
          <div class="card-title">今日总订单量</div>
          <div class="card-value">{{ getTotalOrders() }}</div>
          <div class="card-ratio">
            <div>
              人工: {{ getTotalManualOrders() }} | 自助:
              {{ getTotalSelfServiceOrders() }}
            </div>
            <div>取消率: {{ getCancelRate() }}%</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="overview-card">
          <div class="card-title">高峰期订单量</div>
          <div class="card-value">
            {{ orderData.peakAnalysis?.eveningPeak.avgOrders || 0 }}
          </div>
          <div class="card-ratio">
            <div>
              晚高峰:
              {{ orderData.peakAnalysis?.eveningPeak.startTime || "" }} -
              {{ orderData.peakAnalysis?.eveningPeak.endTime || "" }}
            </div>
            <div>
              午高峰: {{ orderData.peakAnalysis?.lunchPeak.startTime || "" }} -
              {{ orderData.peakAnalysis?.lunchPeak.endTime || "" }}
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="overview-card">
          <div class="card-title">周环比</div>
          <div class="card-value">{{ getWeeklyComparisonRate() }}%</div>
          <div class="card-ratio">
            <div>
              <el-tag
                size="small"
                :type="getWeeklyComparisonRate() >= 0 ? 'success' : 'danger'"
              >
                {{ getWeeklyComparisonRate() >= 0 ? "增长" : "下降" }}
              </el-tag>
            </div>
            <div>上周同期: {{ getLastWeekTotal() }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="overview-card">
          <div class="card-title">单均能耗</div>
          <div class="card-value">
            {{ getAverageEnergyPerOrder().toFixed(2) }} kWh
          </div>
          <div class="card-ratio">
            <div>
              优化建议值:
              {{ (getAverageEnergyPerOrder() * 0.65).toFixed(2) }} kWh
            </div>
            <div>
              预计节约:
              {{ (getAverageEnergyPerOrder() * 0.35).toFixed(2) }} kWh/单
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 订单量/时间柱状图 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="24">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>订单量时段分布</span>
              <div class="header-actions">
                <el-radio-group v-model="orderChartType" size="small">
                  <el-radio-button label="total">总量</el-radio-button>
                  <el-radio-button label="comparison">分类对比</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <div class="chart-container">
            <OrderTimeDistributionChart
              :chart-data="orderData.todayOrders || []"
              :chart-type="orderChartType"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 历史同期对比 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="24">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>历史同期对比</span>
              <div class="header-actions">
                <el-radio-group v-model="compareType" size="small">
                  <el-radio-button label="weekday">工作日</el-radio-button>
                  <el-radio-button label="weekend">周末</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <div class="chart-container">
            <HistoricalComparisonChart
              :chart-data="
                orderData.compareWithHistory
                  ? orderData.compareWithHistory[compareType]
                  : []
              "
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 门店订单量对比 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="24">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>门店订单量对比</span>
            </div>
          </template>
          <el-table :data="orderData.storeComparison || []" style="width: 100%">
            <el-table-column prop="storeName" label="门店名称" />
            <el-table-column prop="totalOrders" label="总订单量" sortable />
            <el-table-column label="订单分布">
              <template #default="scope">
                <div class="order-distribution">
                  <div class="distribution-item">
                    <span class="distribution-label">人工:</span>
                    <span class="distribution-value">{{
                      scope.row.manualOrders
                    }}</span>
                    <el-progress
                      :percentage="
                        Math.round(
                          (scope.row.manualOrders / scope.row.totalOrders) * 100
                        )
                      "
                      :stroke-width="6"
                      color="#409EFF"
                    />
                  </div>
                  <div class="distribution-item">
                    <span class="distribution-label">自助:</span>
                    <span class="distribution-value">{{
                      scope.row.selfServiceOrders
                    }}</span>
                    <el-progress
                      :percentage="
                        Math.round(
                          (scope.row.selfServiceOrders /
                            scope.row.totalOrders) *
                            100
                        )
                      "
                      :stroke-width="6"
                      color="#67C23A"
                    />
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="优化建议">
              <template #default="scope">
                <div
                  v-if="getOrderSuggestion(scope.row)"
                  class="suggestion-text"
                >
                  {{ getOrderSuggestion(scope.row) }}
                </div>
                <div v-else class="suggestion-text">数据正常，暂无建议</div>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 高峰期分析与建议 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="24">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>高峰期设备优化建议</span>
            </div>
          </template>
          <div class="peak-analysis">
            <div class="peak-item">
              <h3>
                午间高峰期 ({{
                  orderData.peakAnalysis?.lunchPeak.startTime || ""
                }}
                - {{ orderData.peakAnalysis?.lunchPeak.endTime || "" }})
              </h3>
              <p>
                平均订单量:
                {{ orderData.peakAnalysis?.lunchPeak.avgOrders || 0 }}
              </p>
              <div class="suggestion-box">
                <p><strong>建议配置:</strong></p>
                <p>人工POS: {{ getLunchPeakManualPos() }}台</p>
                <p>自助POS: {{ getLunchPeakSelfServicePos() }}台</p>
                <p class="suggestion-detail">
                  依据峰值订单量和平均处理速度，在午餐高峰期建议保持以上数量的POS设备在线。
                  人工POS平均每小时处理12单，自助POS平均每小时处理6单，并预留20%容量应对突发情况。
                </p>
              </div>
            </div>

            <div class="peak-item">
              <h3>
                晚间高峰期 ({{
                  orderData.peakAnalysis?.eveningPeak.startTime || ""
                }}
                - {{ orderData.peakAnalysis?.eveningPeak.endTime || "" }})
              </h3>
              <p>
                平均订单量:
                {{ orderData.peakAnalysis?.eveningPeak.avgOrders || 0 }}
              </p>
              <div class="suggestion-box">
                <p><strong>建议配置:</strong></p>
                <p>人工POS: {{ getEveningPeakManualPos() }}台</p>
                <p>自助POS: {{ getEveningPeakSelfServicePos() }}台</p>
                <p class="suggestion-detail">
                  晚间高峰期订单量较午间增加{{
                    Math.round(
                      (((orderData.peakAnalysis?.eveningPeak.avgOrders || 0) -
                        (orderData.peakAnalysis?.lunchPeak.avgOrders || 0)) /
                        (orderData.peakAnalysis?.lunchPeak.avgOrders || 1)) *
                        100
                    )
                  }}%，
                  建议增加相应设备数量以应对客流高峰，确保顾客等待时间控制在3分钟以内。
                </p>
              </div>
            </div>

            <div class="peak-item">
              <h3>闲时设备关闭建议</h3>
              <div class="suggestion-box">
                <p><strong>建议时段:</strong></p>
                <p>
                  上午低谷期 (08:00-10:00): 仅保留{{
                    getLowPeakPos()
                  }}台人工POS、{{ getLowPeakPos() - 1 }}台自助POS
                </p>
                <p>
                  下午低谷期 (14:00-16:00): 保留{{
                    getLowPeakPos() + 1
                  }}台人工POS、{{ getLowPeakPos() }}台自助POS
                </p>
                <p class="suggestion-detail">
                  根据历史订单数据分析，低谷期订单量较高峰下降60%以上，可关闭部分设备以节约能耗。
                  如客流突然增加，可快速开机增加设备支持。
                </p>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { getOrderAnalysisData, getStoreList } from "@/api";
import OrderTimeDistributionChart from "@/components/charts/OrderTimeDistributionChart.vue";
import HistoricalComparisonChart from "@/components/charts/HistoricalComparisonChart.vue";

// 状态
const selectedStore = ref("");
const selectedDate = ref(new Date());
const storeList = ref([]);
const orderData = ref({
  todayOrders: [],
  compareWithHistory: {
    weekday: [],
    weekend: [],
  },
  peakAnalysis: {
    morningPeak: { startTime: "", endTime: "", avgOrders: 0 },
    eveningPeak: { startTime: "", endTime: "", avgOrders: 0 },
    lunchPeak: { startTime: "", endTime: "", avgOrders: 0 },
  },
  storeComparison: [],
});
const orderChartType = ref("total");
const compareType = ref("weekday");

// 方法
const getTotalOrders = () => {
  if (
    !orderData.value.todayOrders ||
    orderData.value.todayOrders.length === 0
  ) {
    return 0;
  }

  return orderData.value.todayOrders.reduce(
    (total, item) => total + item.total,
    0
  );
};

const getTotalManualOrders = () => {
  if (
    !orderData.value.todayOrders ||
    orderData.value.todayOrders.length === 0
  ) {
    return 0;
  }

  return orderData.value.todayOrders.reduce(
    (total, item) => total + item.manual,
    0
  );
};

const getTotalSelfServiceOrders = () => {
  if (
    !orderData.value.todayOrders ||
    orderData.value.todayOrders.length === 0
  ) {
    return 0;
  }

  return orderData.value.todayOrders.reduce(
    (total, item) => total + item.selfService,
    0
  );
};

const getCancelRate = () => {
  if (
    !orderData.value.todayOrders ||
    orderData.value.todayOrders.length === 0
  ) {
    return 0;
  }

  const totalCanceled = orderData.value.todayOrders.reduce(
    (total, item) => total + item.canceled,
    0
  );
  const totalOrders = getTotalOrders();

  return totalOrders > 0 ? ((totalCanceled / totalOrders) * 100).toFixed(1) : 0;
};

const getWeeklyComparisonRate = () => {
  if (
    !orderData.value.compareWithHistory ||
    !orderData.value.compareWithHistory[compareType.value]
  ) {
    return 0;
  }

  const today = orderData.value.compareWithHistory[compareType.value].reduce(
    (total, item) => total + item.today,
    0
  );
  const lastWeek = orderData.value.compareWithHistory[compareType.value].reduce(
    (total, item) => total + item.lastWeek,
    0
  );

  return lastWeek > 0 ? (((today - lastWeek) / lastWeek) * 100).toFixed(1) : 0;
};

const getLastWeekTotal = () => {
  if (
    !orderData.value.compareWithHistory ||
    !orderData.value.compareWithHistory[compareType.value]
  ) {
    return 0;
  }

  return orderData.value.compareWithHistory[compareType.value].reduce(
    (total, item) => total + item.lastWeek,
    0
  );
};

const getAverageEnergyPerOrder = () => {
  // 假设每天总能耗是固定的（基于文档数据）
  const dailyPowerConsumption = 18720; // kWh/天
  const totalOrders = getTotalOrders();

  return totalOrders > 0 ? dailyPowerConsumption / totalOrders : 0;
};

const getLunchPeakManualPos = () => {
  const lunchPeakOrders =
    orderData.value.peakAnalysis?.lunchPeak.avgOrders || 0;
  // 假设人工POS平均每小时处理12单，高峰持续1小时，加20%容量
  const manualHandlingCapacity = 12;
  return Math.ceil(((lunchPeakOrders * 0.6) / manualHandlingCapacity) * 1.2);
};

const getLunchPeakSelfServicePos = () => {
  const lunchPeakOrders =
    orderData.value.peakAnalysis?.lunchPeak.avgOrders || 0;
  // 假设自助POS平均每小时处理6单，高峰持续1小时，加20%容量
  const selfServiceHandlingCapacity = 6;
  return Math.ceil(
    ((lunchPeakOrders * 0.4) / selfServiceHandlingCapacity) * 1.2
  );
};

const getEveningPeakManualPos = () => {
  const eveningPeakOrders =
    orderData.value.peakAnalysis?.eveningPeak.avgOrders || 0;
  // 假设人工POS平均每小时处理12单，高峰持续1小时，加20%容量
  const manualHandlingCapacity = 12;
  return Math.ceil(((eveningPeakOrders * 0.6) / manualHandlingCapacity) * 1.2);
};

const getEveningPeakSelfServicePos = () => {
  const eveningPeakOrders =
    orderData.value.peakAnalysis?.eveningPeak.avgOrders || 0;
  // 假设自助POS平均每小时处理6单，高峰持续1小时，加20%容量
  const selfServiceHandlingCapacity = 6;
  return Math.ceil(
    ((eveningPeakOrders * 0.4) / selfServiceHandlingCapacity) * 1.2
  );
};

const getLowPeakPos = () => {
  // 低谷期订单量约为高峰期的30-40%
  return Math.ceil(getLunchPeakManualPos() * 0.4);
};

const getOrderSuggestion = (storeData) => {
  if (!storeData) return null;

  const manualOrderPercentage =
    (storeData.manualOrders / storeData.totalOrders) * 100;
  const selfServiceOrderPercentage =
    (storeData.selfServiceOrders / storeData.totalOrders) * 100;

  if (selfServiceOrderPercentage < 30) {
    return `自助结算比例过低(${selfServiceOrderPercentage.toFixed(
      1
    )}%)，建议加强引导和宣传，提高自助POS使用率`;
  }

  if (manualOrderPercentage > 70) {
    return `人工结算比例过高(${manualOrderPercentage.toFixed(
      1
    )}%)，建议适当转移部分业务至自助POS以提高整体效率`;
  }

  if (storeData.totalOrders > 8000) {
    return `订单量较大，建议适当增加POS设备预备数量，确保高峰期顺畅运营`;
  }

  return null;
};

// 生命周期
onMounted(async () => {
  try {
    // 获取门店列表
    const stores = await getStoreList();
    storeList.value = stores;
    if (stores.length > 0) {
      selectedStore.value = stores[0].id;
    }

    // 获取订单分析数据
    const data = await getOrderAnalysisData();
    orderData.value = data;
  } catch (error) {
    console.error("获取数据失败:", error);
  }
});

// 监听选择变化，重新获取数据
watch([selectedStore, selectedDate], async () => {
  if (!selectedStore.value) return;

  try {
    const data = await getOrderAnalysisData({
      storeId: selectedStore.value,
      date: selectedDate.value
        ? selectedDate.value.toISOString().split("T")[0]
        : undefined,
    });

    // 在实际项目中，这里会根据API返回重新赋值
    // 这里使用模拟数据，所以不需要重新赋值
  } catch (error) {
    console.error("获取数据失败:", error);
  }
});
</script>

<style lang="scss" scoped>
.order-analysis-container {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
    }
  }

  .overview-card {
    height: 180px;

    .card-title {
      font-size: 14px;
      color: #606266;
      margin-bottom: 10px;
    }

    .card-value {
      font-size: 28px;
      font-weight: 500;
      margin-bottom: 20px;
    }

    .card-ratio {
      font-size: 12px;
      color: #909399;

      div {
        margin-bottom: 5px;
      }
    }
  }

  .chart-row {
    margin-top: 20px;
  }

  .chart-container {
    height: 350px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .order-distribution {
    .distribution-item {
      margin-bottom: 8px;

      .distribution-label {
        margin-right: 5px;
      }

      .distribution-value {
        font-weight: 500;
        margin-right: 5px;
      }
    }
  }

  .suggestion-text {
    color: #e6a23c;
    font-size: 12px;
  }

  .peak-analysis {
    .peak-item {
      margin-bottom: 30px;

      h3 {
        margin-top: 0;
        margin-bottom: 10px;
        color: #409eff;
      }

      .suggestion-box {
        background-color: #f8f8f8;
        border-radius: 4px;
        padding: 15px;
        margin-top: 10px;

        p {
          margin: 5px 0;
        }

        .suggestion-detail {
          margin-top: 10px;
          color: #606266;
          font-size: 13px;
          line-height: 1.5;
        }
      }
    }
  }
}
</style>
