<template>
  <el-config-provider :locale="zhCn">
    <!-- 登录页面 -->
    <router-view v-if="$route.name === 'Login'" />

    <!-- 主应用布局 -->
    <div v-else class="app-container">
      <el-container>
        <el-aside width="220px">
          <div class="logo-section">
            <h3>POS看盘中台</h3>
          </div>
          <el-menu
            router
            :default-active="$route.path"
            class="el-menu-vertical"
            background-color="#001529"
            text-color="#fff"
            active-text-color="#ffd04b"
          >
            <el-menu-item index="/">
              <el-icon><DataLine /></el-icon>
              <span>首页概览</span>
            </el-menu-item>
            <el-menu-item index="/pos-usage">
              <el-icon><PieChart /></el-icon>
              <span>POS使用率分析</span>
            </el-menu-item>
            <el-menu-item index="/order-analysis">
              <el-icon><Histogram /></el-icon>
              <span>订单量时段分析</span>
            </el-menu-item>
            <el-menu-item index="/device-status">
              <el-icon><Monitor /></el-icon>
              <span>设备状态监控</span>
            </el-menu-item>
            <el-menu-item index="/energy-optimization">
              <el-icon><Lightning /></el-icon>
              <span>能耗优化建议</span>
            </el-menu-item>
          </el-menu>
        </el-aside>
        <el-container>
          <el-header>
            <div class="header-title">
              <h2>POS看盘</h2>
            </div>
            <div class="header-user">
              <el-dropdown @command="handleCommand">
                <span class="el-dropdown-link">
                  <el-avatar :size="32" class="user-avatar">
                    {{ userStore.userInfo?.name?.charAt(0) || "U" }}
                  </el-avatar>
                  <span class="username">{{
                    userStore.userInfo?.name || "用户"
                  }}</span>
                  <el-icon><ArrowDown /></el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="profile">
                      <el-icon><User /></el-icon>
                      个人中心
                    </el-dropdown-item>
                    <el-dropdown-item command="settings">
                      <el-icon><Setting /></el-icon>
                      设置
                    </el-dropdown-item>
                    <el-dropdown-item divided command="logout">
                      <el-icon><SwitchButton /></el-icon>
                      退出登录
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </el-header>
          <el-main>
            <router-view />
          </el-main>
          <el-footer>© 2023 POS能耗分析优化系统</el-footer>
        </el-container>
      </el-container>
    </div>
  </el-config-provider>
</template>

<script setup>
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { useUserStore } from "@/stores/user";
import zhCn from "element-plus/dist/locale/zh-cn.mjs";

const router = useRouter();
const userStore = useUserStore();

// 处理下拉菜单命令
const handleCommand = async (command) => {
  switch (command) {
    case "profile":
      router.push("/account");
      break;
    case "settings":
      ElMessage.info("设置功能开发中...");
      break;
    case "logout":
      try {
        await ElMessageBox.confirm("确定要退出登录吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });

        // 执行登出
        userStore.logout();
        ElMessage.success("已退出登录");

        // 跳转到登录页
        router.push("/login");
      } catch {
        // 用户取消登出
      }
      break;
  }
};
</script>

<style lang="scss">
html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB",
    "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
}

#app {
  height: 100vh;
}

.app-container {
  height: 100%;
}

.el-container {
  height: 100%;
}

.el-aside {
  background-color: #001529;
  color: #fff;
  .el-menu {
    border-right: none;
  }
}

.logo-section {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #1f2937;

  h3 {
    color: #fff;
    margin: 0;
    font-size: 16px;
    font-weight: 600;
  }
}

.el-header {
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .header-user {
    margin-right: 20px;
    cursor: pointer;

    .el-dropdown-link {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #333;
      text-decoration: none;

      &:hover {
        color: #409eff;
      }
    }

    .user-avatar {
      background-color: #409eff;
      color: #fff;
      font-weight: 600;
    }

    .username {
      font-size: 14px;
      font-weight: 500;
    }
  }
}

.el-footer {
  text-align: center;
  color: #999;
  line-height: 60px;
  background-color: #f5f7fa;
}

.el-main {
  background-color: #f0f2f5;
  padding: 20px;
}

.el-menu-vertical {
  height: 100%;
}
</style>
