server:
  port: 8081
  servlet:
    context-path: /api/v1

spring:
  application:
    name: rtpos-server

  profiles:
    active: dev

  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************
    username: root
    password: password
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 0
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: 5000ms

  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 600000
      cache-null-values: false

  # Security配置
  security:
    user:
      name: admin
      password: admin123
      roles: ADMIN

# MyBatis配置（暂时禁用，使用JPA）
# mybatis:
#   mapper-locations: classpath:mapper/*.xml
#   type-aliases-package: com.rtpos.server.entity
#   configuration:
#     map-underscore-to-camel-case: true
#     cache-enabled: true
#     lazy-loading-enabled: true
#     multiple-result-sets-enabled: true

# 日志配置
logging:
  level:
    com.rtpos.server: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/rtpos-server.log

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always

# API文档配置
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    operations-sorter: method

# 应用自定义配置
rtpos:
  order:
    sync:
      enabled: true
      interval: 30000 # 30秒同步一次
      batch-size: 100
    cache:
      ttl: 300000 # 5分钟缓存
  security:
    jwt:
      secret: rtpos-server-jwt-secret-key-2024
      expiration: 86400000 # 24小时

# POS订单同步配置
pos:
  api:
    base-url: http://middle-order-biz.beta1.fn
    query-orders-path: /api/queryOrder/querySelfPosNodeOrders
    # 收银日志API配置（具体域名在环境配置文件中覆盖）
    operation-log-base-url: http://rt-pos-api.beta1.fn
    operation-log-path: /api/pos/getOperationLogInfo
    # 门店信息API配置（具体域名在环境配置文件中覆盖）
    store-base-url: http://rt-pos-api.beta1.fn
    store-path: /api/pos/getPosStore
  sync:
    enabled: true
    batch-size: 100
    max-pages: 50
    incremental:
      enabled: true
    check-missing:
      enabled: false
    full:
      enabled: false
    # 收银日志同步配置
    operation-log:
      enabled: true
      batch-size: 20
      max-pages: 50
      incremental:
        enabled: true
      check-missing:
        enabled: false
      full:
        enabled: false
      cleanup:
        enabled: false
      health-check:
        enabled: false
