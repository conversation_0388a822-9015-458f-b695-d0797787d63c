-- 初始化测试数据

-- 创建POS收银操作日志表（如果不存在）
CREATE TABLE IF NOT EXISTS pos_operation_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    store_no INT NOT NULL COMMENT '门店编号',
    pos_no INT NOT NULL COMMENT 'POS机编号',
    device_no VARCHAR(100) COMMENT '设备编号',
    pos_type INT COMMENT 'POS机类型',
    operation_type INT NOT NULL COMMENT '操作类型',
    flow_id VARCHAR(100) COMMENT '流水ID',
    shift_no VARCHAR(50) COMMENT '班次号',
    order_id VARCHAR(50) COMMENT '订单ID',
    membership_card_id VARCHAR(50) COMMENT '会员卡ID',
    occurrence_time BIGINT NOT NULL COMMENT '发生时间（时间戳）',
    occurrence_time_dt DATETIME COMMENT '发生时间（LocalDateTime）',
    work_time VARCHAR(200) COMMENT '工作时间描述',
    extended_json TEXT COMMENT '扩展JSON字符串',
    sync_status INT DEFAULT 0 COMMENT '同步状态：0-待同步，1-已同步，2-同步失败',
    sync_time DATETIME COMMENT '同步时间',
    sync_error VARCHAR(500) COMMENT '同步失败原因',
    original_create_time BIGINT COMMENT '原始创建时间（来自外部系统）',
    original_modify_time BIGINT COMMENT '原始修改时间（来自外部系统）',
    data_version VARCHAR(50) COMMENT '数据版本（用于增量同步）',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),
    version BIGINT DEFAULT 0,
    deleted BOOLEAN DEFAULT FALSE,

    -- 索引
    INDEX idx_operation_store_no (store_no),
    INDEX idx_operation_pos_no (pos_no),
    INDEX idx_operation_device_no (device_no),
    INDEX idx_operation_type (operation_type),
    INDEX idx_operation_flow_id (flow_id),
    INDEX idx_operation_shift_no (shift_no),
    INDEX idx_operation_order_id (order_id),
    INDEX idx_operation_occurrence_time (occurrence_time),
    INDEX idx_operation_membership_card_id (membership_card_id),
    INDEX idx_operation_sync_status (sync_status),

    -- 唯一约束（防止重复数据）
    UNIQUE KEY uk_operation_unique (store_no, pos_no, occurrence_time, operation_type)
);

-- 创建POS订单表（如果不存在）
CREATE TABLE IF NOT EXISTS pos_orders (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    store_id VARCHAR(20) NOT NULL COMMENT '门店ID',
    source INT COMMENT '数据源：1-自建POS，2-第三方POS',
    biz_order_id BIGINT NOT NULL COMMENT '业务订单ID',
    main_order_id BIGINT COMMENT '主订单ID',
    out_order_id VARCHAR(50) COMMENT '外部订单ID',
    pay_time BIGINT COMMENT '支付时间（时间戳）',
    pay_time_dt DATETIME COMMENT '支付时间（LocalDateTime）',
    order_time BIGINT COMMENT '下单时间（时间戳）',
    order_time_dt DATETIME COMMENT '下单时间（LocalDateTime）',
    order_status INT COMMENT '订单状态：1-已完成，2-已取消，3-进行中',
    operator_id VARCHAR(50) COMMENT '操作员ID',
    operator_name VARCHAR(100) COMMENT '操作员姓名',
    original_amt INT COMMENT '原始金额（分）',
    original_amount DECIMAL(10,2) COMMENT '原始金额（元）',
    discount_amt INT COMMENT '折扣金额（分）',
    discount_amount DECIMAL(10,2) COMMENT '折扣金额（元）',
    member_discount_amt INT COMMENT '会员折扣金额（分）',
    member_discount_amount DECIMAL(10,2) COMMENT '会员折扣金额（元）',
    packing_fee INT COMMENT '包装费（分）',
    packing_fee_amount DECIMAL(10,2) COMMENT '包装费（元）',
    packing_fee_tax_rate DECIMAL(6,3) COMMENT '包装费税率',
    member_card_num VARCHAR(50) COMMENT '会员卡号',
    pos_no INT COMMENT 'POS机号',
    device_id VARCHAR(100) COMMENT '设备ID',
    mq_timestamp BIGINT COMMENT 'MQ时间戳',
    duty_code VARCHAR(100) COMMENT '班次代码',
    extended_json TEXT COMMENT '扩展JSON字符串',
    original_deleted INT COMMENT '删除标记（原始字段）',
    deleted_time BIGINT COMMENT '删除时间',
    job_status INT COMMENT '任务状态',
    job_times INT COMMENT '任务次数',
    gmt_create BIGINT COMMENT '原始创建时间',
    gmt_modified BIGINT COMMENT '原始修改时间',
    main_extended_json_model TEXT COMMENT '主扩展JSON模型',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    version BIGINT DEFAULT 0 COMMENT '版本号',
    deleted BOOLEAN DEFAULT FALSE COMMENT '删除标记',

    INDEX idx_pos_store_id (store_id),
    INDEX idx_pos_biz_order_id (biz_order_id),
    INDEX idx_pos_main_order_id (main_order_id),
    INDEX idx_pos_out_order_id (out_order_id),
    INDEX idx_pos_order_time (order_time),
    INDEX idx_pos_pay_time (pay_time),
    INDEX idx_pos_order_status (order_status),
    INDEX idx_pos_operator_id (operator_id),
    INDEX idx_pos_device_id (device_id),
    INDEX idx_pos_no (pos_no),
    UNIQUE KEY uk_biz_order_id (biz_order_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='POS订单表';

-- 插入测试商品数据
INSERT IGNORE INTO products (id, product_code, product_name, category_id, category_name, brand, unit, price, cost_price, stock_quantity, min_stock, max_stock, status, description, barcode, specifications, created_at, updated_at, created_by, updated_by, version, deleted) VALUES
(1, 'P001', '苹果iPhone 15', 1, '手机数码', 'Apple', '台', 5999.00, 5000.00, 50, 10, 100, 'ACTIVE', '苹果最新款手机', '1234567890123', '128GB 蓝色', NOW(), NOW(), 'system', 'system', 0, false),
(2, 'P002', '华为Mate 60', 1, '手机数码', 'Huawei', '台', 4999.00, 4200.00, 30, 5, 80, 'ACTIVE', '华为旗舰手机', '1234567890124', '256GB 黑色', NOW(), NOW(), 'system', 'system', 0, false),
(3, 'P003', '小米13 Pro', 1, '手机数码', 'Xiaomi', '台', 3999.00, 3300.00, 40, 8, 90, 'ACTIVE', '小米高端手机', '1234567890125', '256GB 白色', NOW(), NOW(), 'system', 'system', 0, false),
(4, 'P004', 'MacBook Pro 14', 2, '电脑办公', 'Apple', '台', 15999.00, 14000.00, 20, 3, 50, 'ACTIVE', '苹果笔记本电脑', '1234567890126', 'M3芯片 512GB', NOW(), NOW(), 'system', 'system', 0, false),
(5, 'P005', 'AirPods Pro', 3, '音响耳机', 'Apple', '副', 1999.00, 1500.00, 100, 20, 200, 'ACTIVE', '苹果无线耳机', '1234567890127', '第二代 降噪', NOW(), NOW(), 'system', 'system', 0, false);

-- 插入测试订单数据
INSERT INTO orders (id, order_no, store_id, store_name, customer_id, customer_name, customer_phone, total_amount, discount_amount, actual_amount, payment_method, status, order_time, payment_time, complete_time, remark, created_at, updated_at, created_by, updated_by, version, deleted) VALUES
(1, 'ORD20241201001', 1, '北京旗舰店', 1001, '张三', '13800138001', 7998.00, 0.00, 7998.00, 'WECHAT_PAY', 'COMPLETED', '2024-12-01 10:30:00', '2024-12-01 10:31:00', '2024-12-01 11:00:00', '首次购买', NOW(), NOW(), 'system', 'system', 0, false),
(2, 'ORD20241201002', 1, '北京旗舰店', 1002, '李四', '13800138002', 4999.00, 100.00, 4899.00, 'ALIPAY', 'COMPLETED', '2024-12-01 14:20:00', '2024-12-01 14:21:00', '2024-12-01 15:00:00', 'VIP客户优惠', NOW(), NOW(), 'system', 'system', 0, false),
(3, 'ORD20241201003', 2, '上海体验店', 1003, '王五', '13800138003', 1999.00, 0.00, 1999.00, 'CREDIT_CARD', 'PAID', '2024-12-01 16:45:00', '2024-12-01 16:46:00', NULL, '', NOW(), NOW(), 'system', 'system', 0, false),
(4, 'ORD20241201004', 1, '北京旗舰店', 1004, '赵六', '13800138004', 15999.00, 500.00, 15499.00, 'CASH', 'PENDING', '2024-12-01 18:30:00', NULL, NULL, '企业采购', NOW(), NOW(), 'system', 'system', 0, false);

-- 插入测试订单项数据
INSERT INTO order_items (id, order_id, product_id, product_name, product_code, category_id, category_name, unit_price, quantity, discount_amount, subtotal, specifications, remark, created_at, updated_at, created_by, updated_by, version, deleted) VALUES
-- 订单1的商品
(1, 1, 1, '苹果iPhone 15', 'P001', 1, '手机数码', 5999.00, 1, 0.00, 5999.00, '128GB 蓝色', '', NOW(), NOW(), 'system', 'system', 0, false),
(2, 1, 5, 'AirPods Pro', 'P005', 3, '音响耳机', 1999.00, 1, 0.00, 1999.00, '第二代 降噪', '', NOW(), NOW(), 'system', 'system', 0, false),

-- 订单2的商品
(3, 2, 2, '华为Mate 60', 'P002', 1, '手机数码', 4999.00, 1, 100.00, 4899.00, '256GB 黑色', 'VIP优惠', NOW(), NOW(), 'system', 'system', 0, false),

-- 订单3的商品
(4, 3, 5, 'AirPods Pro', 'P005', 3, '音响耳机', 1999.00, 1, 0.00, 1999.00, '第二代 降噪', '', NOW(), NOW(), 'system', 'system', 0, false),

-- 订单4的商品
(5, 4, 4, 'MacBook Pro 14', 'P004', 2, '电脑办公', 15999.00, 1, 500.00, 15499.00, 'M3芯片 512GB', '企业采购优惠', NOW(), NOW(), 'system', 'system', 0, false);

-- 插入测试收银操作日志数据
INSERT IGNORE INTO pos_operation_logs (
    id, store_no, pos_no, device_no, pos_type, operation_type, flow_id, shift_no,
    order_id, membership_card_id, occurrence_time, occurrence_time_dt, work_time,
    sync_status, sync_time, created_at, updated_at, created_by, updated_by, version, deleted
) VALUES
-- 上班操作
(1, 1001, 895, '8bf46c77-e33c-3534-8a5a-efa1602daade', 1, 10001, '', '250610094215',
 '', '', 1749519736000, FROM_UNIXTIME(1749519736), '上班：2025-06-10 09:42:15 - 下班：未下班',
 1, NOW(), NOW(), NOW(), 'system', 'system', 0, false),

-- 开始交易操作
(2, 1001, 895, '8bf46c77-e33c-3534-8a5a-efa1602daade', 1, 10005, '5asa710018951749519738154', '250610094215',
 '', '', 1749519754000, FROM_UNIXTIME(1749519754), '',
 1, NOW(), NOW(), NOW(), 'system', 'system', 0, false),

-- 会员操作
(3, 1001, 895, '8bf46c77-e33c-3534-8a5a-efa1602daade', 1, 10004, '5asa710018951749519738154', '250610094215',
 '', '8000046923', 1749519745000, FROM_UNIXTIME(1749519745), '',
 1, NOW(), NOW(), NOW(), 'system', 'system', 0, false),

-- 完成交易操作
(4, 1001, 895, '8bf46c77-e33c-3534-8a5a-efa1602daade', 1, 10031, '5asa710018951749519738154', '250610094215',
 '810018951749519777', '', 1749519780000, FROM_UNIXTIME(1749519780), '',
 1, NOW(), NOW(), NOW(), 'system', 'system', 0, false),

-- 下班操作
(5, 1001, 895, '8bf46c77-e33c-3534-8a5a-efa1602daade', 1, 10002, '', '250610094215',
 '', '', 1749520179000, FROM_UNIXTIME(1749520179), '上班：2025-06-10 09:42:15 - 下班：2025-06-10 09:49:38',
 1, NOW(), NOW(), NOW(), 'system', 'system', 0, false),

-- 另一个班次的上班操作
(6, 1001, 895, '8bf46c77-e33c-3534-8a5a-efa1602daade', 1, 10001, '', '250610094950',
 '', '', 1749520191000, FROM_UNIXTIME(1749520191), '上班：2025-06-10 09:49:50 - 下班：未下班',
 1, NOW(), NOW(), NOW(), 'system', 'system', 0, false),

-- 更多交易操作
(7, 1001, 895, '8bf46c77-e33c-3534-8a5a-efa1602daade', 1, 10005, 'U3zd710018951749520193782', '250610094950',
 '', '', 1749520198000, FROM_UNIXTIME(1749520198), '',
 1, NOW(), NOW(), NOW(), 'system', 'system', 0, false),

(8, 1001, 895, '8bf46c77-e33c-3534-8a5a-efa1602daade', 1, 10031, 'U3zd710018951749520193782', '250610094950',
 '810018951749520207', '', 1749520210000, FROM_UNIXTIME(1749520210), '',
 1, NOW(), NOW(), NOW(), 'system', 'system', 0, false);
