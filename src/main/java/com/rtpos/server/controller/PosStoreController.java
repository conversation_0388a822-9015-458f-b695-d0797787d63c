package com.rtpos.server.controller;

import com.rtpos.server.dto.PosStoreQueryResponse;
import com.rtpos.server.entity.PosStore;
import com.rtpos.server.service.PosStoreService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * POS门店信息管理控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/stores")
@RequiredArgsConstructor
@Tag(name = "POS门店管理", description = "POS门店信息查询和管理接口")
public class PosStoreController {

    private final PosStoreService storeService;

    @Operation(summary = "同步门店数据", description = "从外部API同步最新的门店数据到本地数据库")
    @PostMapping("/sync")
    public ResponseEntity<PosStoreQueryResponse> syncStores() {
        log.info("Starting manual sync of store data");

        try {
            PosStoreQueryResponse response = storeService.syncStoresFromApi();

            // 原封不动地转发外部API的响应数据
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Failed to sync store data", e);
            // 异常情况下返回错误响应，保持与外部API相同的结构
            PosStoreQueryResponse errorResponse = new PosStoreQueryResponse();
            errorResponse.setRsCode("99999999");
            errorResponse.setMsg("同步失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    @Operation(summary = "获取门店列表", description = "分页查询所有门店信息")
    @GetMapping
    public ResponseEntity<Page<PosStore>> getAllStores(
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "storeId") String sortBy,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "ASC") String sortDir) {
        
        Sort.Direction direction = Sort.Direction.fromString(sortDir);
        Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));
        
        Page<PosStore> stores = storeService.getAllStores(pageable);
        return ResponseEntity.ok(stores);
    }

    @Operation(summary = "根据门店ID查询", description = "根据门店ID查询具体门店信息")
    @GetMapping("/{storeId}")
    public ResponseEntity<PosStore> getStoreById(
            @Parameter(description = "门店ID") @PathVariable String storeId) {
        
        Optional<PosStore> store = storeService.getStoreById(storeId);
        return store.map(ResponseEntity::ok)
                   .orElse(ResponseEntity.notFound().build());
    }

    @Operation(summary = "搜索门店", description = "根据门店名称模糊搜索门店")
    @GetMapping("/search")
    public ResponseEntity<List<PosStore>> searchStores(
            @Parameter(description = "门店名称关键词") @RequestParam String keyword,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        List<PosStore> stores = storeService.searchStoresByName(keyword, pageable);
        return ResponseEntity.ok(stores);
    }

    @Operation(summary = "根据大区查询门店", description = "根据大区序号查询该大区下的所有门店")
    @GetMapping("/region/{pgSeq}")
    public ResponseEntity<List<PosStore>> getStoresByRegion(
            @Parameter(description = "大区序号") @PathVariable String pgSeq,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "50") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        List<PosStore> stores = storeService.getStoresByRegion(pgSeq, pageable);
        return ResponseEntity.ok(stores);
    }

    @Operation(summary = "根据子区域查询门店", description = "根据子区域ID查询该区域下的所有门店")
    @GetMapping("/subarea/{subId}")
    public ResponseEntity<List<PosStore>> getStoresBySubArea(
            @Parameter(description = "子区域ID") @PathVariable String subId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "50") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        List<PosStore> stores = storeService.getStoresBySubArea(subId, pageable);
        return ResponseEntity.ok(stores);
    }

    @Operation(summary = "获取所有大区", description = "获取所有大区列表")
    @GetMapping("/regions")
    public ResponseEntity<List<Map<String, String>>> getAllRegions() {
        List<Map<String, String>> regions = storeService.getAllRegions();
        return ResponseEntity.ok(regions);
    }

    @Operation(summary = "获取大区下的子区域", description = "获取指定大区下的所有子区域列表")
    @GetMapping("/regions/{pgSeq}/subareas")
    public ResponseEntity<List<Map<String, String>>> getSubAreasByRegion(
            @Parameter(description = "大区序号") @PathVariable String pgSeq) {
        
        List<Map<String, String>> subAreas = storeService.getSubAreasByRegion(pgSeq);
        return ResponseEntity.ok(subAreas);
    }

    @Operation(summary = "获取门店统计", description = "获取门店总数和各大区门店数量统计")
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getStoreStatistics() {
        Long totalCount = storeService.getTotalStoreCount();
        List<Map<String, Object>> regionCounts = storeService.getStoreCountByRegion();
        
        Map<String, Object> statistics = Map.of(
            "totalStores", totalCount,
            "regionStatistics", regionCounts
        );
        
        return ResponseEntity.ok(statistics);
    }

    @Operation(summary = "检查门店是否存在", description = "检查指定门店ID是否存在")
    @GetMapping("/{storeId}/exists")
    public ResponseEntity<Map<String, Boolean>> checkStoreExists(
            @Parameter(description = "门店ID") @PathVariable String storeId) {
        
        boolean exists = storeService.existsStore(storeId);
        return ResponseEntity.ok(Map.of("exists", exists));
    }

    @Operation(summary = "删除门店", description = "软删除指定门店（仅标记为删除状态）")
    @DeleteMapping("/{storeId}")
    public ResponseEntity<Map<String, String>> deleteStore(
            @Parameter(description = "门店ID") @PathVariable String storeId) {
        
        try {
            storeService.deleteStore(storeId);
            return ResponseEntity.ok(Map.of("message", "门店删除成功"));
        } catch (Exception e) {
            log.error("Failed to delete store: {}", storeId, e);
            return ResponseEntity.internalServerError()
                .body(Map.of("message", "删除失败: " + e.getMessage()));
        }
    }
}
