package com.rtpos.server.controller;

import com.rtpos.server.dto.PosOrderDTO;
import com.rtpos.server.dto.PosOrderQueryRequest;
import com.rtpos.server.dto.PosOrderQueryResponse;
import com.rtpos.server.service.PosOrderService;
import com.rtpos.server.service.PosOrderSyncService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * POS订单控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/pos-orders")
@RequiredArgsConstructor
@Tag(name = "POS订单管理", description = "POS订单数据查询和同步接口")
public class PosOrderController {

    private final PosOrderService posOrderService;
    private final PosOrderSyncService posOrderSyncService;

    @Operation(summary = "分页查询POS订单", description = "根据查询条件分页查询POS订单数据")
    @PostMapping("/query")
    public ResponseEntity<Page<PosOrderDTO>> queryPosOrders(@RequestBody PosOrderQueryRequest request) {
        log.info("Querying POS orders with request: {}", request);
        Page<PosOrderDTO> result = posOrderService.findPosOrders(request);
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "根据门店ID查询订单", description = "分页查询指定门店的POS订单")
    @GetMapping("/store/{storeId}")
    public ResponseEntity<Page<PosOrderDTO>> getOrdersByStore(
            @Parameter(description = "门店ID") @PathVariable String storeId,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "页面大小") @RequestParam(defaultValue = "20") int size) {
        
        Page<PosOrderDTO> result = posOrderService.findByStoreId(storeId, page, size);
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "根据时间范围查询订单", description = "查询指定门店和时间范围内的POS订单")
    @GetMapping("/store/{storeId}/time-range")
    public ResponseEntity<Page<PosOrderDTO>> getOrdersByStoreAndTimeRange(
            @Parameter(description = "门店ID") @PathVariable String storeId,
            @Parameter(description = "开始时间戳") @RequestParam Long startTime,
            @Parameter(description = "结束时间戳") @RequestParam Long endTime,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "页面大小") @RequestParam(defaultValue = "20") int size) {
        
        Page<PosOrderDTO> result = posOrderService.findByStoreIdAndTimeRange(storeId, startTime, endTime, page, size);
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "根据时间范围查询订单（日期时间）", description = "使用LocalDateTime查询指定门店和时间范围内的POS订单")
    @GetMapping("/store/{storeId}/datetime-range")
    public ResponseEntity<Page<PosOrderDTO>> getOrdersByStoreAndDateTimeRange(
            @Parameter(description = "门店ID") @PathVariable String storeId,
            @Parameter(description = "开始时间") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "页面大小") @RequestParam(defaultValue = "20") int size) {
        
        Page<PosOrderDTO> result = posOrderService.findByStoreIdAndTimeRange(storeId, startTime, endTime, page, size);
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "根据业务订单ID查询", description = "根据业务订单ID查询单个POS订单")
    @GetMapping("/biz-order/{bizOrderId}")
    public ResponseEntity<PosOrderDTO> getOrderByBizOrderId(
            @Parameter(description = "业务订单ID") @PathVariable Long bizOrderId) {
        
        PosOrderDTO result = posOrderService.findByBizOrderId(bizOrderId);
        return result != null ? ResponseEntity.ok(result) : ResponseEntity.notFound().build();
    }

    @Operation(summary = "根据外部订单ID查询", description = "根据外部订单ID查询单个POS订单")
    @GetMapping("/out-order/{outOrderId}")
    public ResponseEntity<PosOrderDTO> getOrderByOutOrderId(
            @Parameter(description = "外部订单ID") @PathVariable String outOrderId) {
        
        PosOrderDTO result = posOrderService.findByOutOrderId(outOrderId);
        return result != null ? ResponseEntity.ok(result) : ResponseEntity.notFound().build();
    }

    @Operation(summary = "获取门店统计信息", description = "获取指定门店的订单统计信息")
    @GetMapping("/store/{storeId}/statistics")
    public ResponseEntity<Map<String, Object>> getStoreStatistics(
            @Parameter(description = "门店ID") @PathVariable String storeId,
            @Parameter(description = "开始时间戳") @RequestParam(required = false) Long startTime,
            @Parameter(description = "结束时间戳") @RequestParam(required = false) Long endTime) {
        
        Map<String, Object> statistics = new HashMap<>();
        
        if (startTime != null && endTime != null) {
            Long orderCount = posOrderService.countByStoreIdAndTimeRange(storeId, startTime, endTime);
            Long totalSales = posOrderService.sumSalesByStoreIdAndTimeRange(storeId, startTime, endTime);
            
            statistics.put("orderCount", orderCount);
            statistics.put("totalSales", totalSales);
            statistics.put("timeRange", Map.of("startTime", startTime, "endTime", endTime));
        } else {
            Long orderCount = posOrderService.countByStoreId(storeId);
            statistics.put("orderCount", orderCount);
        }
        
        statistics.put("storeId", storeId);
        
        return ResponseEntity.ok(statistics);
    }

    @Operation(summary = "从外部API同步订单", description = "从外部接口同步POS订单数据到本地数据库")
    @PostMapping("/sync")
    public ResponseEntity<PosOrderQueryResponse> syncOrdersFromApi(@RequestBody PosOrderQueryRequest request) {
        log.info("Syncing orders from API with request: {}", request);
        PosOrderQueryResponse result = posOrderSyncService.syncPosOrdersFromApi(request);
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "同步指定门店订单", description = "同步指定门店和时间范围内的订单数据")
    @PostMapping("/sync/store/{storeId}")
    public ResponseEntity<Map<String, String>> syncOrdersByStore(
            @Parameter(description = "门店ID") @PathVariable String storeId,
            @Parameter(description = "开始时间戳") @RequestParam Long startTime,
            @Parameter(description = "结束时间戳") @RequestParam Long endTime) {
        
        try {
            posOrderSyncService.syncPosOrdersByStore(storeId, startTime, endTime);
            
            Map<String, String> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "订单同步完成");
            response.put("storeId", storeId);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Failed to sync orders for store: {}", storeId, e);
            
            Map<String, String> response = new HashMap<>();
            response.put("status", "error");
            response.put("message", "订单同步失败: " + e.getMessage());
            response.put("storeId", storeId);
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @Operation(summary = "增量同步订单", description = "执行增量同步，同步最新的订单数据")
    @PostMapping("/sync/incremental")
    public ResponseEntity<Map<String, String>> incrementalSync() {
        try {
            posOrderSyncService.incrementalSyncPosOrders();
            
            Map<String, String> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "增量同步完成");
            response.put("timestamp", String.valueOf(System.currentTimeMillis()));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Failed to perform incremental sync", e);
            
            Map<String, String> response = new HashMap<>();
            response.put("status", "error");
            response.put("message", "增量同步失败: " + e.getMessage());
            response.put("timestamp", String.valueOf(System.currentTimeMillis()));
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @Operation(summary = "获取最新订单", description = "获取最新的订单数据，用于监控")
    @GetMapping("/latest")
    public ResponseEntity<List<PosOrderDTO>> getLatestOrders(
            @Parameter(description = "返回数量") @RequestParam(defaultValue = "10") int limit) {
        
        List<PosOrderDTO> result = posOrderService.getLatestOrders(limit);
        return ResponseEntity.ok(result);
    }
}
