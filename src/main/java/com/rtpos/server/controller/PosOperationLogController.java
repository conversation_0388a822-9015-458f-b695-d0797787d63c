package com.rtpos.server.controller;

import com.rtpos.server.dto.PosOperationLogQueryRequest;
import com.rtpos.server.dto.PosOperationLogQueryResponse;
import com.rtpos.server.entity.PosOperationLog;
import com.rtpos.server.repository.PosOperationLogRepository;
import com.rtpos.server.service.PosOperationLogSyncService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * POS收银操作日志控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/pos-operation-logs")
@RequiredArgsConstructor
@Tag(name = "POS收银操作日志", description = "POS收银操作日志相关接口")
public class PosOperationLogController {

    private final PosOperationLogSyncService operationLogSyncService;
    private final PosOperationLogRepository operationLogRepository;

    @Operation(summary = "从外部API同步操作日志", description = "从外部接口同步POS收银操作日志数据到本地数据库")
    @PostMapping("/sync")
    public ResponseEntity<PosOperationLogQueryResponse> syncOperationLogsFromApi(@RequestBody PosOperationLogQueryRequest request) {
        log.info("Syncing operation logs from API with request: {}", request);
        PosOperationLogQueryResponse result = operationLogSyncService.syncOperationLogsFromApi(request);
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "同步指定门店和POS机的操作日志", description = "同步指定门店、POS机和时间范围内的操作日志数据")
    @PostMapping("/sync/store/{storeNo}/pos/{posNo}")
    public ResponseEntity<Map<String, String>> syncOperationLogsByStoreAndPos(
            @Parameter(description = "门店编号") @PathVariable Integer storeNo,
            @Parameter(description = "POS机编号") @PathVariable Integer posNo,
            @Parameter(description = "开始时间戳") @RequestParam Long startTime,
            @Parameter(description = "结束时间戳") @RequestParam Long endTime) {
        
        log.info("Syncing operation logs for store: {}, pos: {}, timeRange: {} - {}", 
                storeNo, posNo, startTime, endTime);
        
        try {
            operationLogSyncService.syncOperationLogsByStoreAndPos(storeNo, posNo, startTime, endTime);
            
            Map<String, String> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "操作日志同步完成");
            response.put("storeNo", storeNo.toString());
            response.put("posNo", posNo.toString());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Failed to sync operation logs for store: {}, pos: {}", storeNo, posNo, e);
            
            Map<String, String> response = new HashMap<>();
            response.put("status", "error");
            response.put("message", "操作日志同步失败: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @Operation(summary = "增量同步操作日志", description = "增量同步POS收银操作日志数据")
    @PostMapping("/sync/incremental")
    public ResponseEntity<Map<String, String>> incrementalSyncOperationLogs() {
        log.info("Starting incremental sync for operation logs");
        
        try {
            operationLogSyncService.incrementalSyncOperationLogs();
            
            Map<String, String> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "增量同步操作日志完成");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Failed to perform incremental sync for operation logs", e);
            
            Map<String, String> response = new HashMap<>();
            response.put("status", "error");
            response.put("message", "增量同步操作日志失败: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @Operation(summary = "全量同步操作日志", description = "全量同步指定门店和POS机的操作日志数据")
    @PostMapping("/sync/full")
    public ResponseEntity<Map<String, String>> fullSyncOperationLogs(
            @Parameter(description = "门店编号") @RequestParam Integer storeNo,
            @Parameter(description = "POS机编号") @RequestParam Integer posNo,
            @Parameter(description = "开始时间戳") @RequestParam Long startTime,
            @Parameter(description = "结束时间戳") @RequestParam Long endTime) {
        
        log.info("Starting full sync for operation logs: store {}, pos {}", storeNo, posNo);
        
        try {
            operationLogSyncService.fullSyncOperationLogs(storeNo, posNo, startTime, endTime);
            
            Map<String, String> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "全量同步操作日志完成");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Failed to perform full sync for operation logs", e);
            
            Map<String, String> response = new HashMap<>();
            response.put("status", "error");
            response.put("message", "全量同步操作日志失败: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @Operation(summary = "批量同步操作日志", description = "批量同步多个门店和POS机的操作日志")
    @PostMapping("/sync/batch")
    public ResponseEntity<Map<String, String>> batchSyncOperationLogs(
            @Parameter(description = "门店编号列表，逗号分隔") @RequestParam String storeNos,
            @Parameter(description = "POS机编号列表，逗号分隔") @RequestParam String posNos,
            @Parameter(description = "开始时间戳") @RequestParam Long startTime,
            @Parameter(description = "结束时间戳") @RequestParam Long endTime) {
        
        log.info("Starting batch sync for operation logs: stores {}, poses {}", storeNos, posNos);
        
        try {
            operationLogSyncService.batchSyncOperationLogs(storeNos, posNos, startTime, endTime);
            
            Map<String, String> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "批量同步操作日志完成");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Failed to perform batch sync for operation logs", e);
            
            Map<String, String> response = new HashMap<>();
            response.put("status", "error");
            response.put("message", "批量同步操作日志失败: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @Operation(summary = "查询操作日志", description = "根据条件查询POS收银操作日志")
    @GetMapping("/query")
    public ResponseEntity<Page<PosOperationLog>> queryOperationLogs(
            @Parameter(description = "门店编号") @RequestParam(required = false) Integer storeNo,
            @Parameter(description = "POS机编号") @RequestParam(required = false) Integer posNo,
            @Parameter(description = "操作类型") @RequestParam(required = false) Integer operationType,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "occurrenceTime"));
        
        // 这里可以根据参数构建查询条件
        // 简化实现，返回所有数据
        Page<PosOperationLog> result = operationLogRepository.findAll(pageable);
        
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "根据订单ID查询操作日志", description = "查询指定订单的所有操作日志")
    @GetMapping("/order/{orderId}")
    public ResponseEntity<List<PosOperationLog>> getOperationLogsByOrderId(
            @Parameter(description = "订单ID") @PathVariable String orderId) {
        
        List<PosOperationLog> logs = operationLogRepository.findByOrderId(orderId);
        return ResponseEntity.ok(logs);
    }

    @Operation(summary = "根据班次号查询操作日志", description = "查询指定班次的所有操作日志")
    @GetMapping("/shift/{shiftNo}")
    public ResponseEntity<List<PosOperationLog>> getOperationLogsByShiftNo(
            @Parameter(description = "班次号") @PathVariable String shiftNo) {
        
        List<PosOperationLog> logs = operationLogRepository.findByShiftNo(shiftNo);
        return ResponseEntity.ok(logs);
    }

    @Operation(summary = "根据流水ID查询操作日志", description = "查询指定流水的所有操作日志")
    @GetMapping("/flow/{flowId}")
    public ResponseEntity<List<PosOperationLog>> getOperationLogsByFlowId(
            @Parameter(description = "流水ID") @PathVariable String flowId) {
        
        List<PosOperationLog> logs = operationLogRepository.findByFlowId(flowId);
        return ResponseEntity.ok(logs);
    }
}
