package com.rtpos.server.controller;

import com.rtpos.server.dto.auth.*;
import com.rtpos.server.service.UserAuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 用户认证控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
@Validated
@Tag(name = "用户认证", description = "用户登录认证相关接口")
public class UserAuthController {

    private final UserAuthService userAuthService;

    @Operation(summary = "发送验证码", description = "向指定手机号发送6位验证码")
    @PostMapping("/send-code")
    public ResponseEntity<POSAuthResponse<Object>> sendCode(
            @Parameter(description = "发送验证码请求") @Valid @RequestBody SendCodeRequest request) {
        log.info("Sending verification code to mobile: {}", request.getMobile());
        
        AuthResponse<Object> result = userAuthService.sendCode(request);
        POSAuthResponse<Object> posResult = new POSAuthResponse();
        if (result.getCode() == 200) {
            posResult.setRsCode("00000000");
        } else {
            posResult.setRsCode("9999-" + result.getCode().toString());
        }
        posResult.setBody(result.getData());
        posResult.setMsg(result.getMsg());
        return ResponseEntity.ok(posResult);
    }

    @Operation(summary = "验证码登录", description = "使用手机号和验证码进行登录")
    @PostMapping("/login")
    public ResponseEntity<POSAuthResponse<LoginData>> login(
            @Parameter(description = "登录请求") @Valid @RequestBody LoginRequest request) {
        log.info("User login attempt with mobile: {}", request.getMobile());
        
        AuthResponse<LoginData> result = userAuthService.login(request);
        POSAuthResponse<LoginData> posResult = new POSAuthResponse();
        if (result.getCode() == 200) {
            posResult.setRsCode("00000000");
        } else {
            posResult.setRsCode("9999-" + result.getCode().toString());
        }
        posResult.setBody(result.getData());
        posResult.setMsg(result.getMsg());
        return ResponseEntity.ok(posResult);
    }

    @Operation(summary = "查询员工部门角色", description = "根据员工ID查询员工的部门和角色信息")
    @PostMapping("/query-emp-dept-role")
    public ResponseEntity<AuthResponse<List<EmpDeptRole>>> queryEmpDeptRole(
            @Parameter(description = "查询员工信息请求") @Valid @RequestBody QueryEmpRequest request) {
        log.info("Querying employee dept role for empId: {}", request.getEmpId());
        
        AuthResponse<List<EmpDeptRole>> result = userAuthService.queryEmpDeptRole(request);
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "用户登出", description = "使用token进行用户登出")
    @PostMapping("/logout")
    public ResponseEntity<POSAuthResponse<Object>> logout(
            @Parameter(description = "登出请求") @Valid @RequestBody LogoutRequest request) {
        log.info("User logout attempt with token: {}", request.getToken());

        POSAuthResponse<Object> result = userAuthService.logout(request);
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "验证token", description = "验证token是否有效")
    @GetMapping("/validate-token")
    public ResponseEntity<POSAuthResponse<String>> validateToken(
            @Parameter(description = "用户token", required = true) @RequestParam String token) {
        log.info("Validating token: {}", token);

        POSAuthResponse<String> result = userAuthService.validateToken(token);
        return ResponseEntity.ok(result);
    }
}
