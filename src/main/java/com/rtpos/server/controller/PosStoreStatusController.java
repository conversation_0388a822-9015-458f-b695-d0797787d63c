package com.rtpos.server.controller;

import com.rtpos.server.dto.PosStoreStatusRequest;
import com.rtpos.server.dto.PosStoreStatusResponse;
import com.rtpos.server.service.PosStoreStatusService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * POS门店状态控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/pos/store-status")
@RequiredArgsConstructor
@Validated
@Tag(name = "POS门店状态管理", description = "POS门店在线、离线、上下机状态查询接口")
public class PosStoreStatusController {

    private final PosStoreStatusService storeStatusService;

    @Operation(summary = "获取门店状态信息", 
               description = "获取指定门店下的在线、离线、上下机信息，包括总设备数量、在线离线机台列表等")
    @PostMapping("/query")
    public ResponseEntity<PosStoreStatusResponse> getStoreStatus(
            @Parameter(description = "门店状态查询请求") @Valid @RequestBody PosStoreStatusRequest request) {
        
        log.info("Querying store status for store: {}", request.getStoreNo());
        
        try {
            PosStoreStatusResponse result = storeStatusService.getStoreStatus(request);
            
            // 原封不动地转发外部API的响应数据
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("Failed to query store status for store: {}", request.getStoreNo(), e);
            
            // 异常情况下返回错误响应，保持与外部API相同的结构
            PosStoreStatusResponse errorResponse = new PosStoreStatusResponse();
            errorResponse.setRsCode("99999999");
            errorResponse.setMsg("查询门店状态失败: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    @Operation(summary = "获取门店状态信息（GET方式）", 
               description = "通过GET请求获取指定门店下的在线、离线、上下机信息")
    @GetMapping("/query/{storeNo}")
    public ResponseEntity<PosStoreStatusResponse> getStoreStatusByGet(
            @Parameter(description = "门店编号") @PathVariable Integer storeNo) {
        
        log.info("Querying store status for store: {} (GET method)", storeNo);
        
        try {
            PosStoreStatusRequest request = new PosStoreStatusRequest();
            request.setStoreNo(storeNo);
            
            PosStoreStatusResponse result = storeStatusService.getStoreStatus(request);
            
            // 原封不动地转发外部API的响应数据
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("Failed to query store status for store: {} (GET method)", storeNo, e);
            
            // 异常情况下返回错误响应，保持与外部API相同的结构
            PosStoreStatusResponse errorResponse = new PosStoreStatusResponse();
            errorResponse.setRsCode("99999999");
            errorResponse.setMsg("查询门店状态失败: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
}
