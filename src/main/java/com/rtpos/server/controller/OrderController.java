package com.rtpos.server.controller;

import com.rtpos.server.dto.ApiResponse;
import com.rtpos.server.dto.OrderDTO;
import com.rtpos.server.entity.Order;
import com.rtpos.server.service.OrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/orders")
@RequiredArgsConstructor
@Validated
@Tag(name = "订单管理", description = "订单相关API")
public class OrderController {

    private final OrderService orderService;

    @Operation(summary = "创建订单", description = "创建新的订单")
    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<ApiResponse<OrderDTO>> createOrder(@Valid @RequestBody OrderDTO orderDTO) {
        log.info("Creating order: {}", orderDTO.getOrderNo());
        OrderDTO result = orderService.createOrder(orderDTO);
        return ResponseEntity.ok(ApiResponse.success("订单创建成功", result));
    }

    @Operation(summary = "更新订单", description = "根据ID更新订单信息")
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<ApiResponse<OrderDTO>> updateOrder(
            @Parameter(description = "订单ID") @PathVariable @NotNull Long id,
            @Valid @RequestBody OrderDTO orderDTO) {
        log.info("Updating order: {}", id);
        OrderDTO result = orderService.updateOrder(id, orderDTO);
        return ResponseEntity.ok(ApiResponse.success("订单更新成功", result));
    }

    @Operation(summary = "查询订单", description = "根据ID查询订单详情")
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<ApiResponse<OrderDTO>> getOrder(
            @Parameter(description = "订单ID") @PathVariable @NotNull Long id) {
        log.info("Getting order: {}", id);
        OrderDTO result = orderService.findById(id);
        return ResponseEntity.ok(ApiResponse.success(result));
    }

    @Operation(summary = "根据订单号查询", description = "根据订单号查询订单详情")
    @GetMapping("/orderNo/{orderNo}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<ApiResponse<OrderDTO>> getOrderByOrderNo(
            @Parameter(description = "订单号") @PathVariable String orderNo) {
        log.info("Getting order by orderNo: {}", orderNo);
        OrderDTO result = orderService.findByOrderNo(orderNo);
        return ResponseEntity.ok(ApiResponse.success(result));
    }

    @Operation(summary = "分页查询订单", description = "分页查询所有订单")
    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<ApiResponse<Page<OrderDTO>>> getOrders(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "createdAt") String sort,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String direction) {

        Sort.Direction sortDirection = "asc".equalsIgnoreCase(direction) ? Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));

        Page<OrderDTO> result = orderService.findAll(pageable);
        return ResponseEntity.ok(ApiResponse.success(result));
    }

    @Operation(summary = "根据门店查询订单", description = "根据门店ID分页查询订单")
    @GetMapping("/store/{storeId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<ApiResponse<Page<OrderDTO>>> getOrdersByStore(
            @Parameter(description = "门店ID") @PathVariable @NotNull Long storeId,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<OrderDTO> result = orderService.findByStoreId(storeId, pageable);
        return ResponseEntity.ok(ApiResponse.success(result));
    }

    @Operation(summary = "根据状态查询订单", description = "根据订单状态分页查询订单")
    @GetMapping("/status/{status}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<ApiResponse<Page<OrderDTO>>> getOrdersByStatus(
            @Parameter(description = "订单状态") @PathVariable Order.OrderStatus status,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<OrderDTO> result = orderService.findByStatus(status, pageable);
        return ResponseEntity.ok(ApiResponse.success(result));
    }

    @Operation(summary = "时间范围查询订单", description = "根据时间范围查询订单")
    @GetMapping("/timeRange")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<ApiResponse<List<OrderDTO>>> getOrdersByTimeRange(
            @Parameter(description = "开始时间") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @Parameter(description = "门店ID（可选）") @RequestParam(required = false) Long storeId) {

        List<OrderDTO> result;
        if (storeId != null) {
            result = orderService.findByStoreIdAndOrderTimeBetween(storeId, startTime, endTime);
        } else {
            result = orderService.findByOrderTimeBetween(startTime, endTime);
        }

        return ResponseEntity.ok(ApiResponse.success(result));
    }

    @Operation(summary = "删除订单", description = "软删除订单")
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> deleteOrder(
            @Parameter(description = "订单ID") @PathVariable @NotNull Long id) {
        log.info("Deleting order: {}", id);
        orderService.deleteOrder(id);
        return ResponseEntity.ok(ApiResponse.<Void>success("订单删除成功", null));
    }

    @Operation(summary = "支付订单", description = "处理订单支付")
    @PostMapping("/{id}/pay")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<ApiResponse<OrderDTO>> payOrder(
            @Parameter(description = "订单ID") @PathVariable @NotNull Long id,
            @Parameter(description = "支付方式") @RequestParam String paymentMethod) {
        log.info("Processing payment for order: {} with method: {}", id, paymentMethod);
        OrderDTO result = orderService.payOrder(id, paymentMethod);
        return ResponseEntity.ok(ApiResponse.success("订单支付成功", result));
    }

    @Operation(summary = "完成订单", description = "标记订单为已完成")
    @PostMapping("/{id}/complete")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<ApiResponse<OrderDTO>> completeOrder(
            @Parameter(description = "订单ID") @PathVariable @NotNull Long id) {
        log.info("Completing order: {}", id);
        OrderDTO result = orderService.completeOrder(id);
        return ResponseEntity.ok(ApiResponse.success("订单完成成功", result));
    }

    @Operation(summary = "取消订单", description = "取消订单")
    @PostMapping("/{id}/cancel")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<ApiResponse<OrderDTO>> cancelOrder(
            @Parameter(description = "订单ID") @PathVariable @NotNull Long id) {
        log.info("Cancelling order: {}", id);
        OrderDTO result = orderService.cancelOrder(id);
        return ResponseEntity.ok(ApiResponse.success("订单取消成功", result));
    }

    @Operation(summary = "门店今日统计", description = "获取门店今日订单统计")
    @GetMapping("/stats/today/{storeId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<ApiResponse<Object>> getTodayStats(
            @Parameter(description = "门店ID") @PathVariable @NotNull Long storeId) {

        Long orderCount = orderService.countTodayOrdersByStoreId(storeId);
        Double salesAmount = orderService.sumTodaySalesByStoreId(storeId);

        // 创建统计结果Map
        java.util.Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("orderCount", orderCount);
        stats.put("salesAmount", salesAmount);

        return ResponseEntity.ok(ApiResponse.success(stats));
    }

    @Operation(summary = "同步订单数据", description = "手动触发订单数据同步")
    @PostMapping("/sync")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> syncOrders() {
        log.info("Manual order sync triggered");
        orderService.syncOrders();
        return ResponseEntity.ok(ApiResponse.<Void>success("订单同步完成", null));
    }
}
