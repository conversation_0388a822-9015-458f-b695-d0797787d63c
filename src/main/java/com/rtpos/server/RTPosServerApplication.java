package com.rtpos.server;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * RTPosServer 主应用程序入口
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@SpringBootApplication
@EnableCaching
@EnableAsync
@EnableScheduling
public class RTPosServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(RTPosServerApplication.class, args);
    }
}
