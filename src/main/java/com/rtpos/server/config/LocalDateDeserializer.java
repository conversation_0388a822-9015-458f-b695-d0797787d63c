package com.rtpos.server.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * 自定义LocalDate反序列化器
 * 处理空字符串和null值
 * 
 * <AUTHOR>
 */
@Slf4j
public class LocalDateDeserializer extends JsonDeserializer<LocalDate> {

    private static final DateTimeFormatter[] FORMATTERS = {
        DateTimeFormatter.ofPattern("yyyy-MM-dd"),
        DateTimeFormatter.ofPattern("yyyy/MM/dd"),
        DateTimeFormatter.ofPattern("yyyy-M-d"),
        DateTimeFormatter.ofPattern("yyyy/M/d")
    };

    @Override
    public LocalDate deserialize(JsonParser parser, DeserializationContext context) throws IOException {
        String dateString = parser.getText();
        
        // 处理null或空字符串
        if (dateString == null || dateString.trim().isEmpty()) {
            log.debug("Date string is null or empty, returning null");
            return null;
        }
        
        dateString = dateString.trim();
        
        // 尝试使用不同的格式解析日期
        for (DateTimeFormatter formatter : FORMATTERS) {
            try {
                LocalDate date = LocalDate.parse(dateString, formatter);
                log.debug("Successfully parsed date: {} -> {}", dateString, date);
                return date;
            } catch (DateTimeParseException e) {
                // 继续尝试下一个格式
                log.debug("Failed to parse date '{}' with format {}", dateString, formatter.toString());
            }
        }
        
        // 如果所有格式都失败，记录错误并返回null
        log.warn("Unable to parse date string: '{}', returning null", dateString);
        return null;
    }
}
