package com.rtpos.server.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import redis.embedded.RedisServer;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

/**
 * 嵌入式Redis配置
 * 仅在开发环境使用，避免需要单独安装Redis
 * 生产环境请使用独立的Redis服务器
 *
 * <AUTHOR>
 */
@Configuration
@Profile("dev") // 仅在开发环境启用，生产环境禁用
public class EmbeddedRedisConfig {

    @Value("${spring.data.redis.port:6379}")
    private int redisPort;

    private RedisServer redisServer;

    /**
     * 启动嵌入式Redis服务器
     */
    @PostConstruct
    public void startRedis() {
        try {
            redisServer = new RedisServer(redisPort);
            redisServer.start();
            System.out.println("🚀 Embedded Redis server started on port: " + redisPort);
        } catch (Exception e) {
            System.err.println("❌ Failed to start embedded Redis server: " + e.getMessage());
            // 如果启动失败，不影响应用启动
        }
    }

    /**
     * 停止嵌入式Redis服务器
     */
    @PreDestroy
    public void stopRedis() {
        if (redisServer != null && redisServer.isActive()) {
            redisServer.stop();
            System.out.println("🛑 Embedded Redis server stopped");
        }
    }
}
