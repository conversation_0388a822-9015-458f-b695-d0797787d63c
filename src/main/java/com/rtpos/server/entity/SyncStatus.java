package com.rtpos.server.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * 同步状态实体类
 *
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "sync_status", indexes = {
    @Index(name = "idx_sync_type_store", columnList = "syncType,storeId"),
    @Index(name = "idx_sync_time", columnList = "syncTime"),
    @Index(name = "idx_created_time", columnList = "createdTime")
})
@EqualsAndHashCode(callSuper = true)
public class SyncStatus extends BaseEntity {

    /**
     * 同步类型（incremental, full, progressive等）
     */
    @Column(name = "sync_type", nullable = false, length = 50)
    private String syncType;

    /**
     * 门店ID
     */
    @Column(name = "store_id", nullable = false, length = 50)
    private String storeId;

    /**
     * 同步开始时间戳
     */
    @Column(name = "start_time")
    private Long startTime;

    /**
     * 同步结束时间戳
     */
    @Column(name = "end_time")
    private Long endTime;

    /**
     * 同步时间
     */
    @Column(name = "sync_time", nullable = false)
    private LocalDateTime syncTime;

    /**
     * 同步状态：SUCCESS, FAILURE, RUNNING
     */
    @Column(name = "status", nullable = false, length = 20)
    private String status;

    /**
     * 同步记录数量
     */
    @Column(name = "record_count")
    private Integer recordCount;

    /**
     * 错误信息
     */
    @Column(name = "error_message", length = 1000)
    private String errorMessage;

    /**
     * 耗时（毫秒）
     */
    @Column(name = "duration_ms")
    private Long durationMs;

    /**
     * 同步状态枚举
     */
    public enum Status {
        RUNNING, SUCCESS, FAILURE
    }

    /**
     * 同步类型枚举
     */
    public enum Type {
        // 订单同步类型
        ORDER_INCREMENTAL, ORDER_FULL, ORDER_PROGRESSIVE, ORDER_CHECK_MISSING,

        // 收银员日志同步类型
        OPERATION_LOG_INCREMENTAL, OPERATION_LOG_FULL, OPERATION_LOG_CHECK_MISSING,

        // 通用类型（向后兼容）
        INCREMENTAL, FULL, PROGRESSIVE, CHECK_MISSING
    }
}
