package com.rtpos.server.entity;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * POS门店信息实体类
 * 对应外部接口 getPosStore 返回的门店数据
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "pos_stores", indexes = {
    @Index(name = "idx_store_id", columnList = "storeId", unique = true),
    @Index(name = "idx_store_name", columnList = "storeName"),
    @Index(name = "idx_pg_seq", columnList = "pgSeq"),
    @Index(name = "idx_sub_id", columnList = "subId")
})
public class PosStore extends BaseEntity {

    /**
     * 门店ID
     */
    @Column(name = "store_id", length = 20, nullable = false, unique = true)
    private String storeId;

    /**
     * 门店名称
     */
    @Column(name = "store_name", length = 200, nullable = false)
    private String storeName;

    /**
     * 大区序号
     */
    @Column(name = "pg_seq", length = 10)
    private String pgSeq;

    /**
     * 大区名称
     */
    @Column(name = "pg_name", length = 100)
    private String pgName;

    /**
     * 子区域ID
     */
    @Column(name = "sub_id", length = 20)
    private String subId;

    /**
     * 子区域名称
     */
    @Column(name = "sub_area", length = 100)
    private String subArea;

    /**
     * 同步状态：0-待同步，1-已同步，2-同步失败
     */
    @Column(name = "sync_status")
    private Integer syncStatus = 0;

    /**
     * 同步失败原因
     */
    @Column(name = "sync_error", length = 500)
    private String syncError;

    /**
     * 数据版本（用于增量同步）
     */
    @Column(name = "data_version")
    private String dataVersion;

    /**
     * 预处理设置同步状态
     */
    @PrePersist
    @PreUpdate
    public void prePersist() {
        if (syncStatus == null) {
            syncStatus = 0;
        }
    }
}
