package com.rtpos.server.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 商品实体类
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "products", indexes = {
    @Index(name = "idx_product_code", columnList = "productCode"),
    @Index(name = "idx_category_id", columnList = "categoryId"),
    @Index(name = "idx_status", columnList = "status")
})
public class Product extends BaseEntity {

    @Column(name = "product_code", nullable = false, unique = true, length = 50)
    private String productCode;

    @Column(name = "product_name", nullable = false, length = 200)
    private String productName;

    @Column(name = "category_id")
    private Long categoryId;

    @Column(name = "category_name", length = 100)
    private String categoryName;

    @Column(name = "brand", length = 100)
    private String brand;

    @Column(name = "unit", length = 20)
    private String unit;

    @Column(name = "price", nullable = false, precision = 10, scale = 2)
    private BigDecimal price;

    @Column(name = "cost_price", precision = 10, scale = 2)
    private BigDecimal costPrice;

    @Column(name = "stock_quantity")
    private Integer stockQuantity;

    @Column(name = "min_stock")
    private Integer minStock;

    @Column(name = "max_stock")
    private Integer maxStock;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private ProductStatus status;

    @Column(name = "description", length = 1000)
    private String description;

    @Column(name = "image_url", length = 500)
    private String imageUrl;

    @Column(name = "barcode", length = 50)
    private String barcode;

    @Column(name = "specifications", length = 500)
    private String specifications;

    /**
     * 商品状态枚举
     */
    public enum ProductStatus {
        ACTIVE("正常"),
        INACTIVE("停用"),
        OUT_OF_STOCK("缺货");

        private final String description;

        ProductStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
