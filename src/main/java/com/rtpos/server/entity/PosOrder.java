package com.rtpos.server.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * POS订单实体类
 * 对应外部接口 querySelfPosNodeOrders 返回的订单数据
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "pos_orders", indexes = {
    @Index(name = "idx_pos_store_id", columnList = "storeId"),
    @Index(name = "idx_pos_biz_order_id", columnList = "bizOrderId"),
    @Index(name = "idx_pos_main_order_id", columnList = "mainOrderId"),
    @Index(name = "idx_pos_out_order_id", columnList = "outOrderId"),
    @Index(name = "idx_pos_order_time", columnList = "orderTime"),
    @Index(name = "idx_pos_pay_time", columnList = "payTime"),
    @Index(name = "idx_pos_order_status", columnList = "orderStatus"),
    @Index(name = "idx_pos_operator_id", columnList = "operatorId"),
    @Index(name = "idx_pos_device_id", columnList = "deviceId"),
    @Index(name = "idx_pos_no", columnList = "posNo")
})
public class PosOrder extends BaseEntity {

    /**
     * 门店ID
     */
    @Column(name = "store_id", nullable = false, length = 20)
    private String storeId;

    /**
     * 数据源：1-自建POS，2-第三方POS
     */
    @Column(name = "source")
    private Integer source;

    /**
     * 业务订单ID
     */
    @Column(name = "biz_order_id", nullable = false)
    private Long bizOrderId;

    /**
     * 主订单ID
     */
    @Column(name = "main_order_id")
    private Long mainOrderId;

    /**
     * 外部订单ID
     */
    @Column(name = "out_order_id", length = 50)
    private String outOrderId;

    /**
     * 支付时间（时间戳）
     */
    @Column(name = "pay_time")
    private Long payTime;

    /**
     * 支付时间（LocalDateTime）
     */
    @Column(name = "pay_time_dt")
    private LocalDateTime payTimeDateTime;

    /**
     * 下单时间（时间戳）
     */
    @Column(name = "order_time")
    private Long orderTime;

    /**
     * 下单时间（LocalDateTime）
     */
    @Column(name = "order_time_dt")
    private LocalDateTime orderTimeDateTime;

    /**
     * 订单状态：1-已完成，2-已取消，3-进行中
     */
    @Column(name = "order_status")
    private Integer orderStatus;

    /**
     * 操作员ID
     */
    @Column(name = "operator_id", length = 50)
    private String operatorId;

    /**
     * 操作员姓名
     */
    @Column(name = "operator_name", length = 100)
    private String operatorName;

    /**
     * 原始金额（分）
     */
    @Column(name = "original_amt")
    private Integer originalAmt;

    /**
     * 原始金额（元）
     */
    @Column(name = "original_amount", precision = 10, scale = 2)
    private BigDecimal originalAmount;

    /**
     * 折扣金额（分）
     */
    @Column(name = "discount_amt")
    private Integer discountAmt;

    /**
     * 折扣金额（元）
     */
    @Column(name = "discount_amount", precision = 10, scale = 2)
    private BigDecimal discountAmount;

    /**
     * 会员折扣金额（分）
     */
    @Column(name = "member_discount_amt")
    private Integer memberDiscountAmt;

    /**
     * 会员折扣金额（元）
     */
    @Column(name = "member_discount_amount", precision = 10, scale = 2)
    private BigDecimal memberDiscountAmount;

    /**
     * 包装费（分）
     */
    @Column(name = "packing_fee")
    private Integer packingFee;

    /**
     * 包装费（元）
     */
    @Column(name = "packing_fee_amount", precision = 10, scale = 2)
    private BigDecimal packingFeeAmount;

    /**
     * 包装费税率
     */
    @Column(name = "packing_fee_tax_rate", precision = 6, scale = 3)
    private BigDecimal packingFeeTaxRate;

    /**
     * 会员卡号
     */
    @Column(name = "member_card_num", length = 50)
    private String memberCardNum;

    /**
     * POS机号
     */
    @Column(name = "pos_no")
    private Integer posNo;

    /**
     * 设备ID
     */
    @Column(name = "device_id", length = 100)
    private String deviceId;

    /**
     * MQ时间戳
     */
    @Column(name = "mq_timestamp")
    private Long mqTimestamp;

    /**
     * 班次代码
     */
    @Column(name = "duty_code", length = 100)
    private String dutyCode;

    /**
     * 扩展JSON字符串
     */
    @Column(name = "extended_json", columnDefinition = "TEXT")
    private String extendedJson;

    /**
     * 删除标记（原始字段）
     */
    @Column(name = "original_deleted")
    private Integer originalDeleted;

    /**
     * 删除时间
     */
    @Column(name = "deleted_time")
    private Long deletedTime;

    /**
     * 任务状态
     */
    @Column(name = "job_status")
    private Integer jobStatus;

    /**
     * 任务次数
     */
    @Column(name = "job_times")
    private Integer jobTimes;

    /**
     * 原始创建时间
     */
    @Column(name = "gmt_create")
    private Long gmtCreate;

    /**
     * 原始修改时间
     */
    @Column(name = "gmt_modified")
    private Long gmtModified;

    /**
     * 主扩展JSON模型（存储为JSON字符串）
     */
    @Column(name = "main_extended_json_model", columnDefinition = "TEXT")
    private String mainExtendedJsonModel;
}
