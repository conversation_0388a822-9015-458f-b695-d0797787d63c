package com.rtpos.server.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * POS收银操作日志实体类
 * 对应外部接口 getOperationLogInfo 返回的操作日志数据
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "pos_operation_logs", indexes = {
    @Index(name = "idx_operation_store_no", columnList = "storeNo"),
    @Index(name = "idx_operation_pos_no", columnList = "posNo"),
    @Index(name = "idx_operation_device_no", columnList = "deviceNo"),
    @Index(name = "idx_operation_type", columnList = "operationType"),
    @Index(name = "idx_operation_flow_id", columnList = "flowId"),
    @Index(name = "idx_operation_shift_no", columnList = "shiftNo"),
    @Index(name = "idx_operation_order_id", columnList = "orderId"),
    @Index(name = "idx_operation_occurrence_time", columnList = "occurrenceTime"),
    @Index(name = "idx_operation_membership_card_id", columnList = "membershipCardId")
})
public class PosOperationLog extends BaseEntity {

    /**
     * 门店编号
     */
    @Column(name = "store_no", nullable = false)
    private Integer storeNo;

    /**
     * POS机编号
     */
    @Column(name = "pos_no", nullable = false)
    private Integer posNo;

    /**
     * 设备编号
     */
    @Column(name = "device_no", length = 100)
    private String deviceNo;

    /**
     * POS机类型
     */
    @Column(name = "pos_type")
    private Integer posType;

    /**
     * 操作类型
     * 10001: 上班
     * 10002: 下班
     * 10004: 会员操作
     * 10005: 开始交易
     * 10031: 完成交易
     * 等等...
     */
    @Column(name = "operation_type", nullable = false)
    private Integer operationType;

    /**
     * 流水ID
     */
    @Column(name = "flow_id", length = 100)
    private String flowId;

    /**
     * 班次号
     */
    @Column(name = "shift_no", length = 50)
    private String shiftNo;

    /**
     * 订单ID
     */
    @Column(name = "order_id", length = 50)
    private String orderId;

    /**
     * 会员卡ID
     */
    @Column(name = "membership_card_id", length = 50)
    private String membershipCardId;

    /**
     * 发生时间（时间戳）
     */
    @Column(name = "occurrence_time", nullable = false)
    private Long occurrenceTime;

    /**
     * 发生时间（LocalDateTime）
     */
    @Column(name = "occurrence_time_dt")
    private LocalDateTime occurrenceTimeDateTime;

    /**
     * 工作时间描述
     */
    @Column(name = "work_time", length = 200)
    private String workTime;

    /**
     * 扩展JSON字符串
     */
    @Column(name = "extended_json", columnDefinition = "TEXT")
    private String extendedJson;

    /**
     * 同步状态：0-待同步，1-已同步，2-同步失败
     */
    @Column(name = "sync_status")
    private Integer syncStatus = 0;

    /**
     * 同步时间
     */
    @Column(name = "sync_time")
    private LocalDateTime syncTime;

    /**
     * 同步失败原因
     */
    @Column(name = "sync_error", length = 500)
    private String syncError;

    /**
     * 原始创建时间（来自外部系统）
     */
    @Column(name = "original_create_time")
    private Long originalCreateTime;

    /**
     * 原始修改时间（来自外部系统）
     */
    @Column(name = "original_modify_time")
    private Long originalModifyTime;

    /**
     * 数据版本（用于增量同步）
     */
    @Column(name = "data_version")
    private String dataVersion;

    /**
     * 预处理时间戳转换为LocalDateTime
     */
    @PrePersist
    @PreUpdate
    public void prePersist() {
        if (occurrenceTime != null) {
            occurrenceTimeDateTime = LocalDateTime.ofEpochSecond(
                occurrenceTime / 1000, 
                (int) (occurrenceTime % 1000) * 1000000, 
                java.time.ZoneOffset.of("+8")
            );
        }
        
        if (syncStatus == null) {
            syncStatus = 0;
        }
    }
}
