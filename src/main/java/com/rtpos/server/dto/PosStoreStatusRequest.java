package com.rtpos.server.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * POS门店状态查询请求DTO
 * 对应外部接口 getPosByStore 的请求结构
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "POS门店状态查询请求")
public class PosStoreStatusRequest {

    @Schema(description = "门店编号", example = "1001", required = true)
    @JsonProperty("storeNo")
    @NotNull(message = "门店编号不能为空")
    private Integer storeNo;
}
