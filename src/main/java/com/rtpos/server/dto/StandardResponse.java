package com.rtpos.server.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 标准API响应格式 - 符合前端request.js拦截器要求
 * 前端期望: rsCode='00000000'表示成功, msg包含消息, body包含数据
 * 
 * <AUTHOR>
 */
@Data
public class StandardResponse<T> {

    @JsonProperty("rsCode")
    private String rsCode;
    
    @JsonProperty("msg")
    private String msg;
    
    @JsonProperty("body")
    private T body;

    public StandardResponse() {
    }

    public StandardResponse(String rsCode, String msg) {
        this.rsCode = rsCode;
        this.msg = msg;
    }

    public StandardResponse(String rsCode, String msg, T body) {
        this.rsCode = rsCode;
        this.msg = msg;
        this.body = body;
    }

    /**
     * 成功响应
     */
    public static <T> StandardResponse<T> success() {
        return new StandardResponse<>("00000000", "操作成功");
    }

    /**
     * 成功响应带数据
     */
    public static <T> StandardResponse<T> success(T body) {
        return new StandardResponse<>("00000000", "操作成功", body);
    }

    /**
     * 成功响应带自定义消息和数据
     */
    public static <T> StandardResponse<T> success(String msg, T body) {
        return new StandardResponse<>("00000000", msg, body);
    }

    /**
     * 失败响应
     */
    public static <T> StandardResponse<T> error(String msg) {
        return new StandardResponse<>("99999999", msg);
    }

    /**
     * 失败响应带错误码
     */
    public static <T> StandardResponse<T> error(String rsCode, String msg) {
        return new StandardResponse<>(rsCode, msg);
    }

    /**
     * 参数错误
     */
    public static <T> StandardResponse<T> badRequest(String msg) {
        return new StandardResponse<>("40000000", msg);
    }

    /**
     * 未授权
     */
    public static <T> StandardResponse<T> unauthorized(String msg) {
        return new StandardResponse<>("40100000", msg);
    }

    /**
     * 禁止访问
     */
    public static <T> StandardResponse<T> forbidden(String msg) {
        return new StandardResponse<>("40300000", msg);
    }

    /**
     * 资源不存在
     */
    public static <T> StandardResponse<T> notFound(String msg) {
        return new StandardResponse<>("40400000", msg);
    }

    /**
     * 服务器内部错误
     */
    public static <T> StandardResponse<T> internalError(String msg) {
        return new StandardResponse<>("50000000", msg);
    }

    /**
     * 业务异常
     */
    public static <T> StandardResponse<T> businessError(String msg) {
        return new StandardResponse<>("50100000", msg);
    }

    /**
     * 数据不存在
     */
    public static <T> StandardResponse<T> dataNotFound(String msg) {
        return new StandardResponse<>("50200000", msg);
    }

    /**
     * 数据已存在
     */
    public static <T> StandardResponse<T> dataExists(String msg) {
        return new StandardResponse<>("50300000", msg);
    }

    /**
     * 检查是否成功
     */
    public boolean isSuccess() {
        return "00000000".equals(this.rsCode);
    }
}
