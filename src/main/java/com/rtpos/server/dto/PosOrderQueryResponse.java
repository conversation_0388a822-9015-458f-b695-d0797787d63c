package com.rtpos.server.dto;

import lombok.Data;

import java.util.List;

/**
 * POS订单查询响应结果
 * 
 * <AUTHOR>
 */
@Data
public class PosOrderQueryResponse {

    /**
     * 响应码
     */
    private String rsCode;

    /**
     * 响应体
     */
    private Body body;

    /**
     * 响应消息
     */
    private String msg;

    /**
     * 追踪ID
     */
    private String traceId;

    @Data
    public static class Body {
        /**
         * 总数
         */
        private Integer total;

        /**
         * 订单列表
         */
        private List<PosOrderDTO> orderList;
    }
}
