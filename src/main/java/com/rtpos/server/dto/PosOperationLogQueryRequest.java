package com.rtpos.server.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * POS收银操作日志查询请求DTO
 * 对应外部接口 getOperationLogInfo 的请求参数
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "POS收银操作日志查询请求")
public class PosOperationLogQueryRequest {

    @Schema(description = "门店编号", example = "1001", required = true)
    private Integer storeNo;

    @Schema(description = "POS机编号", example = "895", required = true)
    private Integer posNo;

    @Schema(description = "开始时间（时间戳毫秒）", example = "1749517200000", required = true)
    private Long beginTime;

    @Schema(description = "结束时间（时间戳毫秒）", example = "1749520800000", required = true)
    private Long endTime;

    @Schema(description = "每页数量", example = "20")
    private Integer limit = 20;

    @Schema(description = "页码", example = "1")
    private Integer page = 1;

    /**
     * 构造方法
     */
    public PosOperationLogQueryRequest() {
    }

    /**
     * 构造方法
     */
    public PosOperationLogQueryRequest(Integer storeNo, Integer posNo, Long beginTime, Long endTime) {
        this.storeNo = storeNo;
        this.posNo = posNo;
        this.beginTime = beginTime;
        this.endTime = endTime;
    }

    /**
     * 构造方法
     */
    public PosOperationLogQueryRequest(Integer storeNo, Integer posNo, Long beginTime, Long endTime, Integer limit, Integer page) {
        this.storeNo = storeNo;
        this.posNo = posNo;
        this.beginTime = beginTime;
        this.endTime = endTime;
        this.limit = limit;
        this.page = page;
    }
}
