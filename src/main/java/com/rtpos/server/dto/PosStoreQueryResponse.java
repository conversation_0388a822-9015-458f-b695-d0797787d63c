package com.rtpos.server.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * POS门店查询响应DTO
 * 对应外部接口 getPosStore 的响应结构
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "POS门店查询响应")
public class PosStoreQueryResponse {

    @Schema(description = "响应码")
    @JsonProperty("rsCode")
    private String rsCode;

    @Schema(description = "响应消息")
    @JsonProperty("msg")
    private String msg;

    @Schema(description = "响应数据体")
    @JsonProperty("body")
    private PosStoreResponseBody body;

    @Schema(description = "追踪ID")
    @JsonProperty("traceId")
    private String traceId;

    /**
     * 响应数据体
     */
    @Data
    @Schema(description = "POS门店响应数据体")
    public static class PosStoreResponseBody {

        @Schema(description = "门店信息列表")
        @JsonProperty("storeInfoList")
        private List<PosStoreDTO> storeInfoList;
    }

    /**
     * 判断响应是否成功
     */
    public boolean isSuccess() {
        return "00000000".equals(rsCode);
    }

    /**
     * 获取门店列表
     */
    public List<PosStoreDTO> getStoreList() {
        return body != null ? body.getStoreInfoList() : null;
    }
}
