package com.rtpos.server.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * POS门店状态查询响应DTO
 * 对应外部接口 getPosByStore 的响应结构
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "POS门店状态查询响应")
public class PosStoreStatusResponse {

    @Schema(description = "响应码", example = "00000000")
    @JsonProperty("rsCode")
    private String rsCode;

    @Schema(description = "响应消息", example = "Success")
    @JsonProperty("msg")
    private String msg;

    @Schema(description = "响应数据体")
    @JsonProperty("body")
    private PosStoreStatusBody body;

    @Schema(description = "追踪ID")
    @JsonProperty("traceId")
    private String traceId;

    /**
     * 判断响应是否成功
     */
    public boolean isSuccess() {
        return "00000000".equals(rsCode);
    }
}
