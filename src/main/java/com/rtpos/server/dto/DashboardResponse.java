package com.rtpos.server.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * Dashboard数据响应DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "Dashboard数据响应")
public class DashboardResponse {

    @Schema(description = "总设备数")
    @JsonProperty("totalDevices")
    private Integer totalDevices;

    @Schema(description = "在线设备数")
    @JsonProperty("onlineDevices")
    private Integer onlineDevices;

    @Schema(description = "人工POS数量")
    @JsonProperty("manualPosCount")
    private Integer manualPosCount;

    @Schema(description = "自助POS数量")
    @JsonProperty("selfServicePosCount")
    private Integer selfServicePosCount;

    @Schema(description = "平均使用率")
    @JsonProperty("averageUsageRate")
    private Double averageUsageRate;

    @Schema(description = "日耗电量预估")
    @JsonProperty("dailyPowerConsumption")
    private Double dailyPowerConsumption;

    @Schema(description = "月耗电量预估")
    @JsonProperty("monthlyPowerConsumption")
    private Double monthlyPowerConsumption;

    @Schema(description = "年节电预估")
    @JsonProperty("yearlyProjectedSavings")
    private Double yearlyProjectedSavings;

    @Schema(description = "小时订单趋势")
    @JsonProperty("hourlyOrderTrend")
    private List<HourlyOrderTrendData> hourlyOrderTrend;

    @Schema(description = "使用率趋势")
    @JsonProperty("usageRateTrend")
    private List<UsageRateTrendData> usageRateTrend;

    @Schema(description = "设备状态分布")
    @JsonProperty("deviceStatusDistribution")
    private DeviceStatusDistribution deviceStatusDistribution;

    @Schema(description = "节能趋势")
    @JsonProperty("energySavingTrend")
    private List<EnergySavingTrendData> energySavingTrend;

    /**
     * 小时订单趋势数据
     */
    @Data
    @Schema(description = "小时订单趋势数据")
    public static class HourlyOrderTrendData {
        @Schema(description = "小时", example = "08:00")
        @JsonProperty("hour")
        private String hour;

        @Schema(description = "人工POS订单数")
        @JsonProperty("manual")
        private Integer manual;

        @Schema(description = "自助POS订单数")
        @JsonProperty("selfService")
        private Integer selfService;
    }

    /**
     * 使用率趋势数据
     */
    @Data
    @Schema(description = "使用率趋势数据")
    public static class UsageRateTrendData {
        @Schema(description = "日期", example = "07-01")
        @JsonProperty("date")
        private String date;

        @Schema(description = "人工POS使用率")
        @JsonProperty("manual")
        private Double manual;

        @Schema(description = "自助POS使用率")
        @JsonProperty("selfService")
        private Double selfService;
    }

    /**
     * 设备状态分布
     */
    @Data
    @Schema(description = "设备状态分布")
    public static class DeviceStatusDistribution {
        @Schema(description = "人工POS状态")
        @JsonProperty("manual")
        private DeviceStatus manual;

        @Schema(description = "自助POS状态")
        @JsonProperty("selfService")
        private DeviceStatus selfService;
    }

    /**
     * 设备状态
     */
    @Data
    @Schema(description = "设备状态")
    public static class DeviceStatus {
        @Schema(description = "活跃设备数")
        @JsonProperty("active")
        private Integer active;

        @Schema(description = "空闲设备数")
        @JsonProperty("idle")
        private Integer idle;

        @Schema(description = "离线设备数")
        @JsonProperty("offline")
        private Integer offline;
    }

    /**
     * 节能趋势数据
     */
    @Data
    @Schema(description = "节能趋势数据")
    public static class EnergySavingTrendData {
        @Schema(description = "日期", example = "07-01")
        @JsonProperty("date")
        private String date;

        @Schema(description = "节能量(kWh)")
        @JsonProperty("saving")
        private Double saving;
    }
}
