package com.rtpos.server.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * POS订单数据传输对象
 * 对应外部接口返回的订单数据结构
 * 
 * <AUTHOR>
 */
@Data
public class PosOrderDTO {

    private Long id;

    @JsonProperty("storeId")
    private String storeId;

    @JsonProperty("source")
    private Integer source;

    @JsonProperty("bizOrderId")
    private Long bizOrderId;

    @JsonProperty("mainOrderId")
    private Long mainOrderId;

    @JsonProperty("outOrderId")
    private String outOrderId;

    @JsonProperty("payTime")
    private Long payTime;

    private LocalDateTime payTimeDateTime;

    @JsonProperty("orderTime")
    private Long orderTime;

    private LocalDateTime orderTimeDateTime;

    @JsonProperty("orderStatus")
    private Integer orderStatus;

    @JsonProperty("operatorId")
    private String operatorId;

    @JsonProperty("operatorName")
    private String operatorName;

    @JsonProperty("originalAmt")
    private Integer originalAmt;

    private BigDecimal originalAmount;

    @JsonProperty("discountAmt")
    private Integer discountAmt;

    private BigDecimal discountAmount;

    @JsonProperty("memberDiscountAmt")
    private Integer memberDiscountAmt;

    private BigDecimal memberDiscountAmount;

    @JsonProperty("packingFee")
    private Integer packingFee;

    private BigDecimal packingFeeAmount;

    @JsonProperty("packingFeeTaxRate")
    private BigDecimal packingFeeTaxRate;

    @JsonProperty("memberCardNum")
    private String memberCardNum;

    @JsonProperty("posNo")
    private Integer posNo;

    @JsonProperty("deviceId")
    private String deviceId;

    @JsonProperty("mqTimestamp")
    private Long mqTimestamp;

    @JsonProperty("dutyCode")
    private String dutyCode;

    @JsonProperty("extendedJson")
    private String extendedJson;

    @JsonProperty("deleted")
    private Integer deleted;

    @JsonProperty("deletedTime")
    private Long deletedTime;

    @JsonProperty("jobStatus")
    private Integer jobStatus;

    @JsonProperty("jobTimes")
    private Integer jobTimes;

    @JsonProperty("gmtCreate")
    private Long gmtCreate;

    @JsonProperty("gmtModified")
    private Long gmtModified;

    @JsonProperty("mainExtendedJsonModel")
    private MainExtendedJsonModel mainExtendedJsonModel;

    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    /**
     * 主扩展JSON模型
     */
    @Data
    public static class MainExtendedJsonModel {
        private String vineCode;
        private String dineType;
        private String buyerPhoneNo;
        private String wdkMemberValue;
        private String wdkMemberSource;
        private String memDegrade;
        private String outOrderId;
        private String supNo;
        private String supplyPriceCalTime;
        @JsonProperty("dead_line_time")
        private String deadLineTime;
        @JsonProperty("leader_id")
        private String leaderId;
        @JsonProperty("cn_leader_id")
        private String cnLeaderId;
        private String subDbIndex;
        private String mmcLeaderId;
        private String cancelTime;
        private String saleChannel;
        private String txdSpFrom;
        private String yxDeliveryVoucher;
        private String skuBrandSubsidyFee;
        private String postDiscountFee;
        private String postDiscountMerchantFee;
        private String postDiscountPlatformFee;
        private String memberType;
        private String dropShipping;
        private String selfDeliveryType;
        private String clubOrder;
        private String pgSeq;
        private String isHide;
        private String commentTime;
        private Boolean selfBuiltPos;
        private String posId;
        private String voucherType;
        private String tiktokV2;
        private String dutyCode;
        private String gbizExt;
    }
}
