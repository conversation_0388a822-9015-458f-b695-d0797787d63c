package com.rtpos.server.dto;

import com.rtpos.server.entity.Order;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单数据传输对象
 * 
 * <AUTHOR>
 */
@Data
public class OrderDTO {

    private Long id;
    private String orderNo;
    private Long storeId;
    private String storeName;
    private Long customerId;
    private String customerName;
    private String customerPhone;
    private BigDecimal totalAmount;
    private BigDecimal discountAmount;
    private BigDecimal actualAmount;
    private String paymentMethod;
    private Order.OrderStatus status;
    private LocalDateTime orderTime;
    private LocalDateTime paymentTime;
    private LocalDateTime completeTime;
    private String remark;
    private List<OrderItemDTO> orderItems;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
