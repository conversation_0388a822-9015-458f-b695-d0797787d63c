package com.rtpos.server.dto.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "认证API响应")
public class POSAuthResponse<T>  {


    @Schema(description = "响应码")
    @JsonProperty("rsCode")
    private String rsCode;

    @Schema(description = "响应消息")
    @JsonProperty("msg")
    private String msg;

    @Schema(description = "响应数据")
    private T body;
}
