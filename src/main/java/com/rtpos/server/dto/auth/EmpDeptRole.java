package com.rtpos.server.dto.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 员工部门角色信息
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "员工部门角色信息")
public class EmpDeptRole {

    @JsonProperty("dept_cate")
    @Schema(description = "部门类别", example = "2")
    private Integer deptCate;

    @JsonProperty("dept_id")
    @Schema(description = "部门ID", example = "230400027")
    private String deptId;

    @JsonProperty("dept_level")
    @Schema(description = "部门级别", example = "99")
    private Integer deptLevel;

    @JsonProperty("dept_name")
    @Schema(description = "部门名称", example = "商品组")
    private String deptName;

    @JsonProperty("dept_path")
    @Schema(description = "部门路径", example = "500-506-50190-71055-50199-230400027-")
    private String deptPath;

    @JsonProperty("dept_pool_path")
    @Schema(description = "部门池路径", example = "RT-A-R06-R0603-R0603-R0603-")
    private String deptPoolPath;

    @JsonProperty("dept_type")
    @Schema(description = "部门类型", example = "0")
    private Integer deptType;

    @JsonProperty("is_boss")
    @Schema(description = "是否主管", example = "1")
    private Integer isBoss;

    @JsonProperty("is_main")
    @Schema(description = "是否主要", example = "1")
    private Integer isMain;

    @JsonProperty("label_id")
    @Schema(description = "标签ID", example = "0")
    private Integer labelId;

    @JsonProperty("nt_code")
    @Schema(description = "NT代码", example = "")
    private String ntCode;

    @JsonProperty("pool_code")
    @Schema(description = "池代码", example = "R0603")
    private String poolCode;

    @JsonProperty("role_id")
    @Schema(description = "角色ID", example = "1")
    private String roleId;

    @JsonProperty("role_name")
    @Schema(description = "角色名称", example = "主管")
    private String roleName;

    @Schema(description = "根", example = "")
    private String root;
}
