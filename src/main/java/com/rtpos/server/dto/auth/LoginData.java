package com.rtpos.server.dto.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 登录响应数据
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "登录响应数据")
public class LoginData {

    @JsonProperty("work_id")
    @Schema(description = "工号", example = "92302459")
    private String workId;

    @JsonProperty("is_logged_in")
    @Schema(description = "是否已登录", example = "1")
    private String isLoggedIn;

    @JsonProperty("ding_id")
    @Schema(description = "钉钉ID", example = "$:LWCP_v1:$gEH/3tlQuPCVeGC0ljBSeQ==")
    private String dingId;

    @JsonProperty("emp_name")
    @Schema(description = "员工姓名", example = "王波")
    private String empName;

    @Schema(description = "手机号", example = "15721096991")
    private String mobile;

    @JsonProperty("oldToken")
    @Schema(description = "旧Token", example = "")
    private String oldToken;

    @JsonProperty("old_token")
    @Schema(description = "旧Token", example = "e0kySmcudcJG6zEY3Fr3NHYfVpoiIbDncF92kiPOCn4=")
    private String oldTokenUnderscore;

    @JsonProperty("emp_id")
    @Schema(description = "员工ID", example = "20190232044")
    private String empId;

    @Schema(description = "Token", example = "e0kySmcudcJG6zEY3Fr3NAse4bjXJZlPkgO3+eNNok4=")
    private String token;
}
