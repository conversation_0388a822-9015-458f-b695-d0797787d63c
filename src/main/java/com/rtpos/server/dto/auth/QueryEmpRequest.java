package com.rtpos.server.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 查询员工信息请求
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "查询员工信息请求")
public class QueryEmpRequest {

    @NotBlank(message = "员工ID不能为空")
    @Schema(description = "员工唯一识别码", example = "20190232044", required = true)
    private String empId;
}
