package com.rtpos.server.dto.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 认证API通用响应
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "认证API响应")
public class AuthResponse<T> {

    @Schema(description = "响应码", example = "200")
    private Integer code;

    @Schema(description = "响应消息", example = "操作成功")
    private String msg;

    @Schema(description = "响应数据")
    private T data;

    @Schema(description = "发送验证码方式1钉钉，2短信", example = "1")
    private Integer type;
}
