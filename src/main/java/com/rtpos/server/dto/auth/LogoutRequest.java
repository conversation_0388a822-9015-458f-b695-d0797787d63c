package com.rtpos.server.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 登出请求
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "登出请求")
public class LogoutRequest {

    @NotBlank(message = "token不能为空")
    @Schema(description = "用户token", example = "e0kySmcudcJG6zEY3Fr3NMufWXxAGsehMpAkP/zVEss=", required = true)
    private String token;
}
