package com.rtpos.server.dto.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Token验证响应
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "Token验证响应")
public class TokenValidationResponse {

    @Schema(description = "响应码", example = "00000000")
    @JsonProperty("rsCode")
    private String rsCode;

    @Schema(description = "响应消息", example = "Success")
    private String msg;

    @Schema(description = "响应数据，如果token有效则包含empid", example = "20190232044")
    private String body;
}
