package com.rtpos.server.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

/**
 * 验证码登录请求
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "验证码登录请求")
public class LoginRequest {

    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Schema(description = "手机号", example = "15721096991", required = true)
    private String mobile;

    @Schema(description = "应用标识", example = "RTPOS", defaultValue = "RTPOS")
    private String app = "RTPOS";

    @NotBlank(message = "验证码不能为空")
    @Pattern(regexp = "^\\d{6}$", message = "验证码必须是6位数字")
    @Schema(description = "6位验证码", example = "618308", required = true)
    private String code;

    @NotNull(message = "类型不能为空")
    @Schema(description = "登录类型：1-1天有效期，2-30天有效期", example = "1", required = true, allowableValues = {"1", "2"})
    private Integer type;
}
