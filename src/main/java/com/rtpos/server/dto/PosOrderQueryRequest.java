package com.rtpos.server.dto;

import lombok.Data;

/**
 * POS订单查询请求参数
 * 
 * <AUTHOR>
 */
@Data
public class PosOrderQueryRequest {

    /**
     * 门店ID
     */
    private String storeId;

    /**
     * 开始时间（时间戳）
     */
    private Long generalTimeStart;

    /**
     * 结束时间（时间戳）
     */
    private Long generalTimeEnd;

    /**
     * 当前页码
     */
    private Integer currentPage = 1;

    /**
     * 页面大小
     */
    private Integer pageSize = 20;
}
