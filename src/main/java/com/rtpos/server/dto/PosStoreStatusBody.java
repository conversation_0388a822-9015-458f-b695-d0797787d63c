package com.rtpos.server.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * POS门店状态响应数据体DTO
 * 对应外部接口 getPosByStore 返回的数据结构
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "POS门店状态响应数据体")
public class PosStoreStatusBody {

    @Schema(description = "当前门店总设备数量", example = "105")
    @JsonProperty("storeAllPosCount")
    private Integer storeAllPosCount;

    @Schema(description = "所有门店在线机台数", example = "12000")
    @JsonProperty("allOnlPosCount")
    private Integer allOnlPosCount;

    @Schema(description = "所有门店离线机台数", example = "8999")
    @JsonProperty("allOffPosCount")
    private Integer allOffPosCount;

    /**
     * onlManualPosNum
     * onlManualPosNum
     * Integer
     * 所有门店在线人工机台数量
     *
     * ManualPosCount
     * ManualPosCount
     * Integer
     * 所有门店人工机台数量
     *
     * onlSelfPosNum
     * onlSelfPosNum
     * Integer
     * 所有门店在线自助机台数量
     *
     * SelfPosCount
     * SelfPosCount
     * Integer
     * 所有门店自助机台数量
     *
     * onlMobilePosNum
     * onlMobilePosNum
     * Integer
     * 所有门店在线移动机台数量
     *
     * MobilePosCount
     * MobilePosCount
     * Integer
     * 所有门店移动机台数量
     */

    @Schema(description = "所有门店在线人工机台数量", example = "100")
    @JsonProperty("onlManualPosNum")
    private Integer onlManualPosNum;

    @Schema(description = "所有门店人工机台数量", example = "100")
    @JsonProperty("manualPosCount")
    private Integer manualPosCount;

    @Schema(description = "所有门店在线自助机台数量", example = "100")
    @JsonProperty("onlSelfPosNum")
    private Integer onlSelfPosNum;

    @Schema(description = "所有门店自助机台数量", example = "100")
    @JsonProperty("selfPosCount")
    private Integer selfPosCount;

    @Schema(description = "所有门店在线手机机台数量", example = "100")
    @JsonProperty("onlMobilePosNum")
    private Integer onlMobilePosNum;

    @Schema(description = "所有门店移动机台数量", example = "100")
    @JsonProperty("mobilePosCount")
    private Integer mobilePosCount;

    @Schema(description = "在线机台集合，如果没有返回空集合")
    @JsonProperty("online")
    private List<Integer> online;

    @Schema(description = "离线机台集合，如果没有返回空集合")
    @JsonProperty("offline")
    private List<Integer> offline;

    @Schema(description = "上机状态机台集合，如果没有返回空集合")
    @JsonProperty("login")
    private List<Integer> login;

    @Schema(description = "下机状态机台集合，如果没有返回空集合")
    @JsonProperty("logout")
    private List<Integer> logout;
}
