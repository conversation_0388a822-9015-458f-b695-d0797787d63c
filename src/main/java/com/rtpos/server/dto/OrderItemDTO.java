package com.rtpos.server.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单项数据传输对象
 * 
 * <AUTHOR>
 */
@Data
public class OrderItemDTO {

    private Long id;
    private Long orderId;
    private Long productId;
    private String productName;
    private String productCode;
    private Long categoryId;
    private String categoryName;
    private BigDecimal unitPrice;
    private Integer quantity;
    private BigDecimal discountAmount;
    private BigDecimal subtotal;
    private String specifications;
    private String remark;
}
