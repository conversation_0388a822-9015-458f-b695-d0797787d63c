package com.rtpos.server.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * POS收银操作日志DTO
 * 对应外部接口 getOperationLogInfo 返回的单条操作日志数据
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "POS收银操作日志")
public class PosOperationLogDTO {

    @Schema(description = "门店编号")
    @JsonProperty("storeNo")
    private Integer storeNo;

    @Schema(description = "POS机编号")
    @JsonProperty("posNo")
    private Integer posNo;

    @Schema(description = "设备编号")
    @JsonProperty("deviceNo")
    private String deviceNo;

    @Schema(description = "POS机类型")
    @JsonProperty("posType")
    private Integer posType;

    @Schema(description = "操作类型")
    @JsonProperty("operationType")
    private Integer operationType;

    @Schema(description = "流水ID")
    @JsonProperty("flowId")
    private String flowId;

    @Schema(description = "班次号")
    @JsonProperty("shiftNo")
    private String shiftNo;

    @Schema(description = "订单ID")
    @JsonProperty("orderId")
    private String orderId;

    @Schema(description = "会员卡ID")
    @JsonProperty("membershipCardId")
    private String membershipCardId;

    @Schema(description = "发生时间（时间戳毫秒）")
    @JsonProperty("occurrenceTime")
    private Long occurrenceTime;

    @Schema(description = "工作时间描述")
    @JsonProperty("workTime")
    private String workTime;
}
