package com.rtpos.server.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * POS门店信息DTO
 * 对应外部接口 getPosStore 返回的单条门店数据
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "POS门店信息")
public class PosStoreDTO {

    @Schema(description = "门店ID")
    @JsonProperty("storeId")
    private String storeId;

    @Schema(description = "门店名称")
    @JsonProperty("storeName")
    private String storeName;

    @Schema(description = "大区序号")
    @JsonProperty("pgSeq")
    private String pgSeq;

    @Schema(description = "大区名称")
    @JsonProperty("pgName")
    private String pgName;

    @Schema(description = "子区域ID")
    @JsonProperty("subId")
    private String subId;

    @Schema(description = "子区域名称")
    @JsonProperty("subArea")
    private String subArea;
}
