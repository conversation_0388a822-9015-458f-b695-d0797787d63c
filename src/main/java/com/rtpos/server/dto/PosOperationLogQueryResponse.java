package com.rtpos.server.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * POS收银操作日志查询响应DTO
 * 对应外部接口 getOperationLogInfo 的响应结构
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "POS收银操作日志查询响应")
public class PosOperationLogQueryResponse {

    @Schema(description = "响应码")
    @JsonProperty("rsCode")
    private String rsCode;

    @Schema(description = "响应消息")
    @JsonProperty("msg")
    private String msg;

    @Schema(description = "响应数据")
    @JsonProperty("body")
    private PosOperationLogResponseBody body;

    @Schema(description = "追踪ID")
    @JsonProperty("traceId")
    private String traceId;

    /**
     * 响应数据体
     */
    @Data
    @Schema(description = "POS收银操作日志响应数据体")
    public static class PosOperationLogResponseBody {

        @Schema(description = "总数量")
        @JsonProperty("total")
        private Integer total;

        @Schema(description = "操作日志列表")
        @JsonProperty("operationList")
        private List<PosOperationLogDTO> operationList;
    }

    /**
     * 判断响应是否成功
     */
    public boolean isSuccess() {
        return "00000000".equals(rsCode);
    }

    /**
     * 获取操作日志列表
     */
    public List<PosOperationLogDTO> getOperationList() {
        return body != null ? body.getOperationList() : null;
    }

    /**
     * 获取总数量
     */
    public Integer getTotal() {
        return body != null ? body.getTotal() : 0;
    }
}
