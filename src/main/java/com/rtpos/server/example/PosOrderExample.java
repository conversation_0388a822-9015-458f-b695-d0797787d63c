package com.rtpos.server.example;

import com.rtpos.server.dto.PosOrderDTO;
import com.rtpos.server.dto.PosOrderQueryRequest;
import com.rtpos.server.dto.PosOrderQueryResponse;
import com.rtpos.server.service.PosOrderService;
import com.rtpos.server.service.PosOrderSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * POS订单系统使用示例
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "pos.example.enabled", havingValue = "true", matchIfMissing = false)
public class PosOrderExample implements CommandLineRunner {

    private final PosOrderService posOrderService;
    private final PosOrderSyncService posOrderSyncService;

    @Override
    public void run(String... args) throws Exception {
        log.info("=== POS订单系统使用示例 ===");
        
        // 示例1：创建测试订单数据
        createTestOrderData();
        
        // 示例2：查询订单数据
        queryOrderData();
        
        // 示例3：统计订单信息
        getOrderStatistics();
        
        // 示例4：演示同步功能（注释掉，避免实际调用外部接口）
        // demonstrateSync();
        
        log.info("=== POS订单系统示例完成 ===");
    }

    /**
     * 创建测试订单数据
     */
    private void createTestOrderData() {
        log.info("--- 创建测试订单数据 ---");
        
        PosOrderDTO testOrder = new PosOrderDTO();
        testOrder.setStoreId("1001");
        testOrder.setBizOrderId(910010211748915822L);
        testOrder.setMainOrderId(984758087376297984L);
        testOrder.setOutOrderId("910010211748915822");
        testOrder.setOrderTime(System.currentTimeMillis() - 3600000); // 1小时前
        testOrder.setPayTime(System.currentTimeMillis() - 3500000);
        testOrder.setOrderStatus(1); // 已完成
        testOrder.setOperatorId("20190231768");
        testOrder.setOperatorName("陆潇华");
        testOrder.setOriginalAmt(890); // 8.9元
        testOrder.setDiscountAmt(880); // 8.8元
        testOrder.setMemberDiscountAmt(0);
        testOrder.setPackingFee(0);
        testOrder.setPackingFeeTaxRate(BigDecimal.ZERO);
        testOrder.setMemberCardNum("");
        testOrder.setPosNo(21);
        testOrder.setDeviceId("dadacc24-acfc-3676-86de-d27862839376");
        testOrder.setMqTimestamp(System.currentTimeMillis() - 3600000);
        testOrder.setDutyCode("25060309541620190231768");
        testOrder.setExtendedJson("{\"posId\":\"VKSONBCL8bUDAI/vzALsxZ2W\",\"selfBuiltPos\":true,\"memDegrade\":\"1\"}");
        testOrder.setDeleted(0);
        testOrder.setDeletedTime(0L);
        testOrder.setJobStatus(2);
        testOrder.setJobTimes(1);
        testOrder.setGmtCreate(System.currentTimeMillis() - 3600000);
        testOrder.setGmtModified(System.currentTimeMillis() - 3500000);

        // 检查订单是否已存在
        if (!posOrderService.existsByBizOrderId(testOrder.getBizOrderId())) {
            PosOrderDTO savedOrder = posOrderService.savePosOrder(testOrder);
            log.info("创建测试订单成功，ID: {}, 业务订单ID: {}", savedOrder.getId(), savedOrder.getBizOrderId());
        } else {
            log.info("测试订单已存在，业务订单ID: {}", testOrder.getBizOrderId());
        }
    }

    /**
     * 查询订单数据
     */
    private void queryOrderData() {
        log.info("--- 查询订单数据 ---");
        
        // 1. 根据业务订单ID查询
        PosOrderDTO order = posOrderService.findByBizOrderId(910010211748915822L);
        if (order != null) {
            log.info("根据业务订单ID查询成功: 门店ID={}, 操作员={}, 金额={}分", 
                    order.getStoreId(), order.getOperatorName(), order.getOriginalAmt());
        }
        
        // 2. 分页查询门店订单
        Page<PosOrderDTO> orders = posOrderService.findByStoreId("1001", 0, 10);
        log.info("门店1001的订单数量: {}, 当前页订单数: {}", orders.getTotalElements(), orders.getContent().size());
        
        // 3. 根据时间范围查询
        long endTime = System.currentTimeMillis();
        long startTime = endTime - 24 * 60 * 60 * 1000L; // 24小时前
        Page<PosOrderDTO> timeRangeOrders = posOrderService.findByStoreIdAndTimeRange("1001", startTime, endTime, 0, 10);
        log.info("24小时内门店1001的订单数量: {}", timeRangeOrders.getTotalElements());
    }

    /**
     * 获取订单统计信息
     */
    private void getOrderStatistics() {
        log.info("--- 获取订单统计信息 ---");
        
        // 统计门店订单总数
        Long totalOrders = posOrderService.countByStoreId("1001");
        log.info("门店1001总订单数: {}", totalOrders);
        
        // 统计指定时间范围内的订单数和销售额
        long endTime = System.currentTimeMillis();
        long startTime = endTime - 24 * 60 * 60 * 1000L; // 24小时前
        
        Long ordersInRange = posOrderService.countByStoreIdAndTimeRange("1001", startTime, endTime);
        Long salesInRange = posOrderService.sumSalesByStoreIdAndTimeRange("1001", startTime, endTime);
        
        log.info("24小时内门店1001: 订单数={}, 销售额={}分", ordersInRange, salesInRange);
    }

    /**
     * 演示同步功能（注释掉，避免实际调用外部接口）
     */
    private void demonstrateSync() {
        log.info("--- 演示同步功能 ---");
        
        try {
            // 创建同步请求
            PosOrderQueryRequest request = new PosOrderQueryRequest();
            request.setStoreId("1001");
            request.setGeneralTimeStart(System.currentTimeMillis() - 24 * 60 * 60 * 1000L); // 24小时前
            request.setGeneralTimeEnd(System.currentTimeMillis());
            request.setCurrentPage(1);
            request.setPageSize(10);
            
            // 从外部API同步数据
            PosOrderQueryResponse response = posOrderSyncService.syncPosOrdersFromApi(request);
            
            if (response != null && "00000000".equals(response.getRsCode())) {
                log.info("同步成功，获取到 {} 条订单数据", 
                        response.getBody() != null ? response.getBody().getTotal() : 0);
            } else {
                log.warn("同步失败或无数据");
            }
            
        } catch (Exception e) {
            log.error("同步演示失败（这是正常的，因为可能无法连接到外部接口）", e);
        }
    }
}
