package com.rtpos.server.schedule;

import com.rtpos.server.service.PosOrderSyncService;
import com.rtpos.server.service.PosStoreService;
import com.rtpos.server.service.SyncStatusService;
import com.rtpos.server.entity.PosStore;
import com.rtpos.server.entity.SyncStatus;
import com.rtpos.server.util.SyncTypeConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * POS订单数据同步定时任务
 *
 * 优化策略：
 * 1. 分批次同步门店数据，避免一次性处理过多门店
 * 2. 时间分片同步，将大时间段拆分为小时间段
 * 3. 并发控制，避免过多并发请求
 * 4. 渐进式历史数据同步
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "pos.sync.enabled", havingValue = "true", matchIfMissing = true)
public class PosOrderSyncScheduler {

    private final PosOrderSyncService posOrderSyncService;
    private final PosStoreService posStoreService;
    private final SyncStatusService syncStatusService;

    // 线程池用于并发同步
    private final ExecutorService syncExecutor = Executors.newFixedThreadPool(3);

    @Value("${pos.sync.batch-stores:10}")
    private int batchStoreSize;

    @Value("${pos.sync.time-slice-hours:4}")
    private int timeSliceHours;

    @Value("${pos.sync.max-concurrent-stores:3}")
    private int maxConcurrentStores;

    @Value("${pos.sync.use-sync-status:true}")
    private boolean useSyncStatus;

    /**
     * 增量同步任务 - 每5分钟执行一次
     * 优化策略：使用同步状态记录，避免重复数据处理
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    @ConditionalOnProperty(name = "pos.sync.incremental.enabled", havingValue = "true", matchIfMissing = true)
    public void incrementalSync() {
        log.info("Starting scheduled incremental sync with optimized strategy");
        try {
            if (useSyncStatus) {
                incrementalSyncWithSyncStatus();
            } else {
                posOrderSyncService.incrementalSyncPosOrders();
            }
            log.info("Completed scheduled incremental sync");
        } catch (Exception e) {
            log.error("Failed to execute scheduled incremental sync", e);
        }
    }

    /**
     * 检查缺失订单任务 - 每30分钟执行一次
     */
    @Scheduled(fixedRate = 1800000) // 30分钟
    @ConditionalOnProperty(name = "pos.sync.check-missing.enabled", havingValue = "true", matchIfMissing = false)
    public void checkMissingOrders() {
        log.info("Starting scheduled check missing orders");
        try {
            posOrderSyncService.checkAndSyncMissingOrders();
            log.info("Completed scheduled check missing orders");
        } catch (Exception e) {
            log.error("Failed to execute scheduled check missing orders", e);
        }
    }

    /**
     * 全量同步任务 - 每天凌晨2点执行
     * 优化策略：分批次、分时间段同步所有门店数据
     */
    @Scheduled(cron = "0 0 2 * * ?")
    @ConditionalOnProperty(name = "pos.sync.full.enabled", havingValue = "true", matchIfMissing = false)
    public void fullSync() {
        log.info("Starting scheduled full sync with optimized strategy");
        try {
            // 同步前一天的数据
            long endTime = System.currentTimeMillis();
            long startTime = endTime - 24 * 60 * 60 * 1000L; // 24小时前

            // 分批次同步所有门店
            batchSyncAllStores(startTime, endTime);

            log.info("Completed scheduled full sync");
        } catch (Exception e) {
            log.error("Failed to execute scheduled full sync", e);
        }
    }

    /**
     * 渐进式历史数据同步任务 - 每天凌晨3点执行
     * 用于同步更早期的历史数据，避免首次启动时数据量过大
     */
    @Scheduled(cron = "0 0 3 * * ?")
    @ConditionalOnProperty(name = "pos.sync.progressive.enabled", havingValue = "true", matchIfMissing = false)
    public void progressiveHistorySync() {
        log.info("Starting progressive history sync");
        try {
            // 每次向前同步3天的历史数据
            long endTime = System.currentTimeMillis() - 24 * 60 * 60 * 1000L; // 从昨天开始
            long startTime = endTime - 3 * 24 * 60 * 60 * 1000L; // 向前3天

            // 分批次同步历史数据
            batchSyncAllStores(startTime, endTime);

            log.info("Completed progressive history sync");
        } catch (Exception e) {
            log.error("Failed to execute progressive history sync", e);
        }
    }

    /**
     * 分批次同步所有门店数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    private void batchSyncAllStores(long startTime, long endTime) {
        log.info("Starting batch sync for all stores, timeRange: {} - {}", startTime, endTime);

        try {
            // 获取所有门店数据
            int pageNumber = 0;
            Page<PosStore> storePage;
            int totalStores = 0;
            int processedStores = 0;

            do {
                // 分页获取门店数据
                storePage = posStoreService.getAllStores(PageRequest.of(pageNumber, batchStoreSize));
                List<PosStore> stores = storePage.getContent();

                if (stores.isEmpty()) {
                    break;
                }

                totalStores += stores.size();
                log.info("Processing batch {}, stores: {}", pageNumber + 1, stores.size());

                // 分时间段同步当前批次的门店
                processedStores += syncStoresBatch(stores, startTime, endTime);

                pageNumber++;

                // 批次间添加延迟，避免对外部API造成过大压力
                Thread.sleep(2000);

            } while (storePage.hasNext());

            log.info("Batch sync completed. Total stores: {}, Processed: {}", totalStores, processedStores);

        } catch (Exception e) {
            log.error("Failed to execute batch sync for all stores", e);
        }
    }

    /**
     * 同步一批门店的数据，使用时间分片策略
     *
     * @param stores 门店列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 成功处理的门店数量
     */
    private int syncStoresBatch(List<PosStore> stores, long startTime, long endTime) {
        log.info("Syncing {} stores with time slicing", stores.size());

        int successCount = 0;

        // 计算时间分片
        long timeSliceMillis = timeSliceHours * 60 * 60 * 1000L;

        for (long currentStart = startTime; currentStart < endTime; currentStart += timeSliceMillis) {
            long currentEnd = Math.min(currentStart + timeSliceMillis, endTime);

            log.info("Processing time slice: {} - {}", currentStart, currentEnd);

            // 并发同步当前时间片的门店数据
            successCount += syncStoresConcurrently(stores, currentStart, currentEnd);

            // 时间片间添加延迟
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("Time slice sync interrupted");
                break;
            }
        }

        return successCount;
    }

    /**
     * 并发同步门店数据
     *
     * @param stores 门店列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 成功处理的门店数量
     */
    private int syncStoresConcurrently(List<PosStore> stores, long startTime, long endTime) {
        int successCount = 0;

        // 将门店分组，每组最多maxConcurrentStores个门店并发处理
        for (int i = 0; i < stores.size(); i += maxConcurrentStores) {
            int endIndex = Math.min(i + maxConcurrentStores, stores.size());
            List<PosStore> storeGroup = stores.subList(i, endIndex);

            // 创建并发任务
            CompletableFuture<?>[] futures = storeGroup.stream()
                .map(store -> CompletableFuture.runAsync(() -> {
                    try {
                        log.debug("Syncing store: {} for time range: {} - {}",
                                store.getStoreId(), startTime, endTime);
                        posOrderSyncService.fullSyncPosOrders(store.getStoreId(), startTime, endTime);
                        log.debug("Successfully synced store: {}", store.getStoreId());
                    } catch (Exception e) {
                        log.error("Failed to sync store: {}", store.getStoreId(), e);
                    }
                }, syncExecutor))
                .toArray(CompletableFuture[]::new);

            // 等待当前组的所有任务完成
            try {
                CompletableFuture.allOf(futures).get(30, TimeUnit.MINUTES);
                successCount += storeGroup.size();
            } catch (Exception e) {
                log.error("Failed to complete concurrent sync for store group", e);
            }

            // 组间添加延迟
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("Concurrent sync interrupted");
                break;
            }
        }

        return successCount;
    }

    /**
     * 使用同步状态记录的真正增量同步
     */
    private void incrementalSyncWithSyncStatus() {
        log.info("Starting incremental sync with sync status tracking");

        try {
            long currentTime = System.currentTimeMillis();
            String syncType = SyncTypeConstants.Order.INCREMENTAL;

            // 分批次获取门店并同步
            int pageNumber = 0;
            Page<PosStore> storePage;
            int totalProcessed = 0;

            do {
                // 分页获取门店数据
                storePage = posStoreService.getAllStores(PageRequest.of(pageNumber, batchStoreSize));
                List<PosStore> stores = storePage.getContent();

                if (stores.isEmpty()) {
                    break;
                }

                log.info("Processing incremental sync batch {}, stores: {}", pageNumber + 1, stores.size());

                // 同步当前批次的门店
                for (PosStore store : stores) {
                    try {
                        totalProcessed += syncStoreWithSyncStatus(store, syncType, currentTime);
                    } catch (Exception e) {
                        log.error("Failed to sync store: {}", store.getStoreId(), e);
                    }
                }

                pageNumber++;

                // 批次间添加延迟
                Thread.sleep(1000);

            } while (storePage.hasNext());

            log.info("Incremental sync with sync status completed. Total processed: {}", totalProcessed);

        } catch (Exception e) {
            log.error("Failed to execute incremental sync with sync status", e);
        }
    }

    /**
     * 使用同步状态记录同步单个门店
     */
    private int syncStoreWithSyncStatus(PosStore store, String syncType, long currentTime) {
        try {
            // 获取该门店上次成功同步的时间
            Long lastSyncTime = syncStatusService.getLastSyncTime(syncType, store.getStoreId());

            // 如果没有同步记录，从24小时前开始同步
            if (lastSyncTime == null) {
                lastSyncTime = currentTime - 24 * 60 * 60 * 1000L; // 24小时前
                log.info("Store {} has no sync history, starting from 24 hours ago", store.getStoreId());
            } else {
                log.debug("Store {} last sync time: {}", store.getStoreId(), lastSyncTime);
            }

            // 计算增量同步的时间范围
            long startTime = lastSyncTime;
            long endTime = currentTime;

            // 如果时间范围太小（小于2分钟），跳过此次同步
            if (endTime - startTime < 2 * 60 * 1000L) {
                log.debug("Store {} sync interval too small, skipping", store.getStoreId());
                return 0;
            }

            // 记录同步开始
            syncStatusService.recordSyncStart(syncType, store.getStoreId(), startTime, endTime);

            // 执行同步
            posOrderSyncService.fullSyncPosOrders(store.getStoreId(), startTime, endTime);

            // 记录同步成功（这里简化处理，实际应该统计同步的订单数量）
            syncStatusService.recordSyncSuccess(syncType, store.getStoreId(), 0);

            log.info("Store {} incremental sync completed for time range: {} - {}",
                    store.getStoreId(), startTime, endTime);

            return 1;

        } catch (Exception e) {
            log.error("Failed to sync store: {}", store.getStoreId(), e);

            // 记录同步失败
            syncStatusService.recordSyncFailure(syncType, store.getStoreId(), e.getMessage());

            return 0;
        }
    }

    /**
     * 应用关闭时清理线程池
     */
    public void destroy() {
        if (syncExecutor != null && !syncExecutor.isShutdown()) {
            syncExecutor.shutdown();
            try {
                if (!syncExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                    syncExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                syncExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}
