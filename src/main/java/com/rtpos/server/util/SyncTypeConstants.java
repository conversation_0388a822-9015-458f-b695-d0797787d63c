package com.rtpos.server.util;

import com.rtpos.server.entity.SyncStatus;

/**
 * 同步类型常量工具类
 * 
 * 用于统一管理不同业务模块的同步类型，避免混淆
 *
 * <AUTHOR>
 */
public final class SyncTypeConstants {

    private SyncTypeConstants() {
        // 工具类，禁止实例化
    }

    /**
     * 订单同步类型
     */
    public static final class Order {
        public static final String INCREMENTAL = SyncStatus.Type.ORDER_INCREMENTAL.name();
        public static final String FULL = SyncStatus.Type.ORDER_FULL.name();
        public static final String PROGRESSIVE = SyncStatus.Type.ORDER_PROGRESSIVE.name();
        public static final String CHECK_MISSING = SyncStatus.Type.ORDER_CHECK_MISSING.name();
    }

    /**
     * 收银员日志同步类型
     */
    public static final class OperationLog {
        public static final String INCREMENTAL = SyncStatus.Type.OPERATION_LOG_INCREMENTAL.name();
        public static final String FULL = SyncStatus.Type.OPERATION_LOG_FULL.name();
        public static final String CHECK_MISSING = SyncStatus.Type.OPERATION_LOG_CHECK_MISSING.name();
    }

    /**
     * 通用同步类型（向后兼容）
     */
    public static final class Generic {
        public static final String INCREMENTAL = SyncStatus.Type.INCREMENTAL.name();
        public static final String FULL = SyncStatus.Type.FULL.name();
        public static final String PROGRESSIVE = SyncStatus.Type.PROGRESSIVE.name();
        public static final String CHECK_MISSING = SyncStatus.Type.CHECK_MISSING.name();
    }

    /**
     * 获取同步类型的显示名称
     */
    public static String getDisplayName(String syncType) {
        switch (syncType) {
            // 订单同步
            case "ORDER_INCREMENTAL":
                return "订单增量同步";
            case "ORDER_FULL":
                return "订单全量同步";
            case "ORDER_PROGRESSIVE":
                return "订单渐进式同步";
            case "ORDER_CHECK_MISSING":
                return "订单缺失检查";
            
            // 收银员日志同步
            case "OPERATION_LOG_INCREMENTAL":
                return "收银员日志增量同步";
            case "OPERATION_LOG_FULL":
                return "收银员日志全量同步";
            case "OPERATION_LOG_CHECK_MISSING":
                return "收银员日志缺失检查";
            
            // 通用类型
            case "INCREMENTAL":
                return "增量同步";
            case "FULL":
                return "全量同步";
            case "PROGRESSIVE":
                return "渐进式同步";
            case "CHECK_MISSING":
                return "缺失检查";
            
            default:
                return syncType;
        }
    }

    /**
     * 检查是否为订单同步类型
     */
    public static boolean isOrderSyncType(String syncType) {
        return syncType != null && syncType.startsWith("ORDER_");
    }

    /**
     * 检查是否为收银员日志同步类型
     */
    public static boolean isOperationLogSyncType(String syncType) {
        return syncType != null && syncType.startsWith("OPERATION_LOG_");
    }

    /**
     * 检查是否为增量同步类型
     */
    public static boolean isIncrementalType(String syncType) {
        return syncType != null && syncType.endsWith("_INCREMENTAL");
    }

    /**
     * 检查是否为全量同步类型
     */
    public static boolean isFullType(String syncType) {
        return syncType != null && syncType.endsWith("_FULL");
    }
}
