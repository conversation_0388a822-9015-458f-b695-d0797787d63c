package com.rtpos.server.repository;

import com.rtpos.server.entity.Product;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 商品数据访问层
 * 
 * <AUTHOR>
 */
@Repository
public interface ProductRepository extends JpaRepository<Product, Long> {

    /**
     * 根据商品编码查找商品
     */
    Optional<Product> findByProductCode(String productCode);

    /**
     * 根据条形码查找商品
     */
    Optional<Product> findByBarcode(String barcode);

    /**
     * 根据分类ID查找商品
     */
    Page<Product> findByCategoryIdAndDeletedFalse(Long categoryId, Pageable pageable);

    /**
     * 根据状态查找商品
     */
    Page<Product> findByStatusAndDeletedFalse(Product.ProductStatus status, Pageable pageable);

    /**
     * 根据商品名称模糊查询
     */
    @Query("SELECT p FROM Product p WHERE p.productName LIKE %:name% AND p.deleted = false")
    Page<Product> findByProductNameContaining(@Param("name") String name, Pageable pageable);

    /**
     * 查找库存不足的商品
     */
    @Query("SELECT p FROM Product p WHERE p.stockQuantity <= p.minStock AND p.deleted = false")
    List<Product> findLowStockProducts();

    /**
     * 根据品牌查找商品
     */
    Page<Product> findByBrandAndDeletedFalse(String brand, Pageable pageable);
}
