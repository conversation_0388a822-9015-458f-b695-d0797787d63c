package com.rtpos.server.repository;

import com.rtpos.server.entity.PosStore;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * POS门店信息数据访问层
 * 
 * <AUTHOR>
 */
@Repository
public interface PosStoreRepository extends JpaRepository<PosStore, Long> {

    /**
     * 根据门店ID查询门店信息
     */
    @Query("SELECT p FROM PosStore p WHERE p.storeId = :storeId AND p.deleted = false")
    Optional<PosStore> findByStoreId(@Param("storeId") String storeId);

    /**
     * 根据门店名称模糊查询
     */
    @Query("SELECT p FROM PosStore p WHERE p.storeName LIKE %:storeName% AND p.deleted = false " +
           "ORDER BY p.storeId ASC")
    List<PosStore> findByStoreNameContaining(@Param("storeName") String storeName, Pageable pageable);

    /**
     * 根据大区序号查询门店列表
     */
    @Query("SELECT p FROM PosStore p WHERE p.pgSeq = :pgSeq AND p.deleted = false " +
           "ORDER BY p.storeId ASC")
    List<PosStore> findByPgSeq(@Param("pgSeq") String pgSeq, Pageable pageable);

    /**
     * 根据大区名称查询门店列表
     */
    @Query("SELECT p FROM PosStore p WHERE p.pgName = :pgName AND p.deleted = false " +
           "ORDER BY p.storeId ASC")
    List<PosStore> findByPgName(@Param("pgName") String pgName, Pageable pageable);

    /**
     * 根据子区域ID查询门店列表
     */
    @Query("SELECT p FROM PosStore p WHERE p.subId = :subId AND p.deleted = false " +
           "ORDER BY p.storeId ASC")
    List<PosStore> findBySubId(@Param("subId") String subId, Pageable pageable);

    /**
     * 根据子区域名称查询门店列表
     */
    @Query("SELECT p FROM PosStore p WHERE p.subArea = :subArea AND p.deleted = false " +
           "ORDER BY p.storeId ASC")
    List<PosStore> findBySubArea(@Param("subArea") String subArea, Pageable pageable);

    /**
     * 获取所有大区列表（去重）
     */
    @Query("SELECT DISTINCT p.pgSeq, p.pgName FROM PosStore p WHERE p.deleted = false " +
           "AND p.pgSeq IS NOT NULL AND p.pgName IS NOT NULL ORDER BY p.pgSeq ASC")
    List<Object[]> findDistinctRegions();

    /**
     * 获取指定大区下的所有子区域列表（去重）
     */
    @Query("SELECT DISTINCT p.subId, p.subArea FROM PosStore p WHERE p.pgSeq = :pgSeq " +
           "AND p.deleted = false AND p.subId IS NOT NULL AND p.subArea IS NOT NULL " +
           "ORDER BY p.subId ASC")
    List<Object[]> findDistinctSubAreasByPgSeq(@Param("pgSeq") String pgSeq);

    /**
     * 检查门店是否已存在
     */
    @Query("SELECT COUNT(p) > 0 FROM PosStore p WHERE p.storeId = :storeId AND p.deleted = false")
    boolean existsByStoreId(@Param("storeId") String storeId);

    /**
     * 根据同步状态查询门店
     */
    @Query("SELECT p FROM PosStore p WHERE p.syncStatus = :syncStatus AND p.deleted = false " +
           "ORDER BY p.createdAt ASC")
    List<PosStore> findBySyncStatus(@Param("syncStatus") Integer syncStatus, Pageable pageable);

    /**
     * 统计门店总数
     */
    @Query("SELECT COUNT(p) FROM PosStore p WHERE p.deleted = false")
    Long countAllStores();

    /**
     * 统计各大区门店数量
     */
    @Query("SELECT p.pgSeq, p.pgName, COUNT(p) FROM PosStore p WHERE p.deleted = false " +
           "GROUP BY p.pgSeq, p.pgName ORDER BY p.pgSeq ASC")
    List<Object[]> countStoresByRegion();
}
