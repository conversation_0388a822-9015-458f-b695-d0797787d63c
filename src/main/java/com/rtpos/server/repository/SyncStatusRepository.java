package com.rtpos.server.repository;

import com.rtpos.server.entity.SyncStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 同步状态数据访问层
 *
 * <AUTHOR>
 */
@Repository
public interface SyncStatusRepository extends JpaRepository<SyncStatus, Long> {

    /**
     * 查找指定类型和门店的最新同步记录
     */
    @Query("SELECT s FROM SyncStatus s WHERE s.syncType = :syncType AND s.storeId = :storeId " +
           "ORDER BY s.syncTime DESC LIMIT 1")
    Optional<SyncStatus> findLatestByTypeAndStore(@Param("syncType") String syncType, 
                                                  @Param("storeId") String storeId);

    /**
     * 查找指定类型和门店的最后一次成功同步记录
     */
    @Query("SELECT s FROM SyncStatus s WHERE s.syncType = :syncType AND s.storeId = :storeId " +
           "AND s.status = 'SUCCESS' ORDER BY s.syncTime DESC LIMIT 1")
    Optional<SyncStatus> findLastSuccessfulSync(@Param("syncType") String syncType, 
                                                @Param("storeId") String storeId);

    /**
     * 查找指定类型的最新同步记录
     */
    @Query("SELECT s FROM SyncStatus s WHERE s.syncType = :syncType " +
           "ORDER BY s.syncTime DESC LIMIT 1")
    Optional<SyncStatus> findLatestByType(@Param("syncType") String syncType);

    /**
     * 统计指定类型的同步次数
     */
    @Query("SELECT COUNT(s) FROM SyncStatus s WHERE s.syncType = :syncType")
    long countByType(@Param("syncType") String syncType);

    /**
     * 统计指定类型和状态的同步次数
     */
    @Query("SELECT COUNT(s) FROM SyncStatus s WHERE s.syncType = :syncType AND s.status = :status")
    long countByTypeAndStatus(@Param("syncType") String syncType, @Param("status") String status);

    /**
     * 获取指定类型的平均耗时
     */
    @Query("SELECT AVG(s.durationMs) FROM SyncStatus s WHERE s.syncType = :syncType " +
           "AND s.durationMs IS NOT NULL AND s.status = 'SUCCESS'")
    Double getAverageDurationByType(@Param("syncType") String syncType);

    /**
     * 删除指定时间之前的记录
     */
    @Modifying
    @Query("DELETE FROM SyncStatus s WHERE s.createdAt < :cutoffDate")
    int deleteByCreatedAtBefore(@Param("cutoffDate") LocalDateTime cutoffDate);

    /**
     * 查找运行中的同步任务
     */
    @Query("SELECT s FROM SyncStatus s WHERE s.status = 'RUNNING' " +
           "AND s.syncTime < :timeoutThreshold")
    java.util.List<SyncStatus> findStuckSyncTasks(@Param("timeoutThreshold") LocalDateTime timeoutThreshold);
}
