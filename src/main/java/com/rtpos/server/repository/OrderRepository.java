package com.rtpos.server.repository;

import com.rtpos.server.entity.Order;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 订单数据访问层
 *
 * <AUTHOR>
 */
@Repository
public interface OrderRepository extends JpaRepository<Order, Long> {

    /**
     * 根据订单号查找订单
     */
    Optional<Order> findByOrderNo(String orderNo);

    /**
     * 根据门店ID查找订单
     */
    Page<Order> findByStoreIdAndDeletedFalse(Long storeId, Pageable pageable);

    /**
     * 根据订单状态查找订单
     */
    Page<Order> findByStatusAndDeletedFalse(Order.OrderStatus status, Pageable pageable);

    /**
     * 根据时间范围查找订单
     */
    @Query("SELECT o FROM Order o WHERE o.orderTime BETWEEN :startTime AND :endTime AND o.deleted = false")
    List<Order> findByOrderTimeBetween(@Param("startTime") LocalDateTime startTime,
                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 根据门店ID和时间范围查找订单
     */
    @Query("SELECT o FROM Order o WHERE o.storeId = :storeId AND o.orderTime BETWEEN :startTime AND :endTime AND o.deleted = false")
    List<Order> findByStoreIdAndOrderTimeBetween(@Param("storeId") Long storeId,
                                                @Param("startTime") LocalDateTime startTime,
                                                @Param("endTime") LocalDateTime endTime);

    /**
     * 统计门店今日订单数量
     */
    @Query("SELECT COUNT(o) FROM Order o WHERE o.storeId = :storeId AND CAST(o.orderTime AS date) = CURRENT_DATE AND o.deleted = false")
    Long countTodayOrdersByStoreId(@Param("storeId") Long storeId);

    /**
     * 统计门店今日销售额
     */
    @Query("SELECT COALESCE(SUM(o.actualAmount), 0) FROM Order o WHERE o.storeId = :storeId AND CAST(o.orderTime AS date) = CURRENT_DATE AND o.status = 'COMPLETED' AND o.deleted = false")
    Double sumTodaySalesByStoreId(@Param("storeId") Long storeId);

    /**
     * 查找需要同步的订单（最近更新的）
     */
    @Query("SELECT o FROM Order o WHERE o.updatedAt > :lastSyncTime AND o.deleted = false ORDER BY o.updatedAt ASC")
    List<Order> findOrdersForSync(@Param("lastSyncTime") LocalDateTime lastSyncTime, Pageable pageable);
}
