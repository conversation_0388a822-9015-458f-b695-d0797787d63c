package com.rtpos.server.service.impl;

import com.rtpos.server.dto.PosStoreDTO;
import com.rtpos.server.dto.PosStoreQueryResponse;
import com.rtpos.server.entity.PosStore;
import com.rtpos.server.repository.PosStoreRepository;
import com.rtpos.server.service.PosStoreService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * POS门店信息服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PosStoreServiceImpl implements PosStoreService {

    private final PosStoreRepository storeRepository;
    private final RestTemplate restTemplate;

    @Value("${pos.api.store-base-url}")
    private String apiBaseUrl;

    @Value("${pos.api.store-path}")
    private String storePath;

    @Override
    @Transactional
    public PosStoreQueryResponse syncStoresFromApi() {
        log.info("Starting to sync stores from external API");

        try {
            String url = apiBaseUrl + storePath;
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // 空的请求体
            HttpEntity<String> entity = new HttpEntity<>("{}", headers);
            
            ResponseEntity<PosStoreQueryResponse> response = restTemplate.exchange(
                    url, HttpMethod.POST, entity, PosStoreQueryResponse.class);
            
            PosStoreQueryResponse result = response.getBody();
            
            if (result != null && result.isSuccess() && result.getStoreList() != null) {
                log.info("Successfully fetched {} stores from API", result.getStoreList().size());
                
                // 保存门店数据到本地数据库
                saveStoresToDatabase(result.getStoreList());
                
                return result;
            } else {
                log.warn("API returned error response: {}", result != null ? result.getMsg() : "null response");
                return result;
            }
            
        } catch (Exception e) {
            log.error("Failed to sync stores from API", e);
            throw new RuntimeException("同步门店数据失败", e);
        }
    }

    @Override
    public Page<PosStore> getAllStores(Pageable pageable) {
        return storeRepository.findAll(pageable);
    }

    @Override
    public Optional<PosStore> getStoreById(String storeId) {
        return storeRepository.findByStoreId(storeId);
    }

    @Override
    public List<PosStore> searchStoresByName(String storeName, Pageable pageable) {
        return storeRepository.findByStoreNameContaining(storeName, pageable);
    }

    @Override
    public List<PosStore> getStoresByRegion(String pgSeq, Pageable pageable) {
        return storeRepository.findByPgSeq(pgSeq, pageable);
    }

    @Override
    public List<PosStore> getStoresBySubArea(String subId, Pageable pageable) {
        return storeRepository.findBySubId(subId, pageable);
    }

    @Override
    public List<Map<String, String>> getAllRegions() {
        List<Object[]> results = storeRepository.findDistinctRegions();
        List<Map<String, String>> regions = new ArrayList<>();
        
        for (Object[] result : results) {
            Map<String, String> region = new HashMap<>();
            region.put("pgSeq", (String) result[0]);
            region.put("pgName", (String) result[1]);
            regions.add(region);
        }
        
        return regions;
    }

    @Override
    public List<Map<String, String>> getSubAreasByRegion(String pgSeq) {
        List<Object[]> results = storeRepository.findDistinctSubAreasByPgSeq(pgSeq);
        List<Map<String, String>> subAreas = new ArrayList<>();
        
        for (Object[] result : results) {
            Map<String, String> subArea = new HashMap<>();
            subArea.put("subId", (String) result[0]);
            subArea.put("subArea", (String) result[1]);
            subAreas.add(subArea);
        }
        
        return subAreas;
    }

    @Override
    public Long getTotalStoreCount() {
        return storeRepository.countAllStores();
    }

    @Override
    public List<Map<String, Object>> getStoreCountByRegion() {
        List<Object[]> results = storeRepository.countStoresByRegion();
        List<Map<String, Object>> counts = new ArrayList<>();
        
        for (Object[] result : results) {
            Map<String, Object> count = new HashMap<>();
            count.put("pgSeq", result[0]);
            count.put("pgName", result[1]);
            count.put("storeCount", result[2]);
            counts.add(count);
        }
        
        return counts;
    }

    @Override
    @Transactional
    public PosStore saveStore(PosStore store) {
        return storeRepository.save(store);
    }

    @Override
    @Transactional
    public List<PosStore> saveStores(List<PosStore> stores) {
        return storeRepository.saveAll(stores);
    }

    @Override
    @Transactional
    public void deleteStore(String storeId) {
        Optional<PosStore> store = storeRepository.findByStoreId(storeId);
        if (store.isPresent()) {
            PosStore storeEntity = store.get();
            storeEntity.setDeleted(true);
            storeRepository.save(storeEntity);
            log.info("Soft deleted store: {}", storeId);
        }
    }

    @Override
    public boolean existsStore(String storeId) {
        return storeRepository.existsByStoreId(storeId);
    }

    /**
     * 保存门店数据到本地数据库
     */
    @Transactional
    protected void saveStoresToDatabase(List<PosStoreDTO> storeDTOs) {
        log.info("Saving {} stores to database", storeDTOs.size());
        
        int savedCount = 0;
        int updatedCount = 0;
        int skippedCount = 0;
        
        for (PosStoreDTO dto : storeDTOs) {
            try {
                // 检查是否已存在
                Optional<PosStore> existingStore = storeRepository.findByStoreId(dto.getStoreId());
                
                if (existingStore.isPresent()) {
                    // 更新现有记录
                    PosStore store = existingStore.get();
                    updateStoreFromDTO(store, dto);
                    storeRepository.save(store);
                    updatedCount++;
                } else {
                    // 创建新记录
                    PosStore newStore = convertDTOToEntity(dto);
                    storeRepository.save(newStore);
                    savedCount++;
                }
            } catch (Exception e) {
                log.error("Failed to save store: {}", dto, e);
                skippedCount++;
            }
        }
        
        log.info("Stores saved: {} new, {} updated, {} skipped", savedCount, updatedCount, skippedCount);
    }

    /**
     * 将DTO转换为实体
     */
    private PosStore convertDTOToEntity(PosStoreDTO dto) {
        PosStore entity = new PosStore();
        entity.setStoreId(dto.getStoreId());
        entity.setStoreName(dto.getStoreName());
        entity.setPgSeq(dto.getPgSeq());
        entity.setPgName(dto.getPgName());
        entity.setSubId(dto.getSubId());
        entity.setSubArea(dto.getSubArea());
        entity.setSyncStatus(1); // 已同步
        return entity;
    }

    /**
     * 从DTO更新实体
     */
    private void updateStoreFromDTO(PosStore entity, PosStoreDTO dto) {
        entity.setStoreName(dto.getStoreName());
        entity.setPgSeq(dto.getPgSeq());
        entity.setPgName(dto.getPgName());
        entity.setSubId(dto.getSubId());
        entity.setSubArea(dto.getSubArea());
        entity.setSyncStatus(1); // 已同步
    }
}
