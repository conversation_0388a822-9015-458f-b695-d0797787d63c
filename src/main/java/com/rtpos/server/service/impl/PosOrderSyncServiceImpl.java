package com.rtpos.server.service.impl;

import com.rtpos.server.dto.PosOrderDTO;
import com.rtpos.server.dto.PosOrderQueryRequest;
import com.rtpos.server.dto.PosOrderQueryResponse;
import com.rtpos.server.service.PosOrderService;
import com.rtpos.server.service.PosOrderSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

/**
 * POS订单数据同步服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PosOrderSyncServiceImpl implements PosOrderSyncService {

    private final PosOrderService posOrderService;
    private final RestTemplate restTemplate;

    @Value("${pos.api.base-url:http://middle-order-biz.beta1.fn}")
    private String apiBaseUrl;

    @Value("${pos.api.query-orders-path:/api/queryOrder/querySelfPosNodeOrders}")
    private String queryOrdersPath;

    @Value("${pos.sync.batch-size:100}")
    private int batchSize;

    @Value("${pos.sync.max-pages:50}")
    private int maxPages;

    @Value("${pos.sync.store-ids:1001}")
    private String[] storeIds;

    @Override
    public PosOrderQueryResponse syncPosOrdersFromApi(PosOrderQueryRequest request) {
        log.info("Syncing POS orders from API for store: {}, timeRange: {} - {}", 
                request.getStoreId(), request.getGeneralTimeStart(), request.getGeneralTimeEnd());

        try {
            String url = apiBaseUrl + queryOrdersPath;
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<PosOrderQueryRequest> entity = new HttpEntity<>(request, headers);
            
            ResponseEntity<PosOrderQueryResponse> response = restTemplate.exchange(
                    url, HttpMethod.POST, entity, PosOrderQueryResponse.class);
            
            PosOrderQueryResponse result = response.getBody();
            
            if (result != null && "00000000".equals(result.getRsCode()) && result.getBody() != null) {
                log.info("Successfully fetched {} orders from API", result.getBody().getTotal());
                
                // 保存订单数据到本地数据库
                if (result.getBody().getOrderList() != null && !result.getBody().getOrderList().isEmpty()) {
                    saveOrdersToDatabase(result.getBody().getOrderList());
                }
                
                return result;
            } else {
                log.warn("API returned error response: {}", result != null ? result.getMsg() : "null response");
                return result;
            }
            
        } catch (Exception e) {
            log.error("Failed to sync POS orders from API", e);
            throw new RuntimeException("同步POS订单数据失败", e);
        }
    }

    @Override
    public void syncPosOrdersByStore(String storeId, Long startTime, Long endTime) {
        log.info("Starting sync for store: {}, timeRange: {} - {}", storeId, startTime, endTime);
        
        int currentPage = 1;
        int totalSynced = 0;
        
        while (currentPage <= maxPages) {
            PosOrderQueryRequest request = new PosOrderQueryRequest();
            request.setStoreId(storeId);
            request.setGeneralTimeStart(startTime);
            request.setGeneralTimeEnd(endTime);
            request.setCurrentPage(currentPage);
            request.setPageSize(batchSize);
            
            PosOrderQueryResponse response = syncPosOrdersFromApi(request);
            
            if (response == null || response.getBody() == null || 
                response.getBody().getOrderList() == null || 
                response.getBody().getOrderList().isEmpty()) {
                log.info("No more orders to sync for page: {}", currentPage);
                break;
            }
            
            totalSynced += response.getBody().getOrderList().size();
            
            // 如果当前页的数据量小于页面大小，说明已经是最后一页
            if (response.getBody().getOrderList().size() < batchSize) {
                log.info("Reached last page: {}", currentPage);
                break;
            }
            
            currentPage++;
        }
        
        log.info("Completed sync for store: {}, total synced: {}", storeId, totalSynced);
    }

    @Override
    public void incrementalSyncPosOrders() {
        log.info("Starting incremental sync of POS orders");
        
        try {
            // 获取最新的订单，用于确定增量同步的起始时间
            List<PosOrderDTO> latestOrders = posOrderService.getLatestOrders(1);
            
            Long lastSyncTime;
            if (!latestOrders.isEmpty()) {
                lastSyncTime = latestOrders.get(0).getGmtModified();
            } else {
                // 如果没有历史数据，从24小时前开始同步
                lastSyncTime = System.currentTimeMillis() - 24 * 60 * 60 * 1000L;
            }
            
            Long currentTime = System.currentTimeMillis();
            
            // 这里需要根据实际业务需求配置需要同步的门店列表
            List<String> storeIds = getStoreIdsForSync();
            
            for (String storeId : storeIds) {
                syncPosOrdersByStore(storeId, lastSyncTime, currentTime);
            }
            
            log.info("Completed incremental sync of POS orders");
            
        } catch (Exception e) {
            log.error("Failed to perform incremental sync", e);
        }
    }

    @Override
    public void fullSyncPosOrders(String storeId, Long startTime, Long endTime) {
        log.info("Starting full sync for store: {}", storeId);
        
        try {
            syncPosOrdersByStore(storeId, startTime, endTime);
            log.info("Completed full sync for store: {}", storeId);
        } catch (Exception e) {
            log.error("Failed to perform full sync for store: {}", storeId, e);
        }
    }

    @Override
    public void checkAndSyncMissingOrders() {
        log.info("Starting check and sync missing orders");
        
        try {
            // 这里可以实现检查缺失订单的逻辑
            // 比如对比本地数据和远程数据，找出缺失的订单
            
            log.info("Completed check and sync missing orders");
        } catch (Exception e) {
            log.error("Failed to check and sync missing orders", e);
        }
    }

    /**
     * 保存订单数据到本地数据库
     */
    private void saveOrdersToDatabase(List<PosOrderDTO> orders) {
        log.info("Saving {} orders to database", orders.size());
        
        List<PosOrderDTO> newOrders = new ArrayList<>();
        List<PosOrderDTO> existingOrders = new ArrayList<>();
        
        // 分离新订单和已存在的订单
        for (PosOrderDTO order : orders) {
            if (posOrderService.existsByBizOrderId(order.getBizOrderId())) {
                existingOrders.add(order);
            } else {
                newOrders.add(order);
            }
        }
        
        // 保存新订单
        if (!newOrders.isEmpty()) {
            posOrderService.savePosOrders(newOrders);
            log.info("Saved {} new orders", newOrders.size());
        }
        
        // 更新已存在的订单
        if (!existingOrders.isEmpty()) {
            for (PosOrderDTO order : existingOrders) {
                PosOrderDTO existing = posOrderService.findByBizOrderId(order.getBizOrderId());
                if (existing != null) {
                    order.setId(existing.getId());
                    posOrderService.updatePosOrder(order);
                }
            }
            log.info("Updated {} existing orders", existingOrders.size());
        }
    }

    /**
     * 获取需要同步的门店ID列表
     * 从配置文件中获取门店ID列表
     */
    private List<String> getStoreIdsForSync() {
        List<String> storeIdList = new ArrayList<>();
        if (storeIds != null && storeIds.length > 0) {
            for (String storeId : storeIds) {
                if (storeId != null && !storeId.trim().isEmpty()) {
                    storeIdList.add(storeId.trim());
                }
            }
        }

        // 如果配置为空，使用默认门店
        if (storeIdList.isEmpty()) {
            storeIdList.add("1001");
            log.warn("No store IDs configured, using default store ID: 1001");
        }

        log.info("Configured store IDs for sync: {}", storeIdList);
        return storeIdList;
    }
}
