package com.rtpos.server.service.impl;

import com.rtpos.server.dto.PosOperationLogDTO;
import com.rtpos.server.dto.PosOperationLogQueryRequest;
import com.rtpos.server.dto.PosOperationLogQueryResponse;
import com.rtpos.server.entity.PosOperationLog;
import com.rtpos.server.repository.PosOperationLogRepository;
import com.rtpos.server.service.PosOperationLogSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * POS收银操作日志数据同步服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PosOperationLogSyncServiceImpl implements PosOperationLogSyncService {

    private final PosOperationLogRepository operationLogRepository;
    private final RestTemplate restTemplate;

    @Value("${pos.api.operation-log-base-url}")
    private String apiBaseUrl;

    @Value("${pos.api.operation-log-path}")
    private String operationLogPath;

    @Value("${pos.sync.operation-log.batch-size:20}")
    private int batchSize;

    @Value("${pos.sync.operation-log.max-pages:50}")
    private int maxPages;

    @Override
    public PosOperationLogQueryResponse syncOperationLogsFromApi(PosOperationLogQueryRequest request) {
        log.info("Syncing operation logs from API for store: {}, pos: {}, timeRange: {} - {}", 
                request.getStoreNo(), request.getPosNo(), request.getBeginTime(), request.getEndTime());

        try {
            String url = apiBaseUrl + operationLogPath;
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<PosOperationLogQueryRequest> entity = new HttpEntity<>(request, headers);
            
            ResponseEntity<PosOperationLogQueryResponse> response = restTemplate.exchange(
                    url, HttpMethod.POST, entity, PosOperationLogQueryResponse.class);
            
            PosOperationLogQueryResponse result = response.getBody();
            
            if (result != null && result.isSuccess() && result.getBody() != null) {
                log.info("Successfully fetched {} operation logs from API", result.getTotal());
                
                // 保存操作日志数据到本地数据库
                if (result.getOperationList() != null && !result.getOperationList().isEmpty()) {
                    saveOperationLogsToDatabase(result.getOperationList());
                }
                
                return result;
            } else {
                log.warn("API returned error response: {}", result != null ? result.getMsg() : "null response");
                return result;
            }
            
        } catch (Exception e) {
            log.error("Failed to sync operation logs from API", e);
            throw new RuntimeException("同步POS收银操作日志数据失败", e);
        }
    }

    @Override
    public void syncOperationLogsByStoreAndPos(Integer storeNo, Integer posNo, Long startTime, Long endTime) {
        log.info("Syncing operation logs for store: {}, pos: {}, timeRange: {} - {}", 
                storeNo, posNo, startTime, endTime);
        
        int currentPage = 1;
        int totalSynced = 0;
        
        while (currentPage <= maxPages) {
            PosOperationLogQueryRequest request = new PosOperationLogQueryRequest();
            request.setStoreNo(storeNo);
            request.setPosNo(posNo);
            request.setBeginTime(startTime);
            request.setEndTime(endTime);
            request.setPage(currentPage);
            request.setLimit(batchSize);
            
            PosOperationLogQueryResponse response = syncOperationLogsFromApi(request);
            
            if (response == null || response.getBody() == null || 
                response.getOperationList() == null || 
                response.getOperationList().isEmpty()) {
                log.info("No more operation logs to sync for page: {}", currentPage);
                break;
            }
            
            totalSynced += response.getOperationList().size();
            
            // 如果当前页的数据量小于页面大小，说明已经是最后一页
            if (response.getOperationList().size() < batchSize) {
                log.info("Reached last page: {}", currentPage);
                break;
            }
            
            currentPage++;
            
            // 添加延迟避免API调用过于频繁
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        log.info("Completed syncing operation logs for store: {}, pos: {}, total synced: {}", 
                storeNo, posNo, totalSynced);
    }

    @Override
    public void incrementalSyncOperationLogs() {
        log.info("Starting incremental sync for operation logs");
        
        try {
            // 获取最后同步时间（这里简化处理，实际应该从配置或数据库获取）
            long endTime = System.currentTimeMillis();
            long startTime = endTime - 60 * 60 * 1000L; // 1小时前
            
            // 从配置获取需要同步的门店和POS机列表
            String storeNos = getConfiguredStoreNos();
            String posNos = getConfiguredPosNos();
            
            batchSyncOperationLogs(storeNos, posNos, startTime, endTime);
            
            log.info("Completed incremental sync for operation logs");
        } catch (Exception e) {
            log.error("Failed to perform incremental sync for operation logs", e);
        }
    }

    @Override
    public void fullSyncOperationLogs(Integer storeNo, Integer posNo, Long startTime, Long endTime) {
        log.info("Starting full sync for operation logs: store {}, pos {}", storeNo, posNo);
        
        try {
            syncOperationLogsByStoreAndPos(storeNo, posNo, startTime, endTime);
            log.info("Completed full sync for operation logs: store {}, pos {}", storeNo, posNo);
        } catch (Exception e) {
            log.error("Failed to perform full sync for operation logs: store {}, pos {}", storeNo, posNo, e);
        }
    }

    @Override
    public void checkAndSyncMissingOperationLogs() {
        log.info("Starting check and sync missing operation logs");
        
        try {
            // 这里可以实现检查缺失操作日志的逻辑
            // 比如对比本地数据和远程数据，找出缺失的操作日志
            
            log.info("Completed check and sync missing operation logs");
        } catch (Exception e) {
            log.error("Failed to check and sync missing operation logs", e);
        }
    }

    @Override
    public void batchSyncOperationLogs(String storeNos, String posNos, Long startTime, Long endTime) {
        log.info("Starting batch sync for operation logs: stores {}, poses {}", storeNos, posNos);
        
        try {
            List<String> storeList = Arrays.asList(storeNos.split(","));
            List<String> posList = Arrays.asList(posNos.split(","));
            
            for (String storeNoStr : storeList) {
                for (String posNoStr : posList) {
                    try {
                        Integer storeNo = Integer.parseInt(storeNoStr.trim());
                        Integer posNo = Integer.parseInt(posNoStr.trim());
                        
                        syncOperationLogsByStoreAndPos(storeNo, posNo, startTime, endTime);
                        
                        // 添加延迟避免API调用过于频繁
                        Thread.sleep(200);
                    } catch (NumberFormatException e) {
                        log.warn("Invalid store or pos number: store={}, pos={}", storeNoStr, posNoStr);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
            
            log.info("Completed batch sync for operation logs");
        } catch (Exception e) {
            log.error("Failed to perform batch sync for operation logs", e);
        }
    }

    /**
     * 保存操作日志数据到本地数据库
     */
    @Transactional
    protected void saveOperationLogsToDatabase(List<PosOperationLogDTO> operationLogDTOs) {
        log.info("Saving {} operation logs to database", operationLogDTOs.size());
        
        int savedCount = 0;
        int updatedCount = 0;
        int skippedCount = 0;
        
        for (PosOperationLogDTO dto : operationLogDTOs) {
            try {
                // 检查是否已存在
                Optional<PosOperationLog> existingLog = operationLogRepository.findByUniqueKey(
                    dto.getStoreNo(), dto.getPosNo(), dto.getOccurrenceTime(), dto.getOperationType());
                
                if (existingLog.isPresent()) {
                    // 更新现有记录
                    PosOperationLog log = existingLog.get();
                    updateOperationLogFromDTO(log, dto);
                    operationLogRepository.save(log);
                    updatedCount++;
                } else {
                    // 创建新记录
                    PosOperationLog newLog = convertDTOToEntity(dto);
                    operationLogRepository.save(newLog);
                    savedCount++;
                }
            } catch (Exception e) {
                log.error("Failed to save operation log: {}", dto, e);
                skippedCount++;
            }
        }
        
        log.info("Operation logs saved: {} new, {} updated, {} skipped", savedCount, updatedCount, skippedCount);
    }

    /**
     * 将DTO转换为实体
     */
    private PosOperationLog convertDTOToEntity(PosOperationLogDTO dto) {
        PosOperationLog entity = new PosOperationLog();
        entity.setStoreNo(dto.getStoreNo());
        entity.setPosNo(dto.getPosNo());
        entity.setDeviceNo(dto.getDeviceNo());
        entity.setPosType(dto.getPosType());
        entity.setOperationType(dto.getOperationType());
        entity.setFlowId(dto.getFlowId());
        entity.setShiftNo(dto.getShiftNo());
        entity.setOrderId(dto.getOrderId());
        entity.setMembershipCardId(dto.getMembershipCardId());
        entity.setOccurrenceTime(dto.getOccurrenceTime());
        entity.setWorkTime(dto.getWorkTime());
        entity.setSyncStatus(1); // 已同步
        entity.setSyncTime(LocalDateTime.now());
        return entity;
    }

    /**
     * 从DTO更新实体
     */
    private void updateOperationLogFromDTO(PosOperationLog entity, PosOperationLogDTO dto) {
        entity.setDeviceNo(dto.getDeviceNo());
        entity.setPosType(dto.getPosType());
        entity.setFlowId(dto.getFlowId());
        entity.setShiftNo(dto.getShiftNo());
        entity.setOrderId(dto.getOrderId());
        entity.setMembershipCardId(dto.getMembershipCardId());
        entity.setWorkTime(dto.getWorkTime());
        entity.setSyncStatus(1); // 已同步
        entity.setSyncTime(LocalDateTime.now());
    }

    /**
     * 获取配置的门店编号列表
     */
    @Value("${pos.sync.operation-log.store-nos:1001}")
    private String configuredStoreNos;
    
    private String getConfiguredStoreNos() {
        return configuredStoreNos;
    }

    /**
     * 获取配置的POS机编号列表
     */
    @Value("${pos.sync.operation-log.pos-nos:21}")
    private String configuredPosNos;
    
    private String getConfiguredPosNos() {
        return configuredPosNos;
    }
}
