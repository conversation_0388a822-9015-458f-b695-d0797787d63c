package com.rtpos.server.service.impl;

import com.rtpos.server.dto.DashboardRequest;
import com.rtpos.server.dto.DashboardResponse;
import com.rtpos.server.dto.PosStoreStatusRequest;
import com.rtpos.server.dto.PosStoreStatusResponse;
import com.rtpos.server.entity.PosOperationLog;
import com.rtpos.server.entity.PosOrder;
import com.rtpos.server.repository.PosOperationLogRepository;
import com.rtpos.server.repository.PosOrderRepository;
import com.rtpos.server.service.DashboardService;
import com.rtpos.server.service.PosStoreStatusService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Dashboard数据服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DashboardServiceImpl implements DashboardService {

    private final PosStoreStatusService posStoreStatusService;
    private final PosOrderRepository posOrderRepository;
    private final PosOperationLogRepository posOperationLogRepository;

    @Override
    public DashboardResponse getDashboardData(DashboardRequest request) {
        log.info("Getting dashboard data for store: {}, date: {}", request.getStoreId(), request.getDate());

        DashboardResponse response = new DashboardResponse();
        
        // 设置默认日期为当天
        LocalDate queryDate = request.getDate() != null ? request.getDate() : LocalDate.now();
        
        try {
            // 1. 获取门店设备状态信息
            PosStoreStatusRequest statusRequest = new PosStoreStatusRequest();
            statusRequest.setStoreNo(Integer.parseInt(request.getStoreId()));
            PosStoreStatusResponse storeStatus = posStoreStatusService.getStoreStatus(statusRequest);
            
            // 2. 填充设备状态数据
            fillDeviceStatusData(response, storeStatus);
            
            // 3. 获取订单数据并计算时段分布
            fillOrderTrendData(response, request.getStoreId(), queryDate);
            
            // 4. 计算设备使用率
            fillUsageRateData(response, request.getStoreId(), queryDate, storeStatus);
            
            // 5. 填充能耗数据（模拟计算）
            fillEnergyData(response);
            
            log.info("Successfully generated dashboard data for store: {}", request.getStoreId());
            
        } catch (Exception e) {
            log.error("Failed to get dashboard data for store: {}", request.getStoreId(), e);
            throw new RuntimeException("获取Dashboard数据失败: " + e.getMessage(), e);
        }
        
        return response;
    }

    /**
     * 填充设备状态数据
     */
    private void fillDeviceStatusData(DashboardResponse response, PosStoreStatusResponse storeStatus) {
        if (storeStatus != null && storeStatus.isSuccess() && storeStatus.getBody() != null) {
            var body = storeStatus.getBody();
            
            response.setTotalDevices(body.getStoreAllPosCount());
            response.setOnlineDevices(body.getAllOnlPosCount());
            response.setManualPosCount(body.getManualPosCount());
            response.setSelfServicePosCount(body.getSelfPosCount());
            
            // 设备状态分布
            DashboardResponse.DeviceStatusDistribution distribution = new DashboardResponse.DeviceStatusDistribution();
            
            // 人工POS状态
            DashboardResponse.DeviceStatus manualStatus = new DashboardResponse.DeviceStatus();
            manualStatus.setActive(body.getOnlManualPosNum());
            manualStatus.setIdle(0); // 暂时设为0，可以根据实际业务逻辑计算
            manualStatus.setOffline(body.getManualPosCount() - body.getOnlManualPosNum());
            distribution.setManual(manualStatus);
            
            // 自助POS状态
            DashboardResponse.DeviceStatus selfServiceStatus = new DashboardResponse.DeviceStatus();
            selfServiceStatus.setActive(body.getOnlSelfPosNum());
            selfServiceStatus.setIdle(0); // 暂时设为0，可以根据实际业务逻辑计算
            selfServiceStatus.setOffline(body.getSelfPosCount() - body.getOnlSelfPosNum());
            distribution.setSelfService(selfServiceStatus);
            
            response.setDeviceStatusDistribution(distribution);
        } else {
            // 设置默认值
            response.setTotalDevices(0);
            response.setOnlineDevices(0);
            response.setManualPosCount(0);
            response.setSelfServicePosCount(0);
            
            DashboardResponse.DeviceStatusDistribution distribution = new DashboardResponse.DeviceStatusDistribution();
            DashboardResponse.DeviceStatus defaultStatus = new DashboardResponse.DeviceStatus();
            defaultStatus.setActive(0);
            defaultStatus.setIdle(0);
            defaultStatus.setOffline(0);
            distribution.setManual(defaultStatus);
            distribution.setSelfService(defaultStatus);
            response.setDeviceStatusDistribution(distribution);
        }
    }

    /**
     * 填充订单时段分布数据
     */
    private void fillOrderTrendData(DashboardResponse response, String storeId, LocalDate queryDate) {
        try {
            LocalDateTime startTime = queryDate.atStartOfDay();
            LocalDateTime endTime = queryDate.atTime(LocalTime.MAX);

            log.debug("Querying orders for store: {}, date range: {} to {}", storeId, startTime, endTime);

            // 查询当天的订单数据
            List<PosOrder> orders = posOrderRepository.findByStoreIdAndOrderTimeDateTimeBetween(
                    storeId, startTime, endTime);

            log.debug("Found {} orders for store: {}", orders != null ? orders.size() : 0, storeId);

            // 按小时分组统计订单
            Map<Integer, Map<String, Integer>> hourlyStats = new HashMap<>();

            // 初始化24小时数据
            for (int hour = 0; hour < 24; hour++) {
                Map<String, Integer> stats = new HashMap<>();
                stats.put("manual", 0);
                stats.put("selfService", 0);
                stats.put("mobile", 0);  // 添加mobile类型支持
                hourlyStats.put(hour, stats);
            }

            // 统计订单数据
            if (orders != null) {
                for (PosOrder order : orders) {
                    if (order.getOrderTimeDateTime() != null && order.getOutOrderId() != null) {
                        int hour = order.getOrderTimeDateTime().getHour();
                        String deviceType = getDeviceTypeFromOrderId(order.getOutOrderId());

                        Map<String, Integer> stats = hourlyStats.get(hour);
                        if (stats != null) {
                            Integer currentCount = stats.get(deviceType);
                            if (currentCount != null) {
                                stats.put(deviceType, currentCount + 1);
                            } else {
                                log.warn("Device type '{}' not found in stats map for hour {}", deviceType, hour);
                                stats.put(deviceType, 1);
                            }
                        } else {
                            log.warn("Stats map not found for hour: {}", hour);
                        }
                    }
                }
            }

            // 转换为响应格式
            List<DashboardResponse.HourlyOrderTrendData> trendData = new ArrayList<>();
            for (int hour = 0; hour < 24; hour++) {
                DashboardResponse.HourlyOrderTrendData data = new DashboardResponse.HourlyOrderTrendData();
                data.setHour(String.format("%02d:00", hour));
                Map<String, Integer> stats = hourlyStats.get(hour);

                if (stats != null) {
                    Integer manualCount = stats.get("manual");
                    Integer selfServiceCount = stats.get("selfService");

                    data.setManual(manualCount != null ? manualCount : 0);
                    data.setSelfService(selfServiceCount != null ? selfServiceCount : 0);
                } else {
                    log.warn("Stats map is null for hour: {}, setting default values", hour);
                    data.setManual(0);
                    data.setSelfService(0);
                }

                trendData.add(data);
            }

            response.setHourlyOrderTrend(trendData);
            log.debug("Successfully generated hourly order trend data with {} entries", trendData.size());

        } catch (Exception e) {
            log.error("Error filling order trend data for store: {}, date: {}", storeId, queryDate, e);

            // 设置默认的空数据
            List<DashboardResponse.HourlyOrderTrendData> defaultTrendData = new ArrayList<>();
            for (int hour = 0; hour < 24; hour++) {
                DashboardResponse.HourlyOrderTrendData data = new DashboardResponse.HourlyOrderTrendData();
                data.setHour(String.format("%02d:00", hour));
                data.setManual(0);
                data.setSelfService(0);
                defaultTrendData.add(data);
            }
            response.setHourlyOrderTrend(defaultTrendData);
        }
    }

    /**
     * 根据订单ID判断设备类型
     * 第一个字符：9=人工POS，8=自助POS，7=移动POS
     */
    private String getDeviceTypeFromOrderId(String outOrderId) {
        if (outOrderId != null && !outOrderId.isEmpty()) {
            char firstChar = outOrderId.charAt(0);
            switch (firstChar) {
                case '9':
                    return "manual";
                case '8':
                    return "selfService";
                case '7':
                    // 移动POS归类为人工POS
                    return "manual";
                default:
                    log.debug("Unknown device type for order ID: {}, defaulting to manual", outOrderId);
                    return "manual"; // 默认为人工POS
            }
        }
        log.debug("Order ID is null or empty, defaulting to manual");
        return "manual";
    }

    /**
     * 填充使用率数据
     */
    private void fillUsageRateData(DashboardResponse response, String storeId, LocalDate queryDate, 
                                   PosStoreStatusResponse storeStatus) {
        
        // 计算平均使用率（基于操作日志）
        double averageUsageRate = calculateAverageUsageRate(storeId, queryDate, storeStatus);
        response.setAverageUsageRate(averageUsageRate);
        
        // 生成使用率趋势数据（最近7天）
        List<DashboardResponse.UsageRateTrendData> trendData = new ArrayList<>();
        for (int i = 6; i >= 0; i--) {
            LocalDate date = queryDate.minusDays(i);
            DashboardResponse.UsageRateTrendData data = new DashboardResponse.UsageRateTrendData();
            data.setDate(date.format(DateTimeFormatter.ofPattern("MM-dd")));
            
            // 这里可以根据实际业务逻辑计算每天的使用率
            // 暂时使用模拟数据
            data.setManual(40.0 + Math.random() * 20);
            data.setSelfService(35.0 + Math.random() * 15);
            
            trendData.add(data);
        }
        response.setUsageRateTrend(trendData);
    }

    /**
     * 计算平均使用率
     */
    private double calculateAverageUsageRate(String storeId, LocalDate queryDate, PosStoreStatusResponse storeStatus) {
        if (storeStatus == null || !storeStatus.isSuccess() || storeStatus.getBody() == null) {
            return 0.0;
        }
        
        var body = storeStatus.getBody();
        List<Integer> onlinePosNos = body.getOnline();
        
        if (onlinePosNos == null || onlinePosNos.isEmpty()) {
            return 0.0;
        }
        
        LocalDateTime startTime = queryDate.atStartOfDay();
        LocalDateTime endTime = queryDate.atTime(LocalTime.MAX);
        
        // 查询在线设备的操作日志
        List<PosOperationLog> operationLogs = posOperationLogRepository
                .findByStoreNoAndPosNoInAndOccurrenceTimeDateTimeBetween(
                        Integer.parseInt(storeId), onlinePosNos, startTime, endTime);
        
        // 计算使用率（简化计算：有操作日志的设备视为在使用）
        Set<Integer> activePosNos = operationLogs.stream()
                .map(PosOperationLog::getPosNo)
                .collect(Collectors.toSet());
        
        if (onlinePosNos.isEmpty()) {
            return 0.0;
        }
        
        return (double) activePosNos.size() / onlinePosNos.size() * 100;
    }

    /**
     * 填充能耗数据（模拟计算）
     */
    private void fillEnergyData(DashboardResponse response) {
        // 基于设备数量模拟计算能耗
        int totalDevices = response.getTotalDevices() != null ? response.getTotalDevices() : 0;
        int onlineDevices = response.getOnlineDevices() != null ? response.getOnlineDevices() : 0;
        
        // 模拟计算：每台在线设备每天耗电约1.5kWh
        double dailyConsumption = onlineDevices * 1.5;
        response.setDailyPowerConsumption(dailyConsumption);
        response.setMonthlyPowerConsumption(dailyConsumption * 30);
        response.setYearlyProjectedSavings(dailyConsumption * 365 * 0.1); // 假设节能10%
        
        // 生成节能趋势数据（最近7天）
        List<DashboardResponse.EnergySavingTrendData> energyTrend = new ArrayList<>();
        for (int i = 6; i >= 0; i--) {
            LocalDate date = LocalDate.now().minusDays(i);
            DashboardResponse.EnergySavingTrendData data = new DashboardResponse.EnergySavingTrendData();
            data.setDate(date.format(DateTimeFormatter.ofPattern("MM-dd")));
            data.setSaving(dailyConsumption * 0.1 + Math.random() * 5); // 模拟节能数据
            energyTrend.add(data);
        }
        response.setEnergySavingTrend(energyTrend);
    }
}
