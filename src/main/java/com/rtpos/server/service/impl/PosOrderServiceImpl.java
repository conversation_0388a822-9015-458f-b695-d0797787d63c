package com.rtpos.server.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rtpos.server.dto.PosOrderDTO;
import com.rtpos.server.dto.PosOrderQueryRequest;
import com.rtpos.server.entity.PosOrder;
import com.rtpos.server.exception.BusinessException;
import com.rtpos.server.repository.PosOrderRepository;
import com.rtpos.server.service.PosOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.stream.Collectors;

/**
 * POS订单服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PosOrderServiceImpl implements PosOrderService {

    private final PosOrderRepository posOrderRepository;
    private final ObjectMapper objectMapper;

    @Override
    @Transactional
    public PosOrderDTO savePosOrder(PosOrderDTO posOrderDTO) {
        log.info("Saving POS order: {}", posOrderDTO.getBizOrderId());
        
        PosOrder posOrder = convertToEntity(posOrderDTO);
        posOrder.setCreatedAt(LocalDateTime.now());
        posOrder.setUpdatedAt(LocalDateTime.now());
        
        PosOrder savedOrder = posOrderRepository.save(posOrder);
        PosOrderDTO result = convertToDTO(savedOrder);
        
        log.info("POS order saved successfully: {}", savedOrder.getId());
        return result;
    }

    @Override
    @Transactional
    public List<PosOrderDTO> savePosOrders(List<PosOrderDTO> posOrderDTOs) {
        log.info("Batch saving {} POS orders", posOrderDTOs.size());
        
        List<PosOrder> posOrders = posOrderDTOs.stream()
                .map(this::convertToEntity)
                .peek(order -> {
                    order.setCreatedAt(LocalDateTime.now());
                    order.setUpdatedAt(LocalDateTime.now());
                })
                .collect(Collectors.toList());
        
        List<PosOrder> savedOrders = posOrderRepository.saveAll(posOrders);
        List<PosOrderDTO> result = savedOrders.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        
        log.info("Batch saved {} POS orders successfully", result.size());
        return result;
    }

    @Override
    public PosOrderDTO findById(Long id) {
        return posOrderRepository.findById(id)
                .map(this::convertToDTO)
                .orElse(null);
    }

    @Override
    public PosOrderDTO findByBizOrderId(Long bizOrderId) {
        return posOrderRepository.findByBizOrderId(bizOrderId)
                .map(this::convertToDTO)
                .orElse(null);
    }

    @Override
    public PosOrderDTO findByOutOrderId(String outOrderId) {
        return posOrderRepository.findByOutOrderId(outOrderId)
                .map(this::convertToDTO)
                .orElse(null);
    }

    @Override
    public Page<PosOrderDTO> findPosOrders(PosOrderQueryRequest request) {
        Pageable pageable = PageRequest.of(
                request.getCurrentPage() - 1, 
                request.getPageSize()
        );
        
        Page<PosOrder> orders = posOrderRepository.findByStoreIdAndOrderTimeBetween(
                request.getStoreId(),
                request.getGeneralTimeStart(),
                request.getGeneralTimeEnd(),
                pageable
        );
        
        return orders.map(this::convertToDTO);
    }

    @Override
    public Page<PosOrderDTO> findByStoreId(String storeId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<PosOrder> orders = posOrderRepository.findByStoreIdAndDeletedFalse(storeId, pageable);
        return orders.map(this::convertToDTO);
    }

    @Override
    public Page<PosOrderDTO> findByStoreIdAndTimeRange(String storeId, Long startTime, Long endTime, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<PosOrder> orders = posOrderRepository.findByStoreIdAndOrderTimeBetween(
                storeId, startTime, endTime, pageable);
        return orders.map(this::convertToDTO);
    }

    @Override
    public Page<PosOrderDTO> findByStoreIdAndTimeRange(String storeId, LocalDateTime startTime, LocalDateTime endTime, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<PosOrder> orders = posOrderRepository.findByStoreIdAndOrderTimeDateTimeBetween(
                storeId, startTime, endTime, pageable);
        return orders.map(this::convertToDTO);
    }

    @Override
    public Long countByStoreId(String storeId) {
        return posOrderRepository.countByStoreId(storeId);
    }

    @Override
    public Long countByStoreIdAndTimeRange(String storeId, Long startTime, Long endTime) {
        return posOrderRepository.countByStoreIdAndOrderTimeBetween(storeId, startTime, endTime);
    }

    @Override
    public Long sumSalesByStoreIdAndTimeRange(String storeId, Long startTime, Long endTime) {
        return posOrderRepository.sumDiscountAmtByStoreIdAndOrderTimeBetween(storeId, startTime, endTime);
    }

    @Override
    @Transactional
    public PosOrderDTO updatePosOrder(PosOrderDTO posOrderDTO) {
        PosOrder existingOrder = posOrderRepository.findById(posOrderDTO.getId())
                .orElseThrow(() -> new BusinessException("POS订单不存在: " + posOrderDTO.getId()));
        
        BeanUtils.copyProperties(posOrderDTO, existingOrder, "id", "createdAt");
        existingOrder.setUpdatedAt(LocalDateTime.now());
        
        PosOrder updatedOrder = posOrderRepository.save(existingOrder);
        return convertToDTO(updatedOrder);
    }

    @Override
    @Transactional
    public void deletePosOrder(Long id) {
        PosOrder order = posOrderRepository.findById(id)
                .orElseThrow(() -> new BusinessException("POS订单不存在: " + id));
        
        order.setDeleted(true);
        order.setUpdatedAt(LocalDateTime.now());
        posOrderRepository.save(order);
    }

    @Override
    public boolean existsByBizOrderId(Long bizOrderId) {
        return posOrderRepository.existsByBizOrderId(bizOrderId);
    }

    @Override
    public List<PosOrderDTO> getLatestOrders(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        List<PosOrder> orders = posOrderRepository.findLatestOrders(pageable);
        return orders.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    @Override
    public List<PosOrderDTO> getOrdersForSync(Long lastSyncTime, int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        List<PosOrder> orders = posOrderRepository.findOrdersForSync(lastSyncTime, pageable);
        return orders.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    /**
     * 将DTO转换为实体
     */
    private PosOrder convertToEntity(PosOrderDTO dto) {
        PosOrder entity = new PosOrder();
        BeanUtils.copyProperties(dto, entity, "mainExtendedJsonModel", "id", "version");
        
        // 转换时间戳为LocalDateTime
        if (dto.getOrderTime() != null) {
            entity.setOrderTimeDateTime(
                    LocalDateTime.ofInstant(Instant.ofEpochMilli(dto.getOrderTime()), ZoneId.systemDefault())
            );
        }
        if (dto.getPayTime() != null) {
            entity.setPayTimeDateTime(
                    LocalDateTime.ofInstant(Instant.ofEpochMilli(dto.getPayTime()), ZoneId.systemDefault())
            );
        }
        
        // 转换金额（分转元）
        if (dto.getOriginalAmt() != null) {
            entity.setOriginalAmount(BigDecimal.valueOf(dto.getOriginalAmt()).divide(BigDecimal.valueOf(100)));
        }
        if (dto.getDiscountAmt() != null) {
            entity.setDiscountAmount(BigDecimal.valueOf(dto.getDiscountAmt()).divide(BigDecimal.valueOf(100)));
        }
        if (dto.getMemberDiscountAmt() != null) {
            entity.setMemberDiscountAmount(BigDecimal.valueOf(dto.getMemberDiscountAmt()).divide(BigDecimal.valueOf(100)));
        }
        if (dto.getPackingFee() != null) {
            entity.setPackingFeeAmount(BigDecimal.valueOf(dto.getPackingFee()).divide(BigDecimal.valueOf(100)));
        }
        
        // 转换扩展JSON
        if (dto.getMainExtendedJsonModel() != null) {
            try {
                entity.setMainExtendedJsonModel(objectMapper.writeValueAsString(dto.getMainExtendedJsonModel()));
            } catch (JsonProcessingException e) {
                log.warn("Failed to serialize mainExtendedJsonModel", e);
            }
        }
        
        // 设置删除标记
        entity.setDeleted(dto.getDeleted() != null && dto.getDeleted() == 1);
        entity.setOriginalDeleted(dto.getDeleted());
        
        return entity;
    }

    /**
     * 将实体转换为DTO
     */
    private PosOrderDTO convertToDTO(PosOrder entity) {
        PosOrderDTO dto = new PosOrderDTO();
        BeanUtils.copyProperties(entity, dto, "mainExtendedJsonModel");
        
        // 转换金额（元转分）
        if (entity.getOriginalAmount() != null) {
            dto.setOriginalAmount(entity.getOriginalAmount());
        }
        if (entity.getDiscountAmount() != null) {
            dto.setDiscountAmount(entity.getDiscountAmount());
        }
        if (entity.getMemberDiscountAmount() != null) {
            dto.setMemberDiscountAmount(entity.getMemberDiscountAmount());
        }
        if (entity.getPackingFeeAmount() != null) {
            dto.setPackingFeeAmount(entity.getPackingFeeAmount());
        }
        
        // 转换扩展JSON
        if (entity.getMainExtendedJsonModel() != null) {
            try {
                dto.setMainExtendedJsonModel(
                        objectMapper.readValue(entity.getMainExtendedJsonModel(), PosOrderDTO.MainExtendedJsonModel.class)
                );
            } catch (JsonProcessingException e) {
                log.warn("Failed to deserialize mainExtendedJsonModel", e);
            }
        }
        
        // 设置删除标记
        dto.setDeleted(entity.getOriginalDeleted());
        
        return dto;
    }
}
