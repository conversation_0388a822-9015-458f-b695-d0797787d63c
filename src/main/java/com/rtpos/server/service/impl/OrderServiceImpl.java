package com.rtpos.server.service.impl;

import com.rtpos.server.cache.CacheService;
import com.rtpos.server.dto.OrderDTO;
import com.rtpos.server.dto.OrderItemDTO;
import com.rtpos.server.entity.Order;
import com.rtpos.server.entity.OrderItem;
import com.rtpos.server.exception.BusinessException;
import com.rtpos.server.repository.OrderRepository;
import com.rtpos.server.service.OrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class OrderServiceImpl implements OrderService {

    private final OrderRepository orderRepository;
    private final CacheService cacheService;

    private static final String CACHE_KEY_PREFIX = "order:";
    private static final String CACHE_KEY_ORDER_NO_PREFIX = "order:no:";
    private static final Duration CACHE_TTL = Duration.ofMinutes(30);

    @Override
    public OrderDTO createOrder(OrderDTO orderDTO) {
        log.info("Creating order: {}", orderDTO.getOrderNo());
        
        Order order = convertToEntity(orderDTO);
        order.setCreatedAt(LocalDateTime.now());
        order.setUpdatedAt(LocalDateTime.now());
        
        Order savedOrder = orderRepository.save(order);
        OrderDTO result = convertToDTO(savedOrder);
        
        // 缓存订单
        cacheOrder(result);
        
        log.info("Order created successfully: {}", savedOrder.getId());
        return result;
    }

    @Override
    @CacheEvict(value = "orders", key = "#id")
    public OrderDTO updateOrder(Long id, OrderDTO orderDTO) {
        log.info("Updating order: {}", id);
        
        Order existingOrder = orderRepository.findById(id)
            .orElseThrow(() -> new BusinessException("订单不存在: " + id));
        
        // 更新订单信息
        BeanUtils.copyProperties(orderDTO, existingOrder, "id", "createdAt", "version");
        existingOrder.setUpdatedAt(LocalDateTime.now());
        
        Order savedOrder = orderRepository.save(existingOrder);
        OrderDTO result = convertToDTO(savedOrder);
        
        // 更新缓存
        cacheOrder(result);
        
        log.info("Order updated successfully: {}", id);
        return result;
    }

    @Override
    @Cacheable(value = "orders", key = "#id")
    public OrderDTO findById(Long id) {
        log.debug("Finding order by id: {}", id);
        
        // 先从缓存查找
        String cacheKey = CACHE_KEY_PREFIX + id;
        OrderDTO cached = cacheService.get(cacheKey, OrderDTO.class);
        if (cached != null) {
            return cached;
        }
        
        Order order = orderRepository.findById(id)
            .orElseThrow(() -> new BusinessException("订单不存在: " + id));
        
        OrderDTO result = convertToDTO(order);
        cacheOrder(result);
        
        return result;
    }

    @Override
    public OrderDTO findByOrderNo(String orderNo) {
        log.debug("Finding order by orderNo: {}", orderNo);
        
        // 先从缓存查找
        String cacheKey = CACHE_KEY_ORDER_NO_PREFIX + orderNo;
        OrderDTO cached = cacheService.get(cacheKey, OrderDTO.class);
        if (cached != null) {
            return cached;
        }
        
        Order order = orderRepository.findByOrderNo(orderNo)
            .orElseThrow(() -> new BusinessException("订单不存在: " + orderNo));
        
        OrderDTO result = convertToDTO(order);
        cacheOrder(result);
        
        return result;
    }

    @Override
    public Page<OrderDTO> findAll(Pageable pageable) {
        log.debug("Finding all orders with pageable: {}", pageable);
        
        Page<Order> orders = orderRepository.findAll(pageable);
        return orders.map(this::convertToDTO);
    }

    @Override
    public Page<OrderDTO> findByStoreId(Long storeId, Pageable pageable) {
        log.debug("Finding orders by storeId: {} with pageable: {}", storeId, pageable);
        
        Page<Order> orders = orderRepository.findByStoreIdAndDeletedFalse(storeId, pageable);
        return orders.map(this::convertToDTO);
    }

    @Override
    public Page<OrderDTO> findByStatus(Order.OrderStatus status, Pageable pageable) {
        log.debug("Finding orders by status: {} with pageable: {}", status, pageable);
        
        Page<Order> orders = orderRepository.findByStatusAndDeletedFalse(status, pageable);
        return orders.map(this::convertToDTO);
    }

    @Override
    public List<OrderDTO> findByOrderTimeBetween(LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("Finding orders between {} and {}", startTime, endTime);
        
        List<Order> orders = orderRepository.findByOrderTimeBetween(startTime, endTime);
        return orders.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    @Override
    public List<OrderDTO> findByStoreIdAndOrderTimeBetween(Long storeId, LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("Finding orders by storeId: {} between {} and {}", storeId, startTime, endTime);
        
        List<Order> orders = orderRepository.findByStoreIdAndOrderTimeBetween(storeId, startTime, endTime);
        return orders.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    @Override
    @CacheEvict(value = "orders", key = "#id")
    public void deleteOrder(Long id) {
        log.info("Deleting order: {}", id);
        
        Order order = orderRepository.findById(id)
            .orElseThrow(() -> new BusinessException("订单不存在: " + id));
        
        order.setDeleted(true);
        order.setUpdatedAt(LocalDateTime.now());
        orderRepository.save(order);
        
        // 清除缓存
        clearOrderCache(order);
        
        log.info("Order deleted successfully: {}", id);
    }

    @Override
    public Long countTodayOrdersByStoreId(Long storeId) {
        return orderRepository.countTodayOrdersByStoreId(storeId);
    }

    @Override
    public Double sumTodaySalesByStoreId(Long storeId) {
        return orderRepository.sumTodaySalesByStoreId(storeId);
    }

    @Override
    public void syncOrders() {
        log.info("Starting order synchronization");
        
        // 获取最后同步时间（这里简化处理，实际应该从配置或数据库获取）
        LocalDateTime lastSyncTime = LocalDateTime.now().minusHours(1);
        
        List<Order> ordersToSync = orderRepository.findOrdersForSync(lastSyncTime, PageRequest.of(0, 100));
        
        log.info("Found {} orders to sync", ordersToSync.size());
        
        // 这里可以添加实际的同步逻辑，比如调用外部API
        // 目前只是更新缓存
        for (Order order : ordersToSync) {
            OrderDTO orderDTO = convertToDTO(order);
            cacheOrder(orderDTO);
        }
        
        log.info("Order synchronization completed");
    }

    @Override
    public OrderDTO updateOrderStatus(Long id, Order.OrderStatus status) {
        log.info("Updating order status: {} to {}", id, status);
        
        Order order = orderRepository.findById(id)
            .orElseThrow(() -> new BusinessException("订单不存在: " + id));
        
        order.setStatus(status);
        order.setUpdatedAt(LocalDateTime.now());
        
        if (status == Order.OrderStatus.PAID) {
            order.setPaymentTime(LocalDateTime.now());
        } else if (status == Order.OrderStatus.COMPLETED) {
            order.setCompleteTime(LocalDateTime.now());
        }
        
        Order savedOrder = orderRepository.save(order);
        OrderDTO result = convertToDTO(savedOrder);
        
        // 更新缓存
        cacheOrder(result);
        
        log.info("Order status updated successfully: {} to {}", id, status);
        return result;
    }

    @Override
    public OrderDTO payOrder(Long id, String paymentMethod) {
        log.info("Processing payment for order: {} with method: {}", id, paymentMethod);
        
        Order order = orderRepository.findById(id)
            .orElseThrow(() -> new BusinessException("订单不存在: " + id));
        
        if (order.getStatus() != Order.OrderStatus.PENDING) {
            throw new BusinessException("订单状态不允许支付: " + order.getStatus());
        }
        
        order.setStatus(Order.OrderStatus.PAID);
        order.setPaymentMethod(paymentMethod);
        order.setPaymentTime(LocalDateTime.now());
        order.setUpdatedAt(LocalDateTime.now());
        
        Order savedOrder = orderRepository.save(order);
        OrderDTO result = convertToDTO(savedOrder);
        
        // 更新缓存
        cacheOrder(result);
        
        log.info("Order payment processed successfully: {}", id);
        return result;
    }

    @Override
    public OrderDTO completeOrder(Long id) {
        return updateOrderStatus(id, Order.OrderStatus.COMPLETED);
    }

    @Override
    public OrderDTO cancelOrder(Long id) {
        return updateOrderStatus(id, Order.OrderStatus.CANCELLED);
    }

    private OrderDTO convertToDTO(Order order) {
        OrderDTO dto = new OrderDTO();
        BeanUtils.copyProperties(order, dto);
        
        if (order.getOrderItems() != null) {
            List<OrderItemDTO> itemDTOs = order.getOrderItems().stream()
                .map(this::convertItemToDTO)
                .collect(Collectors.toList());
            dto.setOrderItems(itemDTOs);
        }
        
        return dto;
    }

    private OrderItemDTO convertItemToDTO(OrderItem item) {
        OrderItemDTO dto = new OrderItemDTO();
        BeanUtils.copyProperties(item, dto);
        if (item.getOrder() != null) {
            dto.setOrderId(item.getOrder().getId());
        }
        return dto;
    }

    private Order convertToEntity(OrderDTO dto) {
        Order order = new Order();
        BeanUtils.copyProperties(dto, order, "orderItems");
        
        if (dto.getOrderItems() != null) {
            List<OrderItem> items = dto.getOrderItems().stream()
                .map(itemDTO -> convertItemToEntity(itemDTO, order))
                .collect(Collectors.toList());
            order.setOrderItems(items);
        }
        
        return order;
    }

    private OrderItem convertItemToEntity(OrderItemDTO dto, Order order) {
        OrderItem item = new OrderItem();
        BeanUtils.copyProperties(dto, item, "orderId");
        item.setOrder(order);
        return item;
    }

    private void cacheOrder(OrderDTO order) {
        String cacheKey = CACHE_KEY_PREFIX + order.getId();
        String orderNoCacheKey = CACHE_KEY_ORDER_NO_PREFIX + order.getOrderNo();
        
        cacheService.set(cacheKey, order, CACHE_TTL);
        cacheService.set(orderNoCacheKey, order, CACHE_TTL);
    }

    private void clearOrderCache(Order order) {
        String cacheKey = CACHE_KEY_PREFIX + order.getId();
        String orderNoCacheKey = CACHE_KEY_ORDER_NO_PREFIX + order.getOrderNo();
        
        cacheService.delete(cacheKey);
        cacheService.delete(orderNoCacheKey);
    }
}
