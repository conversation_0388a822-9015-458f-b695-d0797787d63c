package com.rtpos.server.service.impl;

import com.rtpos.server.entity.SyncStatus;
import com.rtpos.server.repository.SyncStatusRepository;
import com.rtpos.server.service.SyncStatusService;
import com.rtpos.server.util.SyncTypeConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 同步状态管理服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SyncStatusServiceImpl implements SyncStatusService {

    private final SyncStatusRepository syncStatusRepository;

    @Override
    @Transactional
    public void recordSyncStart(String syncType, String storeId, Long startTime, Long endTime) {
        log.debug("Recording sync start: type={}, store={}, timeRange={}-{}", 
                syncType, storeId, startTime, endTime);
        
        SyncStatus syncStatus = new SyncStatus();
        syncStatus.setSyncType(syncType);
        syncStatus.setStoreId(storeId);
        syncStatus.setStartTime(startTime);
        syncStatus.setEndTime(endTime);
        syncStatus.setSyncTime(LocalDateTime.now());
        syncStatus.setStatus(SyncStatus.Status.RUNNING.name());
        
        syncStatusRepository.save(syncStatus);
    }

    @Override
    @Transactional
    public void recordSyncSuccess(String syncType, String storeId, int recordCount) {
        log.debug("Recording sync success: type={}, store={}, records={}", 
                syncType, storeId, recordCount);
        
        Optional<SyncStatus> latestSync = syncStatusRepository
                .findLatestByTypeAndStore(syncType, storeId);
        
        if (latestSync.isPresent()) {
            SyncStatus syncStatus = latestSync.get();
            syncStatus.setStatus(SyncStatus.Status.SUCCESS.name());
            syncStatus.setRecordCount(recordCount);
            
            // 计算耗时
            if (syncStatus.getSyncTime() != null) {
                long durationMs = System.currentTimeMillis() - 
                    syncStatus.getSyncTime().atZone(java.time.ZoneId.systemDefault())
                            .toInstant().toEpochMilli();
                syncStatus.setDurationMs(durationMs);
            }
            
            syncStatusRepository.save(syncStatus);
        }
    }

    @Override
    @Transactional
    public void recordSyncFailure(String syncType, String storeId, String errorMessage) {
        log.debug("Recording sync failure: type={}, store={}, error={}", 
                syncType, storeId, errorMessage);
        
        Optional<SyncStatus> latestSync = syncStatusRepository
                .findLatestByTypeAndStore(syncType, storeId);
        
        if (latestSync.isPresent()) {
            SyncStatus syncStatus = latestSync.get();
            syncStatus.setStatus(SyncStatus.Status.FAILURE.name());
            syncStatus.setErrorMessage(errorMessage);
            
            // 计算耗时
            if (syncStatus.getSyncTime() != null) {
                long durationMs = System.currentTimeMillis() - 
                    syncStatus.getSyncTime().atZone(java.time.ZoneId.systemDefault())
                            .toInstant().toEpochMilli();
                syncStatus.setDurationMs(durationMs);
            }
            
            syncStatusRepository.save(syncStatus);
        }
    }

    @Override
    public Long getLastSyncTime(String syncType, String storeId) {
        Optional<SyncStatus> lastSuccessSync = syncStatusRepository
                .findLastSuccessfulSync(syncType, storeId);
        
        if (lastSuccessSync.isPresent()) {
            return lastSuccessSync.get().getEndTime();
        }
        
        return null;
    }

    @Override
    public Map<String, Object> getSyncStatistics(String syncType) {
        Map<String, Object> stats = new HashMap<>();
        
        // 总同步次数
        long totalSyncs = syncStatusRepository.countByType(syncType);
        stats.put("totalSyncs", totalSyncs);
        
        // 成功次数
        long successSyncs = syncStatusRepository.countByTypeAndStatus(
                syncType, SyncStatus.Status.SUCCESS.name());
        stats.put("successSyncs", successSyncs);
        
        // 失败次数
        long failureSyncs = syncStatusRepository.countByTypeAndStatus(
                syncType, SyncStatus.Status.FAILURE.name());
        stats.put("failureSyncs", failureSyncs);
        
        // 成功率
        double successRate = totalSyncs > 0 ? (double) successSyncs / totalSyncs * 100 : 0;
        stats.put("successRate", Math.round(successRate * 100.0) / 100.0);
        
        // 最后同步时间
        Optional<SyncStatus> lastSync = syncStatusRepository.findLatestByType(syncType);
        if (lastSync.isPresent()) {
            stats.put("lastSyncTime", lastSync.get().getSyncTime());
            stats.put("lastSyncStatus", lastSync.get().getStatus());
        }
        
        // 平均耗时
        Double avgDuration = syncStatusRepository.getAverageDurationByType(syncType);
        stats.put("averageDurationMs", avgDuration != null ? avgDuration.longValue() : 0);
        
        return stats;
    }

    @Override
    public boolean needsSync(String storeId, Long lastModifiedTime) {
        return needsSync(storeId, lastModifiedTime, SyncTypeConstants.Order.INCREMENTAL);
    }

    @Override
    public boolean needsSync(String storeId, Long lastModifiedTime, String syncType) {
        if (lastModifiedTime == null) {
            return true;
        }

        Long lastSyncTime = getLastSyncTime(syncType, storeId);
        return lastSyncTime == null || lastModifiedTime > lastSyncTime;
    }

    @Override
    @Transactional
    public void cleanupExpiredRecords(int daysToKeep) {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(daysToKeep);
        int deletedCount = syncStatusRepository.deleteByCreatedAtBefore(cutoffDate);
        log.info("Cleaned up {} expired sync status records older than {} days", 
                deletedCount, daysToKeep);
    }
}
