package com.rtpos.server.service;

import com.rtpos.server.dto.PosOrderDTO;
import com.rtpos.server.dto.PosOrderQueryRequest;
import org.springframework.data.domain.Page;

import java.time.LocalDateTime;
import java.util.List;

/**
 * POS订单服务接口
 *
 * <AUTHOR>
 */
public interface PosOrderService {

    /**
     * 保存POS订单
     */
    PosOrderDTO savePosOrder(PosOrderDTO posOrderDTO);

    /**
     * 批量保存POS订单
     */
    List<PosOrderDTO> savePosOrders(List<PosOrderDTO> posOrderDTOs);

    /**
     * 根据ID查找POS订单
     */
    PosOrderDTO findById(Long id);

    /**
     * 根据业务订单ID查找POS订单
     */
    PosOrderDTO findByBizOrderId(Long bizOrderId);

    /**
     * 根据外部订单ID查找POS订单
     */
    PosOrderDTO findByOutOrderId(String outOrderId);

    /**
     * 分页查询POS订单
     */
    Page<PosOrderDTO> findPosOrders(PosOrderQueryRequest request);

    /**
     * 根据门店ID分页查询POS订单
     */
    Page<PosOrderDTO> findByStoreId(String storeId, int page, int size);

    /**
     * 根据门店ID和时间范围查询POS订单
     */
    Page<PosOrderDTO> findByStoreIdAndTimeRange(String storeId, Long startTime, Long endTime, int page, int size);

    /**
     * 根据门店ID和时间范围查询POS订单（使用LocalDateTime）
     */
    Page<PosOrderDTO> findByStoreIdAndTimeRange(String storeId, LocalDateTime startTime, LocalDateTime endTime, int page, int size);

    /**
     * 统计门店订单数量
     */
    Long countByStoreId(String storeId);

    /**
     * 统计门店指定时间范围内的订单数量
     */
    Long countByStoreIdAndTimeRange(String storeId, Long startTime, Long endTime);

    /**
     * 统计门店指定时间范围内的销售额
     */
    Long sumSalesByStoreIdAndTimeRange(String storeId, Long startTime, Long endTime);

    /**
     * 更新POS订单
     */
    PosOrderDTO updatePosOrder(PosOrderDTO posOrderDTO);

    /**
     * 删除POS订单（软删除）
     */
    void deletePosOrder(Long id);

    /**
     * 检查订单是否已存在
     */
    boolean existsByBizOrderId(Long bizOrderId);

    /**
     * 获取最新的订单（用于增量同步）
     */
    List<PosOrderDTO> getLatestOrders(int limit);

    /**
     * 根据修改时间获取需要同步的订单
     */
    List<PosOrderDTO> getOrdersForSync(Long lastSyncTime, int limit);
}
