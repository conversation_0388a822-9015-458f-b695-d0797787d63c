package com.rtpos.server.service;

import com.rtpos.server.dto.PosOperationLogQueryRequest;
import com.rtpos.server.dto.PosOperationLogQueryResponse;

/**
 * POS收银操作日志数据同步服务接口
 *
 * <AUTHOR>
 */
public interface PosOperationLogSyncService {

    /**
     * 从外部接口同步POS收银操作日志数据
     */
    PosOperationLogQueryResponse syncOperationLogsFromApi(PosOperationLogQueryRequest request);

    /**
     * 同步指定门店和POS机的操作日志数据
     */
    void syncOperationLogsByStoreAndPos(Integer storeNo, Integer posNo, Long startTime, Long endTime);

    /**
     * 增量同步POS收银操作日志数据
     */
    void incrementalSyncOperationLogs();

    /**
     * 全量同步POS收银操作日志数据
     */
    void fullSyncOperationLogs(Integer storeNo, Integer posNo, Long startTime, Long endTime);

    /**
     * 检查并同步缺失的操作日志数据
     */
    void checkAndSyncMissingOperationLogs();

    /**
     * 批量同步多个门店和POS机的操作日志
     */
    void batchSyncOperationLogs(String storeNos, String posNos, Long startTime, Long endTime);
}
