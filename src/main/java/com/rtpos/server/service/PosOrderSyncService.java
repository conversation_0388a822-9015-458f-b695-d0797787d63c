package com.rtpos.server.service;

import com.rtpos.server.dto.PosOrderQueryRequest;
import com.rtpos.server.dto.PosOrderQueryResponse;

/**
 * POS订单数据同步服务接口
 *
 * <AUTHOR>
 */
public interface PosOrderSyncService {

    /**
     * 从外部接口同步POS订单数据
     */
    PosOrderQueryResponse syncPosOrdersFromApi(PosOrderQueryRequest request);

    /**
     * 同步指定门店的POS订单数据
     */
    void syncPosOrdersByStore(String storeId, Long startTime, Long endTime);

    /**
     * 增量同步POS订单数据
     */
    void incrementalSyncPosOrders();

    /**
     * 全量同步POS订单数据
     */
    void fullSyncPosOrders(String storeId, Long startTime, Long endTime);

    /**
     * 检查并同步缺失的订单数据
     */
    void checkAndSyncMissingOrders();
}
