package com.rtpos.server.service;

import com.rtpos.server.entity.PosStore;
import lombok.Data;

import java.util.List;

/**
 * 智能同步策略服务接口
 * 根据门店规模、历史数据量、系统负载等因素动态调整同步策略
 *
 * <AUTHOR>
 */
public interface SmartSyncStrategyService {

    /**
     * 获取门店同步策略
     */
    SyncStrategy getSyncStrategy(PosStore store);

    /**
     * 获取批量同步策略
     */
    BatchSyncStrategy getBatchSyncStrategy(List<PosStore> stores);

    /**
     * 根据系统负载调整策略
     */
    void adjustStrategyByLoad();

    /**
     * 同步策略配置
     */
    @Data
    class SyncStrategy {
        private String storeId;
        private int priority;           // 优先级：1-高，2-中，3-低
        private int timeSliceHours;     // 时间分片大小
        private int batchSize;          // 批次大小
        private int maxRetries;         // 最大重试次数
        private long delayBetweenBatches; // 批次间延迟（毫秒）
        private boolean enableParallel; // 是否启用并行处理
    }

    /**
     * 批量同步策略配置
     */
    @Data
    class BatchSyncStrategy {
        private int batchStoreSize;     // 每批次门店数量
        private int maxConcurrentStores; // 最大并发门店数
        private int timeSliceHours;     // 时间分片大小
        private long delayBetweenBatches; // 批次间延迟
        private List<String> priorityStores; // 优先处理的门店
    }
}
