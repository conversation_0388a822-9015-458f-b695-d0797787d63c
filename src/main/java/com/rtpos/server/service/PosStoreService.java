package com.rtpos.server.service;

import com.rtpos.server.dto.PosStoreQueryResponse;
import com.rtpos.server.entity.PosStore;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * POS门店信息服务接口
 *
 * <AUTHOR>
 */
public interface PosStoreService {

    /**
     * 从外部API获取门店列表并同步到本地数据库
     */
    PosStoreQueryResponse syncStoresFromApi();

    /**
     * 获取所有门店列表（分页）
     */
    Page<PosStore> getAllStores(Pageable pageable);

    /**
     * 根据门店ID查询门店信息
     */
    Optional<PosStore> getStoreById(String storeId);

    /**
     * 根据门店名称模糊查询
     */
    List<PosStore> searchStoresByName(String storeName, Pageable pageable);

    /**
     * 根据大区查询门店列表
     */
    List<PosStore> getStoresByRegion(String pgSeq, Pageable pageable);

    /**
     * 根据子区域查询门店列表
     */
    List<PosStore> getStoresBySubArea(String subId, Pageable pageable);

    /**
     * 获取所有大区列表
     */
    List<Map<String, String>> getAllRegions();

    /**
     * 获取指定大区下的所有子区域列表
     */
    List<Map<String, String>> getSubAreasByRegion(String pgSeq);

    /**
     * 统计门店总数
     */
    Long getTotalStoreCount();

    /**
     * 统计各大区门店数量
     */
    List<Map<String, Object>> getStoreCountByRegion();

    /**
     * 保存门店信息
     */
    PosStore saveStore(PosStore store);

    /**
     * 批量保存门店信息
     */
    List<PosStore> saveStores(List<PosStore> stores);

    /**
     * 删除门店信息（软删除）
     */
    void deleteStore(String storeId);

    /**
     * 检查门店是否存在
     */
    boolean existsStore(String storeId);
}
