package com.rtpos.server.service;

import com.rtpos.server.dto.auth.*;

import java.util.List;

/**
 * 用户认证服务接口
 *
 * <AUTHOR>
 */
public interface UserAuthService {

    /**
     * 发送验证码
     *
     * @param request 发送验证码请求
     * @return 发送结果
     */
    AuthResponse<Object> sendCode(SendCodeRequest request);

    /**
     * 验证码登录
     *
     * @param request 登录请求
     * @return 登录结果
     */
    AuthResponse<LoginData> login(LoginRequest request);

    /**
     * 查询员工部门角色信息
     *
     * @param request 查询请求
     * @return 员工信息
     */
    AuthResponse<List<EmpDeptRole>> queryEmpDeptRole(QueryEmpRequest request);

    /**
     * 用户登出
     *
     * @param request 登出请求
     * @return 登出结果
     */
    POSAuthResponse<Object> logout(LogoutRequest request);

    /**
     * 验证token是否有效
     *
     * @param token 用户token
     * @return 验证结果
     */
    POSAuthResponse<String> validateToken(String token);
}
