package com.rtpos.server.service;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 同步状态管理服务接口
 *
 * <AUTHOR>
 */
public interface SyncStatusService {

    /**
     * 记录同步开始
     */
    void recordSyncStart(String syncType, String storeId, Long startTime, Long endTime);

    /**
     * 记录同步成功
     */
    void recordSyncSuccess(String syncType, String storeId, int recordCount);

    /**
     * 记录同步失败
     */
    void recordSyncFailure(String syncType, String storeId, String errorMessage);

    /**
     * 获取最后同步时间
     */
    Long getLastSyncTime(String syncType, String storeId);

    /**
     * 获取同步统计信息
     */
    Map<String, Object> getSyncStatistics(String syncType);

    /**
     * 检查是否需要同步（默认使用订单增量同步类型）
     */
    boolean needsSync(String storeId, Long lastModifiedTime);

    /**
     * 检查是否需要同步（指定同步类型）
     */
    boolean needsSync(String storeId, Long lastModifiedTime, String syncType);

    /**
     * 清理过期的同步记录
     */
    void cleanupExpiredRecords(int daysToKeep);
}
