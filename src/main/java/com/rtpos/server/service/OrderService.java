package com.rtpos.server.service;

import com.rtpos.server.dto.OrderDTO;
import com.rtpos.server.entity.Order;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单服务接口
 * 
 * <AUTHOR>
 */
public interface OrderService {

    /**
     * 创建订单
     */
    OrderDTO createOrder(OrderDTO orderDTO);

    /**
     * 更新订单
     */
    OrderDTO updateOrder(Long id, OrderDTO orderDTO);

    /**
     * 根据ID查找订单
     */
    OrderDTO findById(Long id);

    /**
     * 根据订单号查找订单
     */
    OrderDTO findByOrderNo(String orderNo);

    /**
     * 分页查询订单
     */
    Page<OrderDTO> findAll(Pageable pageable);

    /**
     * 根据门店ID分页查询订单
     */
    Page<OrderDTO> findByStoreId(Long storeId, Pageable pageable);

    /**
     * 根据状态分页查询订单
     */
    Page<OrderDTO> findByStatus(Order.OrderStatus status, Pageable pageable);

    /**
     * 根据时间范围查询订单
     */
    List<OrderDTO> findByOrderTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据门店ID和时间范围查询订单
     */
    List<OrderDTO> findByStoreIdAndOrderTimeBetween(Long storeId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 删除订单（软删除）
     */
    void deleteOrder(Long id);

    /**
     * 统计门店今日订单数量
     */
    Long countTodayOrdersByStoreId(Long storeId);

    /**
     * 统计门店今日销售额
     */
    Double sumTodaySalesByStoreId(Long storeId);

    /**
     * 同步订单数据
     */
    void syncOrders();

    /**
     * 更新订单状态
     */
    OrderDTO updateOrderStatus(Long id, Order.OrderStatus status);

    /**
     * 支付订单
     */
    OrderDTO payOrder(Long id, String paymentMethod);

    /**
     * 完成订单
     */
    OrderDTO completeOrder(Long id);

    /**
     * 取消订单
     */
    OrderDTO cancelOrder(Long id);
}
