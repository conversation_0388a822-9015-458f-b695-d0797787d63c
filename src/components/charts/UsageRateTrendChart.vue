<template>
  <div class="chart-container" ref="chartContainer"></div>
</template>

<script setup>
import { ref, onMounted, watch, onBeforeUnmount } from "vue";
import * as echarts from "echarts";

const props = defineProps({
  chartData: {
    type: Array,
    required: true,
  },
});

const chartContainer = ref(null);
let chartInstance = null;

const initChart = () => {
  if (!chartInstance) {
    chartInstance = echarts.init(chartContainer.value);
  }

  const options = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "line",
      },
    },
    legend: {
      data: ["人工POS", "自助POS"],
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: props.chartData.map((item) => item.date),
    },
    yAxis: {
      type: "value",
      name: "使用率(%)",
      min: 30,
      max: 60,
    },
    series: [
      {
        name: "人工POS",
        type: "line",
        data: props.chartData.map((item) => item.manual),
        smooth: true,
        lineStyle: {
          width: 3,
        },
        itemStyle: {
          color: "#409EFF",
        },
        symbolSize: 8,
      },
      {
        name: "自助POS",
        type: "line",
        data: props.chartData.map((item) => item.selfService),
        smooth: true,
        lineStyle: {
          width: 3,
        },
        itemStyle: {
          color: "#67C23A",
        },
        symbolSize: 8,
      },
    ],
  };

  chartInstance.setOption(options);
};

const resizeChart = () => {
  chartInstance && chartInstance.resize();
};

watch(
  () => props.chartData,
  () => {
    initChart();
  },
  { deep: true }
);

onMounted(() => {
  initChart();
  window.addEventListener("resize", resizeChart);
});

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  window.removeEventListener("resize", resizeChart);
});
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
