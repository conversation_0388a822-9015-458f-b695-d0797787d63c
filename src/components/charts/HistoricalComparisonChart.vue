<template>
  <div class="chart-container" ref="chartContainer"></div>
</template>

<script setup>
import { ref, onMounted, watch, onBeforeUnmount } from "vue";
import * as echarts from "echarts";

const props = defineProps({
  chartData: {
    type: Array,
    required: true,
  },
});

const chartContainer = ref(null);
let chartInstance = null;

const initChart = () => {
  if (!chartInstance) {
    chartInstance = echarts.init(chartContainer.value);
  }

  const options = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: "#6a7985",
        },
      },
    },
    legend: {
      data: ["今日", "上周同期", "平均"],
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: props.chartData.map((item) => item.hour),
    },
    yAxis: {
      type: "value",
      name: "订单数量",
    },
    series: [
      {
        name: "今日",
        type: "line",
        stack: "Total",
        emphasis: {
          focus: "series",
        },
        data: props.chartData.map((item) => item.today),
        itemStyle: {
          color: "#409EFF",
        },
        lineStyle: {
          width: 3,
        },
        symbolSize: 8,
      },
      {
        name: "上周同期",
        type: "line",
        stack: "Total",
        emphasis: {
          focus: "series",
        },
        data: props.chartData.map((item) => item.lastWeek),
        itemStyle: {
          color: "#E6A23C",
        },
        lineStyle: {
          width: 3,
          type: "dashed",
        },
        symbolSize: 8,
      },
      {
        name: "平均",
        type: "line",
        stack: "Total",
        emphasis: {
          focus: "series",
        },
        data: props.chartData.map((item) => item.avg),
        itemStyle: {
          color: "#909399",
        },
        lineStyle: {
          width: 2,
          type: "dotted",
        },
        symbolSize: 8,
      },
    ],
  };

  chartInstance.setOption(options);
};

const resizeChart = () => {
  chartInstance && chartInstance.resize();
};

watch(
  () => props.chartData,
  () => {
    initChart();
  },
  { deep: true }
);

onMounted(() => {
  initChart();
  window.addEventListener("resize", resizeChart);
});

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  window.removeEventListener("resize", resizeChart);
});
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
