<template>
  <div class="chart-container" ref="chartContainer"></div>
</template>

<script setup>
import { ref, onMounted, watch, onBeforeUnmount } from "vue";
import * as echarts from "echarts";

const props = defineProps({
  chartData: {
    type: Array,
    required: true,
  },
  chartType: {
    type: String,
    default: "total", // 'total' 或 'comparison'
  },
});

const chartContainer = ref(null);
let chartInstance = null;

const initChart = () => {
  if (!chartInstance) {
    chartInstance = echarts.init(chartContainer.value);
  }

  let options = {};

  if (props.chartType === "total") {
    // 总量视图
    options = {
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "shadow",
        },
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        containLabel: true,
      },
      xAxis: {
        type: "category",
        data: props.chartData.map((item) => item.hour),
      },
      yAxis: {
        type: "value",
        name: "订单数",
      },
      series: [
        {
          name: "总订单量",
          type: "line",
          smooth: true,
          data: props.chartData.map((item) => item.total),
          symbol: "circle",
          symbolSize: 8,
          itemStyle: {
            color: "#409EFF",
          },
          lineStyle: {
            width: 3,
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "rgba(64, 158, 255, 0.3)" },
              { offset: 1, color: "rgba(64, 158, 255, 0.1)" },
            ]),
          },
        },
      ],
    };
  } else {
    // 分类对比视图
    options = {
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "shadow",
        },
      },
      legend: {
        data: ["人工POS", "自助POS", "取消订单"],
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        containLabel: true,
      },
      xAxis: {
        type: "category",
        data: props.chartData.map((item) => item.hour),
      },
      yAxis: {
        type: "value",
        name: "订单数",
      },
      series: [
        {
          name: "人工POS",
          type: "bar",
          stack: "total",
          emphasis: {
            focus: "series",
          },
          data: props.chartData.map((item) => item.manual),
          itemStyle: {
            color: "#409EFF",
          },
        },
        {
          name: "自助POS",
          type: "bar",
          stack: "total",
          emphasis: {
            focus: "series",
          },
          data: props.chartData.map((item) => item.selfService),
          itemStyle: {
            color: "#67C23A",
          },
        },
        {
          name: "取消订单",
          type: "bar",
          data: props.chartData.map((item) => item.canceled),
          itemStyle: {
            color: "#F56C6C",
          },
        },
      ],
    };
  }

  chartInstance.setOption(options);
};

const resizeChart = () => {
  chartInstance && chartInstance.resize();
};

watch(
  () => props.chartData,
  () => {
    initChart();
  },
  { deep: true }
);

watch(
  () => props.chartType,
  () => {
    initChart();
  }
);

onMounted(() => {
  initChart();
  window.addEventListener("resize", resizeChart);
});

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  window.removeEventListener("resize", resizeChart);
});
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
