<template>
  <div class="chart-container" ref="chartContainer"></div>
</template>

<script setup>
import { ref, onMounted, watch, onBeforeUnmount } from "vue";
import * as echarts from "echarts";

const props = defineProps({
  chartData: {
    type: Array,
    required: true,
  },
});

const chartContainer = ref(null);
let chartInstance = null;

const initChart = () => {
  if (!chartInstance) {
    chartInstance = echarts.init(chartContainer.value);
  }

  const options = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      formatter: function (params) {
        return `${params[0].name}<br/>${
          params[0].seriesName
        }: ${params[0].value.toLocaleString()} 元`;
      },
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: props.chartData.map((item) => item.month),
    },
    yAxis: {
      type: "value",
      name: "节约金额 (元)",
    },
    visualMap: {
      show: false,
      pieces: [
        {
          gt: 0,
          lte: 550000,
          color: "#93CE07",
        },
        {
          gt: 550000,
          lte: 600000,
          color: "#67C23A",
        },
      ],
    },
    series: [
      {
        name: "节约金额",
        type: "bar",
        data: props.chartData.map((item) => item.saving),
        label: {
          show: true,
          position: "top",
          formatter: function (params) {
            return (params.value / 10000).toFixed(1) + "万";
          },
        },
        itemStyle: {
          borderRadius: [5, 5, 0, 0],
        },
      },
    ],
  };

  chartInstance.setOption(options);
};

const resizeChart = () => {
  chartInstance && chartInstance.resize();
};

watch(
  () => props.chartData,
  () => {
    initChart();
  },
  { deep: true }
);

onMounted(() => {
  initChart();
  window.addEventListener("resize", resizeChart);
});

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  window.removeEventListener("resize", resizeChart);
});
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
