<template>
  <div class="chart-container" ref="chartContainer"></div>
</template>

<script setup>
import { ref, onMounted, watch, onBeforeUnmount } from "vue";
import * as echarts from "echarts";

const props = defineProps({
  chartData: {
    type: Array,
    required: true,
  },
  viewType: {
    type: String,
    default: "percentage",
  },
});

const chartContainer = ref(null);
let chartInstance = null;

const initChart = () => {
  if (!chartInstance) {
    chartInstance = echarts.init(chartContainer.value);
  }

  const xAxisData = props.chartData.map((item) => item.hour);
  let seriesData = [];
  let yAxisName = "";

  if (props.viewType === "percentage") {
    // 百分比视图
    seriesData = [
      {
        name: "人工POS",
        type: "line",
        smooth: true,
        data: props.chartData.map((item) => item.manual),
        lineStyle: {
          width: 3,
        },
        itemStyle: {
          color: "#409EFF",
        },
        symbolSize: 8,
      },
      {
        name: "自助POS",
        type: "line",
        smooth: true,
        data: props.chartData.map((item) => item.selfService),
        lineStyle: {
          width: 3,
        },
        itemStyle: {
          color: "#67C23A",
        },
        symbolSize: 8,
      },
    ];
    yAxisName = "使用率 (%)";
  } else {
    // 设备数视图 - 转换成设备数
    // 假设每10%使用率对应1台设备使用
    const conversionRate = 0.1; // 每10%使用率表示1台设备使用
    seriesData = [
      {
        name: "人工POS",
        type: "bar",
        stack: "total",
        data: props.chartData.map((item) =>
          Math.round(item.manual * conversionRate)
        ),
        itemStyle: {
          color: "#409EFF",
        },
      },
      {
        name: "自助POS",
        type: "bar",
        stack: "total",
        data: props.chartData.map((item) =>
          Math.round(item.selfService * conversionRate)
        ),
        itemStyle: {
          color: "#67C23A",
        },
      },
    ];
    yAxisName = "使用设备数 (台)";
  }

  const options = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: props.viewType === "percentage" ? "line" : "shadow",
      },
    },
    legend: {
      data: ["人工POS", "自助POS"],
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: props.viewType !== "percentage",
      data: xAxisData,
    },
    yAxis: {
      type: "value",
      name: yAxisName,
    },
    series: seriesData,
  };

  chartInstance.setOption(options);
};

const resizeChart = () => {
  chartInstance && chartInstance.resize();
};

watch(
  () => props.chartData,
  () => {
    initChart();
  },
  { deep: true }
);

watch(
  () => props.viewType,
  () => {
    initChart();
  }
);

onMounted(() => {
  initChart();
  window.addEventListener("resize", resizeChart);
});

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  window.removeEventListener("resize", resizeChart);
});
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
