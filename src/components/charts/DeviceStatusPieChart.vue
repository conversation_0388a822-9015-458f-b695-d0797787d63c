<template>
  <div class="chart-container" ref="chartContainer"></div>
</template>

<script setup>
import { ref, onMounted, watch, onBeforeUnmount } from "vue";
import * as echarts from "echarts";

const props = defineProps({
  chartData: {
    type: Object,
    required: true,
  },
});

const chartContainer = ref(null);
let chartInstance = null;

const initChart = () => {
  if (!chartInstance) {
    chartInstance = echarts.init(chartContainer.value);
  }

  // 处理数据
  const manualData = [
    {
      name: "使用中",
      value: props.chartData.manual.active,
      itemStyle: { color: "#67C23A" },
    },
    {
      name: "闲置",
      value: props.chartData.manual.idle,
      itemStyle: { color: "#E6A23C" },
    },
    {
      name: "离线",
      value: props.chartData.manual.offline,
      itemStyle: { color: "#909399" },
    },
  ];

  const selfServiceData = [
    {
      name: "使用中",
      value: props.chartData.selfService.active,
      itemStyle: { color: "#67C23A" },
    },
    {
      name: "闲置",
      value: props.chartData.selfService.idle,
      itemStyle: { color: "#E6A23C" },
    },
    {
      name: "离线",
      value: props.chartData.selfService.offline,
      itemStyle: { color: "#909399" },
    },
  ];

  const options = {
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)",
    },
    legend: {
      orient: "vertical",
      left: 10,
      data: ["使用中", "闲置", "离线"],
    },
    series: [
      {
        name: "人工POS",
        type: "pie",
        radius: ["30%", "45%"],
        center: ["35%", "50%"],
        label: {
          formatter: "{b}: {c} ({d}%)",
        },
        data: manualData,
      },
      {
        name: "自助POS",
        type: "pie",
        radius: ["30%", "45%"],
        center: ["75%", "50%"],
        label: {
          formatter: "{b}: {c} ({d}%)",
        },
        data: selfServiceData,
      },
    ],
  };

  chartInstance.setOption(options);
};

const resizeChart = () => {
  chartInstance && chartInstance.resize();
};

watch(
  () => props.chartData,
  () => {
    initChart();
  },
  { deep: true }
);

onMounted(() => {
  initChart();
  window.addEventListener("resize", resizeChart);
});

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  window.removeEventListener("resize", resizeChart);
});
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
