#!/bin/bash

# 自定义端口启动脚本
# 使用方法: ./start-custom-port.sh [端口号]
# 例如: ./start-custom-port.sh 9090

PORT=${1:-8081}  # 默认端口8081，可通过参数指定

echo "=== RTPosServer 自定义端口启动 ==="
echo "启动端口: $PORT"
echo ""

# 检查端口是否被占用
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null ; then
    echo "错误: 端口 $PORT 已被占用"
    echo "请选择其他端口或停止占用该端口的进程"
    exit 1
fi

echo "正在启动应用..."
echo "访问地址:"
echo "  - API文档: http://localhost:$PORT/api/v1/swagger-ui.html"
echo "  - H2控制台: http://localhost:$PORT/api/v1/h2-console"
echo "  - 健康检查: http://localhost:$PORT/api/v1/actuator/health"
echo ""
echo "默认认证信息:"
echo "  - 用户名: admin"
echo "  - 密码: admin123"
echo ""
echo "按 Ctrl+C 停止应用"
echo "=========================="

# 使用指定端口启动应用
./mvnw spring-boot:run -Dspring-boot.run.profiles=dev -Dspring-boot.run.jvmArguments="-Dserver.port=$PORT"
