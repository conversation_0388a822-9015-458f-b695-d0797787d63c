version: '3.8'

services:
  # Redis服务
  redis:
    image: redis:7-alpine
    container_name: rtpos-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-your_redis_password}
    networks:
      - rtpos-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MySQL数据库（可选，如果不使用H2）
  mysql:
    image: mysql:8.0
    container_name: rtpos-mysql
    restart: unless-stopped
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    environment:
      - MYSQL_ROOT_PASSWORD=${DB_ROOT_PASSWORD:-root_password}
      - MYSQL_DATABASE=${DB_NAME:-rtpos_prod}
      - MYSQL_USER=${DB_USERNAME:-rtpos_user}
      - MYSQL_PASSWORD=${DB_PASSWORD:-rtpos_password}
    networks:
      - rtpos-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 3

  # RTPosEnergyServer应用
  rtpos-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: rtpos-server
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      # Spring配置
      - SPRING_PROFILES_ACTIVE=prod
      - SERVER_PORT=8081
      
      # Redis配置
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-your_redis_password}
      - REDIS_DATABASE=0
      
      # 数据库配置（如果使用MySQL）
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=${DB_NAME:-rtpos_prod}
      - DB_USERNAME=${DB_USERNAME:-rtpos_user}
      - DB_PASSWORD=${DB_PASSWORD:-rtpos_password}
      
      # JVM配置
      - JAVA_OPTS=-Xms512m -Xmx1g -XX:+UseG1GC
      
      # 外部API配置
      - POS_ORDER_API_BASE_URL=${POS_ORDER_API_BASE_URL:-http://middle-order-biz.idc1.fn}
      - POS_OPERATION_LOG_API_BASE_URL=${POS_OPERATION_LOG_API_BASE_URL:-http://rt-pos-api.idc1.fn}
      
      # 同步配置
      - POS_STORE_IDS=${POS_STORE_IDS:-1001,1002,1003}
      - POS_STORE_NOS=${POS_STORE_NOS:-1001,1002,1003}
      - POS_NOS=${POS_NOS:-895,896,897}
    
    volumes:
      - app_logs:/app/logs
    
    networks:
      - rtpos-network
    
    depends_on:
      redis:
        condition: service_healthy
      mysql:
        condition: service_healthy
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/api/v1/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: rtpos-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    networks:
      - rtpos-network
    depends_on:
      - rtpos-server

volumes:
  redis_data:
    driver: local
  mysql_data:
    driver: local
  app_logs:
    driver: local

networks:
  rtpos-network:
    driver: bridge
