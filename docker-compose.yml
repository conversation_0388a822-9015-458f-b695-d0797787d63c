version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: rtpos-mysql
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: rtpos_db
      MYSQL_USER: rtpos
      MYSQL_PASSWORD: rtpos123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - rtpos-network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: rtpos-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - rtpos-network
    restart: unless-stopped

  # RTPosServer应用
  rtpos-server:
    build: .
    container_name: rtpos-server
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_DATASOURCE_URL: *****************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: rtpos
      SPRING_DATASOURCE_PASSWORD: rtpos123
      SPRING_DATA_REDIS_HOST: redis
      SPRING_DATA_REDIS_PORT: 6379
    ports:
      - "8081:8081"
    depends_on:
      - mysql
      - redis
    networks:
      - rtpos-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/api/v1/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mysql_data:
  redis_data:

networks:
  rtpos-network:
    driver: bridge
