# RTPosServer 开发和部署流程指南

## 🎯 项目概述

RTPosServer是一个POS订单数据同步系统，支持从开发到生产的完整部署流程。本文档详细说明了什么情况下需要Docker，以及如何在不同环境中进行部署。

## 🏗️ 项目架构

### 环境分层
```
开发环境 (Development) → 测试环境 (Staging/Beta) → 生产环境 (Production)
     ↓                        ↓                         ↓
  本地开发调试              功能测试验证               正式服务运行
  beta1.fn API            beta1.fn API              idc1.fn API
  本地数据库               测试数据库                 生产数据库
```

### 部署方式对比

| 部署方式 | 适用场景 | 优势 | 劣势 |
|----------|----------|------|------|
| **本地直接运行** | 日常开发 | 启动快、资源少、调试方便 | 环境差异、依赖管理 |
| **Docker容器** | 团队协作、生产部署 | 环境一致、易扩展、隔离性好 | 资源占用、学习成本 |
| **JAR包部署** | 简单生产环境 | 部署简单、性能好 | 环境依赖、扩展性差 |

## 🚀 开发阶段流程

### 1. 环境选择

#### 方案A：本地MySQL开发（推荐）
```bash
# 适用场景：日常开发、快速调试
./start-dev-local.sh
```

**特点**：
- ✅ 启动速度快
- ✅ 资源占用少  
- ✅ 调试方便
- ✅ 数据持久化
- ❌ 需要本地安装MySQL

#### 方案B：Docker开发环境
```bash
# 适用场景：团队协作、环境一致性要求高
./start-dev.sh
```

**特点**：
- ✅ 环境完全一致
- ✅ 包含完整服务栈
- ✅ 一键启动
- ❌ 需要Docker
- ❌ 资源占用较多

#### 方案C：国内镜像源（网络问题解决方案）
```bash
# 适用场景：Docker Hub访问困难
./start-dev-cn.sh
```

**特点**：
- ✅ 解决网络访问问题
- ✅ 下载速度快
- ✅ 稳定可靠

### 2. 开发环境配置

#### 配置文件：`application-dev.yml`
- **API端点**：`beta1.fn` 域名
- **数据库**：本地MySQL或Docker MySQL
- **Redis**：本地Redis或Docker Redis
- **日志级别**：DEBUG（便于调试）
- **同步频率**：较高（便于测试）

#### 访问地址
- **API文档**：http://localhost:8081/api/v1/swagger-ui.html
- **健康检查**：http://localhost:8081/api/v1/actuator/health
- **数据库管理**：http://localhost:8083（phpMyAdmin）

### 3. 开发工作流
```
代码修改 → 自动重启 → 功能测试 → 单元测试 → 代码提交
```

## 🧪 测试阶段流程

### 测试环境特点
- **配置文件**：`application-dev.yml`
- **API域名**：`beta1.fn`
- **数据库**：独立的测试数据库
- **用途**：功能验证、集成测试

### 部署方式

#### 方式1：JAR包部署
```bash
# 设置测试环境
export SPRING_PROFILES_ACTIVE=dev
export DB_HOST=test-mysql-server
export REDIS_HOST=test-redis-server

# 启动应用
./start-prod.sh
```

#### 方式2：Docker部署
```bash
# 使用开发环境Docker配置
docker-compose -f docker-compose.dev.yml up -d

# 或使用国内镜像源
docker-compose -f docker-compose.dev-cn.yml up -d
```

## 🚀 生产阶段流程

### 生产环境特点
- **配置文件**：`application-prod.yml`
- **API域名**：`idc1.fn`
- **数据库**：生产MySQL集群
- **Redis**：生产Redis集群
- **监控**：完整的监控和告警系统

### 部署方式选择

#### 🎯 什么情况下需要Docker？

##### ✅ **需要Docker的场景**：

1. **生产环境部署**
   - 多服务容器编排
   - 自动重启和健康检查
   - 资源限制和隔离
   - 负载均衡和服务发现

2. **团队协作开发**
   - 统一开发环境
   - 避免"在我机器上能跑"问题
   - 快速环境搭建

3. **CI/CD流水线**
   - 自动化构建和部署
   - 环境一致性保证
   - 容器化交付

4. **微服务架构**
   - 服务间隔离
   - 独立扩缩容
   - 版本管理

##### ❌ **不需要Docker的场景**：

1. **本地快速开发**
   - 调试方便
   - 启动速度快
   - 资源占用少

2. **简单单机部署**
   - 服务器已有基础服务
   - 不需要容器化复杂性
   - 传统运维模式

### 生产部署方案

#### 方案1：JAR包部署（简单场景）
```bash
# 设置生产环境变量
export SPRING_PROFILES_ACTIVE=prod
export DB_HOST=prod-mysql-cluster
export REDIS_HOST=prod-redis-cluster
export REDIS_PASSWORD=your_redis_password

# 启动生产应用
./start-prod.sh
```

**适用场景**：
- 单机或少量服务器
- 传统运维环境
- 简单的部署需求

#### 方案2：Docker容器部署（推荐）
```bash
# 设置环境变量
cp .env.example .env
# 编辑 .env 文件配置生产参数

# 启动生产环境
docker-compose -f docker-compose.prod.yml up -d
```

**适用场景**：
- 生产环境标准部署
- 需要服务编排
- 高可用要求

#### 方案3：Kubernetes部署（大规模）
```bash
# 构建镜像
docker build -t rtpos-server:latest .

# 部署到K8s集群
kubectl apply -f k8s/
```

**适用场景**：
- 大规模集群部署
- 自动扩缩容需求
- 云原生架构

## 📊 环境配置对比表

| 环境 | 配置文件 | API域名 | 数据库 | 部署方式 | 启动脚本 | 用途 |
|------|----------|---------|--------|----------|----------|------|
| **本地开发** | `application-dev.yml` | `beta1.fn` | 本地MySQL | 直接运行 | `start-dev-local.sh` | 日常开发调试 |
| **Docker开发** | `application-dev.yml` | `beta1.fn` | Docker MySQL | Docker | `start-dev.sh` | 团队协作开发 |
| **测试环境** | `application-dev.yml` | `beta1.fn` | 测试MySQL | JAR/Docker | `start-prod.sh` | 功能测试验证 |
| **生产环境** | `application-prod.yml` | `idc1.fn` | 生产MySQL | Docker/K8s | `docker-compose.prod.yml` | 正式服务运行 |

## 🔄 完整CI/CD流程建议

### 1. 开发阶段
```
本地开发 → 代码提交 → 单元测试 → 代码审查
```

### 2. 构建阶段
```
代码拉取 → Maven构建 → Docker镜像构建 → 镜像推送
```

### 3. 测试阶段
```
测试环境部署 → 自动化测试 → 性能测试 → 安全测试
```

### 4. 生产阶段
```
生产环境部署 → 健康检查 → 监控告警 → 日志收集
```

## 💡 最佳实践建议

### 🔧 开发阶段
- **首选本地开发**：使用 `start-dev-local.sh`，快速高效
- **团队协作**：使用 `docker-compose.dev.yml`，环境一致
- **网络问题**：使用 `start-dev-cn.sh`，国内镜像源

### 🚀 部署阶段
- **小型项目**：JAR包部署，简单直接
- **中型项目**：Docker Compose，容器化管理
- **大型项目**：Kubernetes，云原生架构

### 📈 运维阶段
- **监控**：Spring Boot Actuator + Prometheus
- **日志**：ELK Stack 或云日志服务
- **备份**：定期数据库备份和恢复测试
- **扩容**：基于监控指标的自动扩缩容

## 🛠️ 常用命令速查

### 开发环境
```bash
# 本地MySQL开发
./start-dev-local.sh

# Docker开发环境
./start-dev.sh

# 国内镜像源
./start-dev-cn.sh

# 环境检查
./check-local-env.sh
./check-dev-env.sh
```

### 生产环境
```bash
# JAR包部署
./start-prod.sh

# Docker部署
docker-compose -f docker-compose.prod.yml up -d

# 查看日志
docker-compose -f docker-compose.prod.yml logs -f

# 停止服务
docker-compose -f docker-compose.prod.yml down
```

### 数据库管理
```bash
# 本地数据库管理
./db-manage-local.sh

# Docker数据库管理
./db-manage.sh
```

## 🔍 故障排除

### 常见问题
1. **Docker网络问题**：使用国内镜像源
2. **端口冲突**：检查端口占用情况
3. **数据库连接失败**：检查服务状态和配置
4. **内存不足**：调整JVM参数

### 日志查看
```bash
# 应用日志
tail -f logs/rtpos-server.log

# Docker日志
docker logs rtpos-server

# 系统资源
docker stats
```

---

通过本指南，您可以根据不同的场景和需求选择合适的开发和部署方式，确保项目在各个阶段都能稳定高效地运行。
