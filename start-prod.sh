#!/bin/bash

# 生产环境启动脚本
# 用于启动RTPosEnergyServer生产环境

echo "=== RTPosEnergyServer 生产环境启动脚本 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查Java环境
check_java() {
    if ! command -v java &> /dev/null; then
        echo -e "${RED}❌ Java未安装，请先安装Java 17或更高版本${NC}"
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | head -n1 | cut -d'"' -f2 | cut -d'.' -f1)
    if [ "$JAVA_VERSION" -lt 17 ]; then
        echo -e "${RED}❌ Java版本过低，需要Java 17或更高版本${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Java环境检查通过: $(java -version 2>&1 | head -n1)${NC}"
}

# 检查Redis连接
check_redis() {
    echo -e "${BLUE}🔍 检查Redis连接...${NC}"
    
    REDIS_HOST=${REDIS_HOST:-localhost}
    REDIS_PORT=${REDIS_PORT:-6379}
    REDIS_PASSWORD=${REDIS_PASSWORD:-}
    
    if command -v redis-cli &> /dev/null; then
        if [ -n "$REDIS_PASSWORD" ]; then
            REDIS_RESPONSE=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" ping 2>/dev/null)
        else
            REDIS_RESPONSE=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" ping 2>/dev/null)
        fi
        
        if [ "$REDIS_RESPONSE" = "PONG" ]; then
            echo -e "${GREEN}✅ Redis连接正常: $REDIS_HOST:$REDIS_PORT${NC}"
        else
            echo -e "${YELLOW}⚠️  Redis连接失败，应用将在没有缓存的情况下运行${NC}"
            echo -e "${YELLOW}   请检查Redis服务是否启动: $REDIS_HOST:$REDIS_PORT${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  redis-cli未安装，跳过Redis连接检查${NC}"
    fi
}

# 设置环境变量
setup_environment() {
    echo -e "${BLUE}🔧 设置生产环境变量...${NC}"
    
    # 设置默认环境变量（如果未设置）
    export SPRING_PROFILES_ACTIVE=${SPRING_PROFILES_ACTIVE:-prod}
    export SERVER_PORT=${SERVER_PORT:-8081}
    
    # Redis配置
    export REDIS_HOST=${REDIS_HOST:-localhost}
    export REDIS_PORT=${REDIS_PORT:-6379}
    export REDIS_PASSWORD=${REDIS_PASSWORD:-}
    export REDIS_DATABASE=${REDIS_DATABASE:-0}
    
    # 数据库配置（如果使用MySQL）
    export DB_HOST=${DB_HOST:-localhost}
    export DB_PORT=${DB_PORT:-3306}
    export DB_NAME=${DB_NAME:-rtpos_prod}
    export DB_USERNAME=${DB_USERNAME:-root}
    export DB_PASSWORD=${DB_PASSWORD:-}
    
    # JVM参数
    export JAVA_OPTS=${JAVA_OPTS:-"-Xms512m -Xmx2g -XX:+UseG1GC -XX:+UseStringDeduplication"}
    
    echo -e "${GREEN}✅ 环境变量设置完成${NC}"
    echo -e "   Profile: $SPRING_PROFILES_ACTIVE"
    echo -e "   Port: $SERVER_PORT"
    echo -e "   Redis: $REDIS_HOST:$REDIS_PORT"
}

# 编译项目
build_project() {
    echo -e "${BLUE}🔨 编译项目...${NC}"
    
    if [ ! -f "pom.xml" ]; then
        echo -e "${RED}❌ 未找到pom.xml文件，请在项目根目录运行此脚本${NC}"
        exit 1
    fi
    
    ./mvnw clean package -DskipTests -Pprod
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 项目编译成功${NC}"
    else
        echo -e "${RED}❌ 项目编译失败${NC}"
        exit 1
    fi
}

# 启动应用
start_application() {
    echo -e "${BLUE}🚀 启动生产环境应用...${NC}"
    
    JAR_FILE=$(find target -name "*.jar" | grep -v original | head -n1)
    
    if [ -z "$JAR_FILE" ]; then
        echo -e "${RED}❌ 未找到编译后的JAR文件${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 找到JAR文件: $JAR_FILE${NC}"
    
    # 创建日志目录
    mkdir -p logs
    
    # 启动应用
    echo -e "${BLUE}🎯 启动命令: java $JAVA_OPTS -jar $JAR_FILE${NC}"
    echo -e "${YELLOW}📝 日志将输出到控制台和 logs/rtpos-server-prod.log${NC}"
    echo -e "${YELLOW}💡 使用 Ctrl+C 停止应用${NC}"
    echo ""
    
    java $JAVA_OPTS -jar "$JAR_FILE"
}

# 显示帮助信息
show_help() {
    echo "生产环境启动脚本使用说明："
    echo ""
    echo "环境变量配置："
    echo "  REDIS_HOST=*************     # Redis服务器地址"
    echo "  REDIS_PORT=6379              # Redis端口"
    echo "  REDIS_PASSWORD=your_password # Redis密码"
    echo "  DB_HOST=*************        # 数据库服务器地址"
    echo "  DB_USERNAME=rtpos_user       # 数据库用户名"
    echo "  DB_PASSWORD=your_db_password # 数据库密码"
    echo ""
    echo "使用示例："
    echo "  # 使用默认配置启动"
    echo "  ./start-prod.sh"
    echo ""
    echo "  # 使用自定义Redis配置启动"
    echo "  REDIS_HOST=************* REDIS_PASSWORD=mypassword ./start-prod.sh"
    echo ""
    echo "  # 使用环境变量文件启动"
    echo "  source .env && ./start-prod.sh"
}

# 主函数
main() {
    case "$1" in
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            check_java
            setup_environment
            check_redis
            build_project
            start_application
            ;;
    esac
}

# 捕获中断信号
trap 'echo -e "\n${YELLOW}🛑 应用正在停止...${NC}"; exit 0' INT TERM

# 执行主函数
main "$@"
