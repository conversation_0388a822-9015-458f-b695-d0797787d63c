#!/bin/bash

# POS门店API测试脚本

echo "=== POS门店API功能测试 ==="

BASE_URL="http://localhost:8081/api/v1"

echo ""
echo "1. 测试同步门店数据..."
curl -X POST "${BASE_URL}/stores/sync" \
  -H "Content-Type: application/json" \
  | jq '.'

echo ""
echo "2. 测试获取门店列表（前5条）..."
curl -X GET "${BASE_URL}/stores?page=0&size=5" \
  -H "Content-Type: application/json" \
  | jq '.'

echo ""
echo "3. 测试获取所有大区..."
curl -X GET "${BASE_URL}/stores/regions" \
  -H "Content-Type: application/json" \
  | jq '.'

echo ""
echo "4. 测试获取门店统计..."
curl -X GET "${BASE_URL}/stores/statistics" \
  -H "Content-Type: application/json" \
  | jq '.'

echo ""
echo "5. 测试搜索门店（关键词：店）..."
curl -X GET "${BASE_URL}/stores/search?keyword=店&size=3" \
  -H "Content-Type: application/json" \
  | jq '.'

echo ""
echo "=== 测试完成 ==="
