version: '3.8'

services:
  # 开发环境MySQL数据库 - 使用国内镜像源
  mysql-dev:
    image: registry.cn-hangzhou.aliyuncs.com/library/mysql:8.0
    container_name: rtpos-mysql-dev
    restart: unless-stopped
    ports:
      - "3306:3306"
    volumes:
      - mysql_dev_data:/var/lib/mysql
      - ./init-dev.sql:/docker-entrypoint-initdb.d/init-dev.sql
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: rtpos_dev
      MYSQL_USER: rtpos_dev
      MYSQL_PASSWORD: rtpos_dev123
    networks:
      - rtpos-dev-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-uroot", "-ppassword"]
      interval: 30s
      timeout: 10s
      retries: 5
    command: --default-authentication-plugin=mysql_native_password

  # 开发环境Redis缓存 - 使用国内镜像源
  redis-dev:
    image: registry.cn-hangzhou.aliyuncs.com/library/redis:7-alpine
    container_name: rtpos-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - rtpos-dev-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # phpMyAdmin - 数据库管理工具（使用DockerHub中国镜像）
  phpmyadmin:
    image: registry.cn-hangzhou.aliyuncs.com/library/phpmyadmin:latest
    container_name: rtpos-phpmyadmin-dev
    restart: unless-stopped
    ports:
      - "8083:80"
    environment:
      PMA_HOST: mysql-dev
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: password
      MYSQL_ROOT_PASSWORD: password
    depends_on:
      - mysql-dev
    networks:
      - rtpos-dev-network

volumes:
  mysql_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  rtpos-dev-network:
    driver: bridge
