# POS门店状态API使用指南

## 概述

本文档介绍POS门店状态API的使用方法，该API用于获取指定门店下的在线、离线、上下机信息。

## API接口信息

### 基本信息
- **功能**: 获取门店下的在线、离线、上下机信息
- **特点**: 直接转发外部API响应，不保存到数据库
- **开发环境外部API**: `http://**************:8080/api/pos/getPosByStore`
- **生产环境外部API**: `http://rt-pos-api.idc1.fn/api/pos/getPosByStore`

### 接口端点

#### 1. POST方式查询（推荐）
```
POST /api/v1/api/pos/store-status/query
Content-Type: application/json
```

**请求体**:
```json
{
    "storeNo": 1001
}
```

#### 2. GET方式查询
```
GET /api/v1/api/pos/store-status/query/{storeNo}
```

## 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| storeNo | Integer | 是 | 门店编号 | 1001 |

## 响应格式

### 成功响应
```json
{
  "rsCode": "00000000",
  "msg": "Success",
  "body": {
    "allPos": 105,
    "allOnlPosCount": 12000,
    "alloffPosCount": 8999,
    "online": [14, 144, 213, 228, 266, 285, 342, 389, 666, 859],
    "offline": [1, 19, 21, 87, 995, 996, 997, 998, 999],
    "login": [144, 213, 285, 389, 859, 996],
    "logout": [1, 14, 19, 21, 87, 112, 123, 999]
  },
  "traceId": null
}
```

### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| rsCode | String | 响应码，"00000000"表示成功 |
| msg | String | 响应消息 |
| body | Object | 响应数据体 |
| body.allPos | Integer | 总设备数量 |
| body.allOnlPosCount | Integer | 所有门店在线机台数 |
| body.alloffPosCount | Integer | 所有门店离线机台数 |
| body.online | List<Integer> | 在线机台集合，如果没有返回空集合 |
| body.offline | List<Integer> | 离线机台集合，如果没有返回空集合 |
| body.login | List<Integer> | 上机状态机台集合，如果没有返回空集合 |
| body.logout | List<Integer> | 下机状态机台集合，如果没有返回空集合 |
| traceId | String | 追踪ID |

### 错误响应
```json
{
  "rsCode": "99999999",
  "msg": "查询门店状态失败: 具体错误信息",
  "body": null,
  "traceId": null
}
```

## 使用示例

### cURL示例

#### POST方式
```bash
curl -X POST \
  http://localhost:8081/api/v1/api/pos/store-status/query \
  -H 'Content-Type: application/json' \
  -d '{
    "storeNo": 1001
  }'
```

#### GET方式
```bash
curl -X GET \
  http://localhost:8081/api/v1/api/pos/store-status/query/1001
```

### JavaScript示例
```javascript
// POST方式
const response = await fetch('/api/v1/api/pos/store-status/query', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    storeNo: 1001
  })
});

const data = await response.json();
console.log('门店状态:', data);
```

### Java示例
```java
@Autowired
private PosStoreStatusService storeStatusService;

public void queryStoreStatus() {
    PosStoreStatusRequest request = new PosStoreStatusRequest();
    request.setStoreNo(1001);
    
    PosStoreStatusResponse response = storeStatusService.getStoreStatus(request);
    
    if (response.isSuccess()) {
        PosStoreStatusBody body = response.getBody();
        System.out.println("总设备数量: " + body.getAllPos());
        System.out.println("在线机台: " + body.getOnline());
        System.out.println("离线机台: " + body.getOffline());
        System.out.println("上机状态: " + body.getLogin());
        System.out.println("下机状态: " + body.getLogout());
    } else {
        System.err.println("查询失败: " + response.getMsg());
    }
}
```

## 测试

### 使用测试脚本
项目提供了测试脚本 `test-store-status-api.sh`：

```bash
# 使用默认配置测试
./test-store-status-api.sh

# 测试指定门店
./test-store-status-api.sh -s 1002

# 使用不同的URL
./test-store-status-api.sh -u http://localhost:8080/api/v1
```

### Swagger UI
启动应用后，可以通过Swagger UI进行测试：
- 开发环境: http://localhost:8081/api/v1/swagger-ui.html
- 查找 "POS门店状态管理" 标签下的接口

## 配置说明

### 开发环境配置 (application-dev.yml)
```yaml
pos:
  api:
    store-status-base-url: http://**************:8080
    store-status-path: /api/pos/getPosByStore
```

### 生产环境配置 (application-prod.yml)
```yaml
pos:
  api:
    store-status-base-url: http://rt-pos-api.idc1.fn
    store-status-path: /api/pos/getPosByStore
```

## 注意事项

1. **认证**: 接口无需认证，已在安全配置中设置为公开访问
2. **网络**: 确保应用服务器能够访问外部API地址
3. **超时**: 外部API调用超时时间为60秒
4. **错误处理**: 接口会将外部API的错误原样返回
5. **日志**: 所有请求和响应都会记录到应用日志中
6. **性能**: 该接口为实时查询，不使用缓存

## 故障排除

### 常见问题

1. **连接超时**
   - 检查网络连接
   - 确认外部API地址是否正确
   - 检查防火墙设置

2. **接口访问失败**
   - 确认接口路径是否正确
   - 检查应用是否正常启动

3. **门店不存在**
   - 确认门店编号是否正确
   - 检查门店是否在外部系统中存在

4. **响应格式错误**
   - 检查外部API是否正常
   - 确认API版本是否匹配

### 日志查看
```bash
# 查看应用日志
tail -f logs/rtpos-server.log

# 过滤门店状态相关日志
grep "store status" logs/rtpos-server.log
```
