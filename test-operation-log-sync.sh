#!/bin/bash

# POS收银操作日志同步功能测试脚本
# 用于验证收银操作日志同步功能是否正常工作

echo "=== POS收银操作日志同步功能测试 ==="

# 配置
BASE_URL="http://localhost:8081/api/v1"
API_PREFIX="/api/pos-operation-logs"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo -e "\n${YELLOW}测试: $description${NC}"
    echo "请求: $method $BASE_URL$API_PREFIX$endpoint"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\n%{http_code}" "$BASE_URL$API_PREFIX$endpoint")
    else
        if [ -n "$data" ]; then
            response=$(curl -s -w "\n%{http_code}" -X "$method" \
                -H "Content-Type: application/json" \
                -d "$data" \
                "$BASE_URL$API_PREFIX$endpoint")
        else
            response=$(curl -s -w "\n%{http_code}" -X "$method" \
                "$BASE_URL$API_PREFIX$endpoint")
        fi
    fi
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" -eq 200 ]; then
        echo -e "${GREEN}✓ 成功 (HTTP $http_code)${NC}"
        echo "响应: $(echo "$body" | head -c 200)..."
    else
        echo -e "${RED}✗ 失败 (HTTP $http_code)${NC}"
        echo "错误: $body"
    fi
}

# 等待应用启动
echo "等待应用启动..."
for i in {1..30}; do
    if curl -s "$BASE_URL/actuator/health" > /dev/null 2>&1; then
        echo -e "${GREEN}应用已启动${NC}"
        break
    fi
    if [ $i -eq 30 ]; then
        echo -e "${RED}应用启动超时，请检查应用是否正常运行${NC}"
        exit 1
    fi
    sleep 2
done

# 测试用例

# 1. 测试查询操作日志
test_api "GET" "/query?page=0&size=5" "" "查询操作日志列表"

# 2. 测试增量同步
test_api "POST" "/sync/incremental" "" "增量同步操作日志"

# 3. 测试指定门店和POS机同步
start_time=$(($(date +%s) * 1000 - 3600000))  # 1小时前
end_time=$(($(date +%s) * 1000))               # 现在
test_api "POST" "/sync/store/1001/pos/895?startTime=$start_time&endTime=$end_time" "" "同步指定门店和POS机的操作日志"

# 4. 测试批量同步
test_api "POST" "/sync/batch?storeNos=1001,1002&posNos=895,896&startTime=$start_time&endTime=$end_time" "" "批量同步操作日志"

# 5. 测试根据订单ID查询
test_api "GET" "/order/810018951749519777" "" "根据订单ID查询操作日志"

# 6. 测试根据班次号查询
test_api "GET" "/shift/250610094215" "" "根据班次号查询操作日志"

# 7. 测试根据流水ID查询
test_api "GET" "/flow/5asa710018951749519738154" "" "根据流水ID查询操作日志"

# 8. 测试直接API同步
sync_request='{
    "storeNo": 1001,
    "posNo": 895,
    "beginTime": '$start_time',
    "endTime": '$end_time',
    "limit": 20,
    "page": 1
}'
test_api "POST" "/sync" "$sync_request" "直接API同步操作日志"

echo -e "\n=== 测试完成 ==="

# 显示数据库中的操作日志数量
echo -e "\n${YELLOW}检查数据库中的操作日志数量...${NC}"
if command -v mysql &> /dev/null; then
    # 如果有MySQL客户端，尝试连接并查询
    echo "尝试连接数据库查询操作日志数量..."
    # 这里需要根据实际的数据库配置调整连接参数
    # mysql -h localhost -u root -p -e "USE rtpos_db; SELECT COUNT(*) as total FROM pos_operation_logs;"
else
    echo "MySQL客户端未安装，跳过数据库查询"
fi

echo -e "\n${GREEN}测试脚本执行完成！${NC}"
echo "请检查上述测试结果，确保所有功能正常工作。"
echo ""
echo "如需查看详细日志，请检查应用日志文件。"
echo "如需查看Swagger文档，请访问: $BASE_URL/swagger-ui.html"
