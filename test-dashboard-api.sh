#!/bin/bash

# Dashboard API测试脚本
# 测试新创建的Dashboard整合API接口

echo "======================================"
echo "Dashboard API 测试脚本"
echo "======================================"

# 配置
BASE_URL="http://localhost:8081/api/v1"
DASHBOARD_URL="$BASE_URL/pos/dashboard"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local test_name="$1"
    local url="$2"
    local method="$3"
    local data="$4"
    
    echo -e "\n${YELLOW}测试: $test_name${NC}"
    echo "URL: $url"
    echo "Method: $method"
    if [ -n "$data" ]; then
        echo "Data: $data"
    fi
    echo "----------------------------------------"
    
    if [ "$method" = "POST" ]; then
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
            -X POST \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$url")
    else
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
            -X GET \
            "$url")
    fi
    
    # 分离响应体和状态码
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    response_body=$(echo "$response" | sed '/HTTP_CODE:/d')
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✓ 请求成功 (HTTP $http_code)${NC}"
        echo "响应内容:"
        echo "$response_body" | python3 -m json.tool 2>/dev/null || echo "$response_body"
    else
        echo -e "${RED}✗ 请求失败 (HTTP $http_code)${NC}"
        echo "响应内容:"
        echo "$response_body"
    fi
}

# 检查服务是否运行
echo "检查服务状态..."
health_response=$(curl -s "$BASE_URL/actuator/health" 2>/dev/null)
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 服务正在运行${NC}"
else
    echo -e "${RED}✗ 服务未运行，请先启动应用${NC}"
    echo "启动命令: ./mvnw spring-boot:run -Dspring-boot.run.profiles=dev"
    exit 1
fi

# 测试1: 获取Dashboard数据 - 基本请求
test_api "获取Dashboard数据 - 基本请求" \
    "$DASHBOARD_URL/data" \
    "POST" \
    '{"storeId": "1001"}'

# 测试2: 获取Dashboard数据 - 指定日期
test_api "获取Dashboard数据 - 指定日期" \
    "$DASHBOARD_URL/data" \
    "POST" \
    '{"storeId": "1001", "date": "2024-06-27"}'

# 测试3: 获取Dashboard数据 - 指定时间范围
test_api "获取Dashboard数据 - 指定时间范围" \
    "$DASHBOARD_URL/data" \
    "POST" \
    '{"storeId": "1001", "date": "2024-06-27", "timeRange": "today"}'

# 测试4: 获取Dashboard数据 - 本周数据
test_api "获取Dashboard数据 - 本周数据" \
    "$DASHBOARD_URL/data" \
    "POST" \
    '{"storeId": "1001", "timeRange": "week"}'

# 测试5: 获取Dashboard数据 - 本月数据
test_api "获取Dashboard数据 - 本月数据" \
    "$DASHBOARD_URL/data" \
    "POST" \
    '{"storeId": "1001", "timeRange": "month"}'

# 测试6: 错误测试 - 缺少storeId
test_api "错误测试 - 缺少storeId" \
    "$DASHBOARD_URL/data" \
    "POST" \
    '{}'

# 测试7: 错误测试 - 空storeId
test_api "错误测试 - 空storeId" \
    "$DASHBOARD_URL/data" \
    "POST" \
    '{"storeId": ""}'

# 测试8: 错误测试 - 无效日期格式
test_api "错误测试 - 无效日期格式" \
    "$DASHBOARD_URL/data" \
    "POST" \
    '{"storeId": "1001", "date": "invalid-date"}'

echo -e "\n======================================"
echo -e "${GREEN}Dashboard API 测试完成${NC}"
echo "======================================"

# 显示API文档链接
echo -e "\n${YELLOW}相关链接:${NC}"
echo "API文档: http://localhost:8081/api/v1/swagger-ui.html"
echo "H2控制台: http://localhost:8081/api/v1/h2-console"
echo "健康检查: http://localhost:8081/api/v1/actuator/health"
